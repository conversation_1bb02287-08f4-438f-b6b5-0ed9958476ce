{"name": "app", "version": "0.1.0", "scripts": {"ng": "ng", "start:storybook": "ng run storybook:serve", "prebuild": "rimraf ./dist", "build": "npm-run-all --parallel build:*", "build:lib": "ng build lib --configuration production && npm run build-assets", "_build:storybook": "storybook build -o ./dist/storybook", "build-assets": "npm-run-all --parallel build-assets:*", "build-assets:fonts-copy": "cpx \"./projects/lib/src/styles/fonts/**/*.{eot,svg,ttf,woff,woff2}\" ./dist/lib/styles/fonts/", "build-assets:fonts-scss": "scss-bundle -c ./projects/lib/config/scss-bundle/fonts.config.json", "build-assets:scss-common-theme": "sass projects/lib/src/styles/themes/hub-common-theme.scss dist/lib/styles/themes/_theme.scss --load-path=node_modules", "test": "npm-run-all --parallel test:*", "test:lib": "ng test lib", "test:file": "ng test lib", "lint": "npm-run-all --parallel lint:*", "lint:lib": "ng lint lib", "pack": "npm run build:lib && cd ./dist/lib && npm pack && cd ../.."}, "private": true, "dependencies": {"@angular/animations": "19.2.14", "@angular/cdk": "^19.2.19", "@angular/common": "19.2.14", "@angular/compiler": "19.2.14", "@angular/core": "19.2.14", "@angular/forms": "19.2.14", "@angular/material": "^19.2.19", "@angular/platform-browser": "19.2.14", "@angular/platform-browser-dynamic": "19.2.14", "@angular/router": "19.2.14", "@auth0/angular-jwt": "^5.2.0", "@storybook/addon-actions": "^9.0.8", "dexie": "3.0.2", "rxjs": "^7.8.2", "tslib": "2.8.1", "zone.js": "0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.15", "@angular-devkit/core": "^20.1.6", "@angular/cli": "19.2.15", "@angular/compiler-cli": "19.2.14", "@angular/language-service": "19.2.14", "@babel/core": "7.12.9", "@ngneat/spectator": "6.1.2", "@ngx-translate/core": "^16.0.4", "@storybook/angular": "9.1.2", "@storybook/builder-webpack5": "9.1.2", "@types/crypto-js": "4.0.1", "@types/jasmine": "3.6.2", "@types/jasminewd2": "2.0.8", "@types/lodash": "4.14.119", "@types/node": "12.0.8", "@types/tapable": "2.2.2", "@typescript-eslint/eslint-plugin": "^8.39.1", "@typescript-eslint/parser": "^8.39.1", "angular-eslint": "20.1.1", "autoprefixer": "10.0.4", "babel-loader": "8.2.2", "cpx": "1.5.0", "crypto-js": "4.0.0", "eslint": "^8.57.1", "eslint-plugin-storybook": "9.1.2", "jasmine-core": "3.6.0", "jasmine-spec-reporter": "6.0.0", "karma": "6.3.4", "karma-chrome-launcher": "3.1.0", "karma-coverage-istanbul-reporter": "3.0.3", "karma-jasmine": "4.0.1", "karma-jasmine-html-reporter": "1.5.4", "moment": "2.29.1", "moment-timezone": "0.5.32", "ng-packagr": "19.2.2", "ngx-color-picker": "^16.0.0", "npm-run-all": "4.1.5", "rimraf": "3.0.2", "sass": "^1.41.0", "scss-bundle": "^3.1.2", "storybook": "^9.1.2", "ts-node": "9.0.0", "typescript": "5.8.3", "typescript-eslint": "8.34.1"}, "browserslist": [">0.2%", "not dead", "not ie <= 9"]}
pipeline {
  agent any
  options {
    disableConcurrentBuilds()
    timestamps()
    timeout(time: 1, unit: 'HOURS')
  }
  environment {
    REVISION = sh(returnStdout: true, script: 'git rev-parse HEAD').trim()
    BRANCH_NAME_NORMALIZED = "${BRANCH_NAME.toLowerCase().replace("/", "_")}"
    REPO = "skywindgroup"
    SERVICE = "sw-ubo-library-common"
    REGION = "asia.gcr.io"
    PROJECT = "gcpstg"
    NODE = "jod"
  }

  stages {
    stage('NPM install') {
      when {
        anyOf {
          branch 'release/*'
          expression { env.BRANCH_NAME.startsWith('PR-') }
        }
      }
      steps {
        withDockerContainer(image: "node:${NODE}", toolName: 'latest') {
          configFileProvider([configFile(fileId: 'npm-npmjs', targetLocation: '.npmrc')]) {
            sh 'npm ci'
          }
        }
      }
    }
    stage('Lint') {
      when {
        expression { env.BRANCH_NAME.startsWith('PR-') }
      }
      steps {
        withDockerContainer(image: "node:${NODE}", toolName: 'latest') {
          configFileProvider([configFile(fileId: 'npm-npmjs', targetLocation: '.npmrc')]) {
            sh 'npm run lint'
          }
        }
      }
    }
    stage('Test/unit') {
      when {
        expression { env.BRANCH_NAME.startsWith('PR-') }
      }
      steps {
        withDockerContainer(image: "cimg/node:22.9-browsers", toolName: 'latest') {
          configFileProvider([configFile(fileId: 'npm-npmjs', targetLocation: '.npmrc')]) {
            sh 'npm run test:lib --  --no-watch --no-progress --code-coverage'
          }
        }
      }
    }
    stage('Build') {
      when {
        expression { env.BRANCH_NAME.startsWith('PR-') }
      }
      steps {
        withDockerContainer(image: "node:${NODE}", toolName: 'latest') {
          configFileProvider([configFile(fileId: 'npm-npmjs', targetLocation: '.npmrc')]) {
            sh 'npm run build:lib'
          }
        }
      }
    }
//     stage('Docker/build') {
//       when {
//         branch 'release/*'
//       }
//       steps {
//         withDockerContainer(image: "node:${NODE}", toolName: 'latest') {
//           configFileProvider([configFile(fileId: 'npm-npmjs', targetLocation: '.npmrc')]) {
//             sh 'npx rimraf ./dist'
//             sh 'npm run build:storybook'
//           }
//         }
//         sh "docker build --no-cache -f Dockerfile -t ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED} ."
//       }
//     }
//     stage('Docker/tag') {
//       when {
//         branch 'release/*'
//       }
//       steps {
//         sh 'docker tag ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED} ${REGION}/${PROJECT}/${SERVICE}:${BRANCH_NAME_NORMALIZED}'
//         sh 'docker tag ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED} ${REGION}/${PROJECT}/${SERVICE}:${REVISION}'
//       }
//     }
//     stage('Docker/push') {
//       when {
//         branch 'release/*'
//       }
//       steps {
//         sh 'docker push ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED}'
//         sh 'docker push ${REGION}/${PROJECT}/${SERVICE}:${BRANCH_NAME_NORMALIZED}'
//         sh 'docker push ${REGION}/${PROJECT}/${SERVICE}:${REVISION}'
//       }
//     }
    stage('Publish') {
      when {
        branch 'release/*'
      }
      steps {
       sh '''
       git config user.name cmbuilder
       git config user.email <EMAIL>
       git fetch --tags
       '''
        withDockerContainer(image: "node:${NODE}", toolName: 'latest') {
          sh 'npm config set git-tag-version false'
          dir('projects/lib') {
            sh 'npm version $(git describe --abbrev=0 --tags)'
            sh 'npm version patch'
            script {
              env.VERSION = readJSON(file: 'package.json').version
            }
          }
          sh 'npm run build:lib'
          dir('dist/lib') {
            configFileProvider([configFile(fileId: 'npmjs-publish', targetLocation: '.npmrc')]) {
              sh 'npm publish'
            }
            configFileProvider([configFile(fileId: 'jfrog-publish', targetLocation: '.npmrc')]) {
              sh 'npm publish'
            }
          }
        }
        sh '''
        git tag ${VERSION} -m "Bumped to ${VERSION}"
        git push origin tag $(git describe --abbrev=0 --tags)
        '''
      }
    }
  }
}

<style>
  .sidebar-container {
    background-color: #333758;
  }

  .sidebar-container section > a > .sidebar-item > .sidebar-expander {
    border-left-color: #a9b0c0 !important;
  }

  .sidebar-item {
    padding-top: 8px !important;
    padding-bottom: 8px !important;
    font-weight: 400;
  }
  .sidebar-item,
  .sidebar-item svg {
    color: #a9b0c0 !important;
  }

  .sidebar-item.selected {
    color: #fff !important;
    background-color: #2a2c44 !important;
  }

  .sidebar-header + div > form {
    padding: 4px 6px;
    border: 1px solid #626781;
    border-radius: 4px;
    background-color: rgb(41, 43, 67);
    color: #fafafa;
  }

  .sidebar-header + div > form svg,
  .sidebar-header + div > form input{
    color: #fafafa;
  }

  .sidebar-header + div > form input::-webkit-input-placeholder { /* Edge */
    color: #626781;
  }

  .sidebar-header + div > form input:-ms-input-placeholder { /* Internet Explorer 10-11 */
    color: #626781;
  }

  .sidebar-header + div > form input::placeholder {
    color: #626781;
  }

  .sidebar-header + div > form + div {
    color: #fafafa;
  }

  .sidebar-header button {
    color: rgba(255, 255, 255, .5);
    box-shadow: rgba(255, 255, 255, .5) 0 0 0 1px inset;
  }

  .sidebar-header button:hover {
    color: #fff;
    box-shadow: #fff 0 0 0 1px inset;
  }

  #storybook-panel-root {
    background-color: #fafafa;
  }

</style>
<link rel="stylesheet" href="">

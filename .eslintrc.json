{"root": true, "ignorePatterns": ["projects/**/*"], "overrides": [{"files": ["*.ts"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "@angular-eslint/eslint-plugin"], "parserOptions": {"project": ["tsconfig.json"]}, "rules": {"@angular-eslint/component-class-suffix": "error", "@angular-eslint/directive-class-suffix": "error", "@angular-eslint/no-conflicting-lifecycle": "error", "@angular-eslint/use-lifecycle-interface": "error", "@typescript-eslint/no-unused-expressions": "error", "@typescript-eslint/no-use-before-define": "error", "no-console": ["error", {"allow": ["log", "warn", "error"]}], "no-debugger": "error", "no-var": "error", "quotes": ["error", "single"], "semi": ["error", "always"]}}, {"files": ["*.html"], "parser": "@angular-eslint/template-parser", "plugins": ["@angular-eslint/eslint-plugin-template"], "rules": {"@angular-eslint/template/banana-in-box": "error"}}], "extends": ["plugin:storybook/recommended"]}
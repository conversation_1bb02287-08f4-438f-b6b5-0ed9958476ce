{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"lib": {"projectType": "library", "root": "projects/lib", "sourceRoot": "projects/lib/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"tsConfig": "projects/lib/tsconfig.lib.json", "project": "projects/lib/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/lib/tsconfig.lib.prod.json"}}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/lib/src/test.ts", "tsConfig": "projects/lib/tsconfig.spec.json", "karmaConfig": "projects/lib/karma.conf.js"}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["projects/lib/**/*.ts", "projects/lib/**/*.html"]}}}}, "storybook": {"projectType": "application", "root": "", "sourceRoot": "", "architect": {"build": {"builder": "@storybook/angular:build-storybook", "options": {"outputDir": "dist/storybook", "configDir": ".storybook", "browserTarget": "lib:build", "compodoc": false}}, "serve": {"builder": "@storybook/angular:start-storybook", "options": {"port": 6006, "configDir": ".storybook", "browserTarget": "lib:build", "compodoc": false}}}}}, "cli": {"analytics": false}}
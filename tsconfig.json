{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "skipLibCheck": true, "esModuleInterop": true, "sourceMap": true, "declaration": false, "experimentalDecorators": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "module": "esnext", "moduleResolution": "node", "importHelpers": true, "target": "ES2022", "typeRoots": ["node_modules/@types"], "lib": ["es2018", "dom"], "paths": {"lib": ["dist/lib"], "lib/*": ["dist/lib/*"]}, "useDefineForClassFields": false}}
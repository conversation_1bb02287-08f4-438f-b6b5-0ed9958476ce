import type { <PERSON>a, StoryObj } from '@storybook/angular';
import { SwuiCheckboxComponent } from './swui-checkbox.component';

const meta: Meta<SwuiCheckboxComponent> = {
  title: 'Forms/Checkbox',
  component: SwuiCheckboxComponent,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    ngModelChange: { action: 'ngModelChange' },
  },
};

export default meta;
type Story = StoryObj<SwuiCheckboxComponent>;

export const Default: Story = {
  args: {},
  render: (args) => ({
    props: {
      ...args,
      ngModel: true,
    },
    template: `<lib-swui-checkbox [ngModel]="ngModel" (ngModelChange)="ngModelChange($event)"></lib-swui-checkbox>`,
  }),
};

import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SwuiCheckboxComponent } from './swui-checkbox.component';

describe('SwuiCheckboxComponent', () => {
  let component: SwuiCheckboxComponent;
  let fixture: ComponentFixture<SwuiCheckboxComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [SwuiCheckboxComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiCheckboxComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

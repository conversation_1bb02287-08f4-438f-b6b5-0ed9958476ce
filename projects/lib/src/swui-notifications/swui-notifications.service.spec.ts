import { TestBed } from '@angular/core/testing';

import { SwuiNotificationsService } from './swui-notifications.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Overlay } from '@angular/cdk/overlay';

describe('SwuiNotificationsService', () => {

  let service: SwuiNotificationsService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        MatSnackBar,
        Overlay,
        SwuiNotificationsService,
      ],
    });

    service = TestBed.inject(SwuiNotificationsService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('showSnackbar should be called after call success method', () => {
    spyOn(service, 'showSnackbar');
    service.success('success');
    expect(service.showSnackbar).toHaveBeenCalled();
  });

  it('showSnackbar should be called after call error method', () => {
    spyOn(service, 'showSnackbar');
    service.error('error');
    expect(service.showSnackbar).toHaveBeenCalled();
  });

  it('showSnackbar should be called after call warning method', () => {
    spyOn(service, 'showSnackbar');
    service.warning('warning');
    expect(service.showSnackbar).toHaveBeenCalled();
  });
});

import { moduleMetadata, storiesOf } from '@storybook/angular';

import { SwuiNotificationsTemplateComponent } from './swui-notifications-template.component';
import { SwuiNotificationsTemplateModule } from './swui-notifications-template.module';

storiesOf('Notifications', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        SwuiNotificationsTemplateModule,
      ],
    })
  )
  .add('Different types of notifications', () => ({
    component: SwuiNotificationsTemplateComponent,
  }));

import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { MAT_SNACK_BAR_DATA, MatSnackBarRef } from '@angular/material/snack-bar';
import { NO_ERRORS_SCHEMA } from '@angular/core';

import { notificationsMatModules } from '../swui-notifications.module';
import { SwuiSnackbarComponent } from './swui-snackbar.component';

describe('SwuiSnackbarComponent', () => {
  let component: SwuiSnackbarComponent;
  let fixture: ComponentFixture<SwuiSnackbarComponent>;

  beforeEach(waitForAsync(() => {
    const matSnackBarRef = jasmine.createSpyObj('MatSnackBarRef', ['dismiss']);
    TestBed.configureTestingModule({
      imports: [
        ...notificationsMatModules,
      ],
      declarations: [SwuiSnackbarComponent],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        { provide: MatSnackBarRef, useValue: matSnackBarRef },
        { provide: MAT_SNACK_BAR_DATA, useValue: {} },
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiSnackbarComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

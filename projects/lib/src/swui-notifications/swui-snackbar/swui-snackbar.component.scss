.sw-snackbar {
  display: flex;
  align-items: center;
  color: #fff;
  &__icon {
    display: none;
    padding-right: 13px;
    mat-icon {
      display: block;
      width: 22px;
      height: 20px;
      line-height: 20px;
      font-size: 22px;
      overflow: hidden;
    }
    svg {
      display: block;
      width: 20px;
      height: 20px;
      path {
        fill: #fff;
      }
    }
  }
  &__content {
    padding-right: 40px;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 0.25px;
  }
  &__title {
    font-weight: 700;
  }
  &__button {
    position: absolute;
    right: 10px;
    top: 50%;
    height: 36px;
    width: 36px;
    line-height: 36px;
    margin-top: -18px;
    mat-icon {
      margin: auto;
      display: block;
      height: 14px;
      line-height: 14px;
    }
  }
}

:host-context(.swui-snackbar-error) {
  .sw-snackbar {
    &__icon {
      &--error {
        display: block;
      }
    }
  }
}

:host-context(.swui-snackbar-success) {
  .sw-snackbar {
    &__icon {
      &--success {
        display: block;
      }
    }
  }
}

import { Injectable } from '@angular/core';
import { MatSnackBar, MatSnackBarConfig, MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition } from '@angular/material/snack-bar';

import { SwuiSnackbarComponent } from './swui-snackbar/swui-snackbar.component';

export interface NotificationsMessage {
  message: string;
  title?: string;
  verticalPosition?: MatSnackBarVerticalPosition;
  horizontalPosition?: MatSnackBarHorizontalPosition;
  duration?: number;
}

const DEFAULT_DURATION = 5000;
const DEFAULT_VERTICAL_POSITION = 'bottom';
const DEFAULT_HORIZONTAL_POSITION = 'left';
const DEFAULT_ERROR_MESSAGE = 'Oops, something went wrong. Please try again later.';

@Injectable({
  providedIn: 'root'
})
export class SwuiNotificationsService {

  constructor(
    private snackbar: MatSnackBar,
  ) {
  }

  success( message: string, title?: string, verticalPosition?: MatSnackBarVerticalPosition,
           horizontalPosition?: MatSnackBarHorizontalPosition, duration?: number
  ) {
    const data = { message, title, verticalPosition, horizontalPosition, duration };
    return this.showSnackbar(data, 'swui-snackbar-success');
  }

  error( message: string, title?: string, verticalPosition?: MatSnackBarVerticalPosition,
         horizontalPosition?: MatSnackBarHorizontalPosition, duration?: number
  ) {
    const data = {
      message: message || DEFAULT_ERROR_MESSAGE,
      title,
      verticalPosition,
      horizontalPosition,
      duration
    };
    return this.showSnackbar(data, 'swui-snackbar-error');
  }

  warning( message: string, title?: string, verticalPosition?: MatSnackBarVerticalPosition,
           horizontalPosition?: MatSnackBarHorizontalPosition, duration?: number
  ) {
    const data = { message, title, verticalPosition, horizontalPosition, duration };
    return this.showSnackbar(data, 'swui-snackbar-warning');
  }

  showSnackbar( data: NotificationsMessage, panelClass = 'swui-snackbar-success' ) {
    const config: MatSnackBarConfig = {
      data,
      panelClass,
      duration: data.duration || DEFAULT_DURATION,
      verticalPosition: data.verticalPosition || DEFAULT_VERTICAL_POSITION,
      horizontalPosition: data.horizontalPosition || DEFAULT_HORIZONTAL_POSITION,
    };

    return this.snackbar.openFromComponent(SwuiSnackbarComponent, config);
  }
}

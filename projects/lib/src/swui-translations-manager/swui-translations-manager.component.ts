import {
  AfterViewChecked,
  Component,
  EventEmitter,
  forwardRef,
  Input,
  OnDestroy,
  OnInit,
  Optional,
  Output,
  ViewChild
} from '@angular/core';
import {
  AbstractControl,
  ControlValueAccessor,
  UntypedFormArray,
  UntypedFormControl,
  UntypedFormGroup,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ValidationErrors,
  Validator
} from '@angular/forms';
import { MatTabGroup } from '@angular/material/tabs';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { SwuiSelectOption } from '../swui-select/swui-select.interface';
import { SwuiTranslationsManagerService } from './swui-translations-manager.service';

interface FormValues {
  [lang: string]: any;
}

export interface SwuiTranslationsManagerChild {
  form: UntypedFormGroup;

  setValue( val?: FormValues ): void;

  initForm( val?: FormValues ): UntypedFormGroup;
}

@Component({
    selector: 'lib-swui-translations-manager',
    templateUrl: './swui-translations-manager.component.html',
    styleUrls: ['./swui-translations-manager.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => SwuiTranslationsManagerComponent),
            multi: true
        },
        {
            provide: NG_VALIDATORS,
            useExisting: forwardRef(() => SwuiTranslationsManagerComponent),
            multi: true
        }
    ],
    standalone: false
})
export class SwuiTranslationsManagerComponent implements ControlValueAccessor, Validator, OnInit, OnDestroy, AfterViewChecked {
  @Input()
  set languages( val: SwuiSelectOption[] | undefined ) {
    this._languages = Array.isArray(val) ? val : [];
    this.availableLanguages = this._languages;
  }

  get languages(): SwuiSelectOption[] {
    return this._languages.sort(( a, b ) => {
      return this.translateService.instant(a.text).localeCompare(this.translateService.instant(b.text));
    });
  }

  @Input('component')
  set childComponent( value: SwuiTranslationsManagerChild | undefined ) {
    this._childFormComponent = value;
  }

  get childComponent(): SwuiTranslationsManagerChild | undefined {
    return this._childFormComponent;
  }

  @Input() set submitted( val: boolean | undefined ) {
    this._submitted = val ?? false;
    if (this._submitted) {
      this.switchToFirstInvalidTab();
    }
  }

  get submitted(): boolean | undefined {
    return this._submitted;
  }

  @Input()
  set isDisabled( val: boolean | undefined ) {
    this._isDisabled = val ?? false;
    this.isRemoveLanguageDisabled = this._isDisabled;
  }

  get isDisabled(): boolean {
    return this._isDisabled;
  }

  @Input() withoutDefault = false;
  @Input() isRemoveLanguageDisabled = false;
  @Input() isOrderingSupported = true;

  @Output() addLang = new EventEmitter<AbstractControl>();

  @ViewChild('tabSet', { static: true }) readonly tabsRef?: MatTabGroup;
  readonly form: UntypedFormGroup;

  selectedTabIndex = -1;
  availableLanguages: SwuiSelectOption[] = [];

  private _submitted = false;
  private _isDisabled = false;
  private _childFormComponent?: any;
  private _languages: SwuiSelectOption[] = [];

  private _onChange: ( _: any ) => void = (() => {
  });

  private readonly destroyed$ = new Subject<void>();

  constructor( private readonly translateService: TranslateService,
               @Optional() private readonly translationManagerService: SwuiTranslationsManagerService ) {
    this.form = new UntypedFormGroup({
      info: new UntypedFormArray([])
    });
  }

  ngOnInit(): void {
    this.form.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(value => {
      if (this._onChange) {
        this._onChange(this.transformForm(value));
      }
      this.form.markAllAsTouched();
      this.onTouched();
    });

    this.listenLanguages();

    if (!this.withoutDefault) {
      this.infoArray.push(this.initLangGroup({ id: 'en' }), { emitEvent: false });
      this.availableLanguages = this.availableLanguages.filter(( { id } ) => id !== 'en');
      this.selectedTabIndex = this.controls.length - 1;
      this.childComponent?.setValue({ id: 'en' });
    }

    const child = this.childComponent;
    if (child) {
      child.form.valueChanges.pipe(
        filter(val => !!val),
        takeUntil(this.destroyed$)
      ).subscribe(( val: FormValues ) => {
        const childControl = this.controls.find(( control ) => control.get('id')?.value === val.id);
        if (childControl) {
          childControl.patchValue(val);
        }
      });

      child.form.statusChanges.pipe(
        takeUntil(this.destroyed$)
      ).subscribe(() => {
        if (this.selectedTabIndex !== -1 && this.controls.length && this.controls[this.selectedTabIndex]) {
          const errors = child.form.invalid ? { invalid: true } : null;
          this.controls[this.selectedTabIndex].setErrors(errors);
        }
        if (child.form.touched) {
          this.onTouched();
          if (this.selectedTabIndex !== -1 && this.controls.length && this.controls[this.selectedTabIndex]) {
            this.controls[this.selectedTabIndex].markAsTouched();
          }
        }
      });
    }
  }

  ngOnDestroy(): void {
    this.destroyed$.next(undefined);
    this.destroyed$.complete();
  }

  ngAfterViewChecked(): void {
    if (this.tabsRef) {
      this.tabsRef.realignInkBar();
    }
  }

  onTouched: any = () => {
  };

  get controls(): UntypedFormGroup[] {
    return this.infoArray.controls as UntypedFormGroup[];
  }

  writeValue( value: { [lang: string]: any } | undefined ): void {
    if (typeof value === 'undefined' || value === null || (value && !Object.keys(value).length)) {
      return;
    }

    const processedValue = Object.entries(value).map(( [id, item] ) => ({ ...item, id }));
    if (this.isOrderingSupported) {
      processedValue.sort(( a, b ) => a.order - b.order);
    }

    this.availableLanguages = this._languages;
    this.infoArray.clear({ emitEvent: false });
    processedValue.forEach(val => {
      this.availableLanguages = this.availableLanguages.filter(( { id } ) => id !== val.id);
      this.infoArray.push(this.initLangGroup(val), { emitEvent: false });
    });

    this.form.patchValue({ info: processedValue }, { emitEvent: false });

    this.onSelectedIndexChange(0);
    this.form.markAsPristine();
    this.form.markAsUntouched();
  }

  registerOnChange( fn: any ): void {
    this._onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( isDisabled: boolean ): void {
    if (isDisabled) {
      this.form.disable({ emitEvent: false });
    } else {
      this.form.enable({ emitEvent: false });
    }
  }

  validate(): ValidationErrors | null {
    return this.form.valid && this.controls.length ? null : { invalidForm: { valid: false } };
  }

  addTab( lang: string ) {
    this.infoArray.push(this.initLangGroup({ id: lang }));
    this.availableLanguages = this.availableLanguages.filter(( { id } ) => id !== lang);
    this.selectedTabIndex = this.controls.length - 1;
    if (this.translationManagerService) {
      this.translationManagerService.addLanguages([lang]);
    }
    this.addLang.emit(this.controls[this.selectedTabIndex]);
  }

  removeTab( index?: number ) {
    if (!this.isDisabled) {
      const indexToRemove = index ?? this.selectedTabIndex;

      const control = this.controls[indexToRemove].get('id');
      const removedLang = control?.value;

      const langSet = new Set(this.availableLanguages.map(( { id } ) => id));
      if (removedLang) {
        langSet.add(removedLang);
      }

      this.availableLanguages = this.languages.reduce<SwuiSelectOption[]>(( result, option ) => {
        if (langSet.has(option.id)) {
          result.push(option);
        }
        return result;
      }, []);

      if (this.selectedTabIndex === indexToRemove) {
        if (this.tabsRef && (this.selectedTabIndex === this.tabsRef._tabs.length - 2 || this.availableLanguages.length === 1)) {
          this.selectedTabIndex = indexToRemove - 1;
        } else {
          const val = this.infoArray.at(indexToRemove + 1) ? this.infoArray.at(indexToRemove + 1).value : null;
          this.childComponent?.setValue(val);
        }
      }
      this.infoArray.removeAt(indexToRemove);

      if (index === undefined && this.translationManagerService) {
        this.translationManagerService.removeLanguage(removedLang);
      }
    }
  }

  prevent( event: Event ) {
    event.preventDefault();
    event.stopPropagation();
  }

  onSelectedIndexChange( index: number ) {
    this.selectedTabIndex = index;
    if (index !== -1 && this.infoArray.at(index).value && this.childComponent) {
      this.childComponent.setValue(this.infoArray.at(index)?.value);
    }
  }

  trackByFn( _: number, control: AbstractControl ): string {
    return control.value.id;
  }

  private get infoArray(): UntypedFormArray {
    return this.form.get('info') as UntypedFormArray;
  }

  private transformForm( form: FormValues ): FormValues {
    return (form.info ?? {}).reduce(( result: FormValues, item: any, order: number ) => {
      const processedItem = {
        ...item,
        ...(this.isOrderingSupported ? { order } : {})
      };
      delete processedItem.id;
      return {
        ...result,
        [item.id]: processedItem
      };
    }, {});
  }

  private initLangGroup( val: any ): UntypedFormGroup {
    return this.childComponent ? this.childComponent.initForm(val) : new UntypedFormGroup({ id: new UntypedFormControl(val.id) });
  }

  private switchToFirstInvalidTab() {
    const firstInvalid = this.controls.findIndex(( { status } ) => status === 'INVALID');
    if (this.tabsRef && firstInvalid !== -1) {
      this.tabsRef.selectedIndex = firstInvalid;
    }
  }

  private listenLanguages() {
    if (this.translationManagerService) {
      this.translationManagerService.languages$.pipe(
        takeUntil(this.destroyed$)
      ).subscribe(languages => {
        const indexes = this.controls.reduce<number[]>(( res, { value }, index ) => {
          if (!languages.includes(value.id)) {
            res.unshift(index);
          }
          return res;
        }, []);

        indexes.forEach(index => {
          this.removeTab(index);
        });

        languages.forEach(language => {
          const availableLanguages = this.availableLanguages.map(( { id } ) => id);
          if (availableLanguages.includes(language)) {
            this.addTab(language);
          }
        });
      });
    }
  }
}

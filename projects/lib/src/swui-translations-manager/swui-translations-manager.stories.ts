import { moduleMetadata, storiesOf } from '@storybook/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { SwuiTranslationsManagerModule } from './swui-translations-manager.module';
import { SwuiTranslationsManagerComponent } from './swui-translations-manager.component';
import { I18nModule } from '../i18n.module';
import { MatMenuModule } from '@angular/material/menu';

const EN = require('./locale.json');

const languages = [
  { id: 'en', text: 'LANGUAGES.en' },
  { id: 'zh', text: 'LANGUAGES.zh' },
  { id: 'zh-tw', text: 'LANGUAGES.zh-tw' },
  { id: 'ja', text: 'LANGUAGES.ja' },
  { id: 'ro', text: 'LANGUAGES.ro' }
];

const template = `
      <lib-swui-translations-manager
        [languages]="languages"
        [isDisabled]="isDisabled"
        [ngModel]="value"
        [isRemoveLanguageDisabled]="isRemoveLanguageDisabled"
        [withoutDefault]="withoutDefault">
      </lib-swui-translations-manager>
`;

storiesOf('Translations manager', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        SwuiTranslationsManagerModule,
        I18nModule.forRoot({ translations: { en: EN } }),
        MatMenuModule,
      ],
    })
  )
  .add('default', () => ({
    component: SwuiTranslationsManagerComponent,
    template: template,
    props: {
      languages: languages,
      value: {
        'en': { },
        'ro': { },
        'zh': { }
      }
    },
  }))
  .add('withoutDefault', () => ({
    component: SwuiTranslationsManagerComponent,
    template: template,
    props: {
      languages: languages,
      withoutDefault: true,
    },
  }))
  .add('isRemoveLanguageDisabled', () => ({
    component: SwuiTranslationsManagerComponent,
    template: template,
    props: {
      languages: languages,
      isRemoveLanguageDisabled: true,
    },
  }))
  .add('disabled', () => ({
    component: SwuiTranslationsManagerComponent,
    template: template,
    props: {
      languages: languages,
      isDisabled: true
    },
  }));


<div class="table-games bordered" [ngStyle]="{height:height}">
  <div class="table-games__header">
    <mat-checkbox (change)="checkAllChanged()" [(ngModel)]="allChecked" [disabled]="disabled || !items?.length">
      {{ 'COMPONENTS.GAMES_SELECT_MANAGER.selectAll' | translate }}
    </mat-checkbox>
  </div>
  <div #scrollTable class="table-games__body"
       cdkDropList #selectedItems [cdkDropListData]="items" (cdkDropListDropped)="drop($event)"
       cdkDropListLockAxis="y" cdkDropListOrientation="vertical" cdkDropListAutoScrollDisabled="false">
    <table class="mat-table">
      <thead *ngIf="extraColumn">
        <tr class="mat-header-row">
          <th class="mat-header-cell" colspan="3">{{ 'COMPONENTS.GAMES_SELECT_MANAGER.game' | translate }}</th>
          <th class="mat-header-cell">{{ extraColumn.title | translate }}</th>
        </tr>
      </thead>

      <tbody>

        <tr class="element-row mat-row" *ngFor="let item of items" [ngSwitch]="item.type"
            [ngClass]="{'corrupted': item.type === itemTypes.CORRUPTION}"
            [cdkDragDisabled]="disabled" cdkDrag>

          <td *cdkDragPlaceholder colspan="4" class="drag-placeholder">
            <mat-checkbox [(ngModel)]="item.checked" disabled>
              <span class="table-games__game">{{ item.title }}</span>
            </mat-checkbox>
          </td>
          <td *cdkDragPreview class="drag-preview" >{{ item.title }}</td>

          <ng-container *ngSwitchCase="itemTypes.GAME">
            <td class="mat-cell" [ngClass]="{'handle':!disabled}" cdkDragHandle></td>
            <td class="mat-cell">
              <mat-checkbox (change)="itemSelectionChanged()" [(ngModel)]="item.checked" [disabled]="disabled">
                <span class="table-games__game">{{ item.title }}</span>
              </mat-checkbox>
            </td>
            <td class="mat-cell" style="padding-right: 16px; text-align: right">
              <mat-chip *ngFor="let gameLabel of item.gameInfo.labels" style="margin: 2px"
                [ngClass]="getLabelClass(gameLabel)">{{ gameLabel.title }}
              </mat-chip>
            </td>

            <td class="mat-cell" *ngIf="extraColumn" [extraColumn]="extraColumn" [game]="item.gameInfo" extra-column-chooser
                [disabled]="disabled"
                (valueChange)="extraColumnValueChanged()">
            </td>
          </ng-container>

          <ng-container *ngSwitchCase="item.type === itemTypes.LABEL || item.type === itemTypes.PROVIDER ? item.type : ''">
            <td class="mat-cell" [ngClass]="{'handle':!disabled}" cdkDragHandle></td>
            <td class="mat-cell">
              <mat-checkbox (change)="itemSelectionChanged()" [(ngModel)]="item.checked" [disabled]="disabled">
                <mat-chip [ngClass]="getLabelClass(item)">
                  <span class="table-games__game">{{ item.title }}</span>
                </mat-chip>
              </mat-checkbox>
            </td>
            <td class="mat-cell" style="padding-right: 16px; text-align: right">
              <span>
                {{ 'COMPONENTS.GAMES_SELECT_MANAGER.inlineGamesCount' | translate:{count: item.items.length} }}
              </span>
            </td>
          </ng-container>

          <ng-container *ngSwitchCase="itemTypes.INTERSECTION">
            <td class="mat-cell" [ngClass]="{'handle':!disabled}" cdkDragHandle></td>
            <td class="mat-cell">
              <mat-checkbox (change)="itemSelectionChanged()" [(ngModel)]="item.checked" [disabled]="disabled">
                <mat-chip *ngFor="let label of item.items" [ngClass]="getLabelClass(label)">
                  {{ label.title }}
                </mat-chip>
              </mat-checkbox>
            </td>
            <td class="mat-cell" style="padding-right: 16px; text-align: right">
              <span *ngIf="item.intersection.games">
                {{ 'COMPONENTS.GAMES_SELECT_MANAGER.inlineGamesCount' | translate:{count: item.intersection.games.length} }}
              </span>
            </td>
          </ng-container>

          <ng-container *ngSwitchCase="itemTypes.CORRUPTION">
            <td class="mat-cell" [ngClass]="{'handle':!disabled}" cdkDragHandle></td>
            <td class="mat-cell" colspan="2">
              <mat-checkbox (change)="itemSelectionChanged()" [(ngModel)]="item.checked" [disabled]="disabled">
                {{ 'COMPONENTS.GAMES_SELECT_MANAGER.itemNotAvailable' | translate: {id: item.id} }}
              </mat-checkbox>
            </td>
          </ng-container>

        </tr>

        <tr class="element-row mat-row" *ngIf="!items || items.length === 0">
          <td class="mat-cell" colspan="3" *ngIf="!extraColumn; else extraColumnNoGames">
            {{ emptyGamesText | translate }}
          </td>
          <ng-template #extraColumnNoGames>
            <td class="mat-cell" colspan="4">
              {{ 'COMPONENTS.GAMES_SELECT_MANAGER.emptySelectedGamesAlter' | translate }}
            </td>
          </ng-template>
        </tr>

      </tbody>
    </table>
  </div>

</div>

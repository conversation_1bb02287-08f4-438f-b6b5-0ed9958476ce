import { gameSelectItemType, gameSelectItemTypes } from './game-select-item-types';
import { GameInfo } from '../game.model';

export interface SelectItemLabel {
  id?: string;
  group: string;
  title: string;
}

export interface SelectItemGameInfo {
  code: string;
  title: string;
  type: string;
  labels: SelectItemLabel[];
  coins?: { [field: string]: { coin: number }; }[];
  coeff?: number;
}

interface SelectItemGameIntersection {
  games: (SelectItemLabel | SelectItemGameInfo)[];
}

export type GameSelectItemData = SelectItemLabel | SelectItemGameInfo | SelectItemGameIntersection | {};

export class GameSelectItem {
  checked: boolean;
  id?: string;
  type: gameSelectItemType;
  items: GameSelectItem[] = []; // for labels and intersections
  readonly data: GameSelectItemData; // can be GameInfo, Label, Provider, etc...

  constructor(obj: any) {
    this.checked = false;

    if ('id' in obj) {
      this.id = obj.id;
    }

    this.type = obj.type;
    this.data = obj.data || {};

    if (this.type === gameSelectItemTypes.INTERSECTION && obj.items) {
      this.items = obj.items;
    } else {
      this.items = [];
    }
  }

  get label(): SelectItemLabel {
    return this.data as SelectItemLabel;
  }

  get gameInfo(): SelectItemGameInfo {
    return this.data as SelectItemGameInfo;
  }

  get intersection(): SelectItemGameIntersection {
    return this.data as SelectItemGameIntersection;
  }

  public get title(): string {
    if ('title' in this.data) {
      return this.gameInfo.title;
    }
    return '';
  }

  public get isLabel(): boolean {
    return [
      gameSelectItemTypes.LABEL,
      gameSelectItemTypes.PROVIDER,
    ].indexOf(this.type) > -1;
  }

  toggleCheck() {
    this.checked = !this.checked;
  }

  public addGameToLabel(game: GameSelectItem) {
    if (this.isLabel) {
      this.items.push(game);
    }
  }

  public getGameLabels(): SelectItemLabel[] | undefined {
    if (this.type !== gameSelectItemTypes.GAME) {
      return;
    }
    return this.gameInfo.labels;
  }
}

export function fromGameInfo(game: GameInfo): GameSelectItem {
  if (!game.labels.find(label => label.id === game.providerCode)) {
    game.labels.push({
      id: game.providerCode,
      title: game.providerTitle,
      group: 'provider',
    }); // adding provider as label
  }
  if ('code' in game && 'providerCode' in game) {
    return new GameSelectItem({
      id: game.code,
      type: gameSelectItemTypes.GAME,
      data: game,
    });
  } else {
    return new GameSelectItem({
      type: gameSelectItemTypes.CORRUPTION,
      data: game,
    });
  }
}

export function createFromLabel( label: SelectItemLabel ): GameSelectItem {
  return new GameSelectItem({
    id: label.id,
    type: label.group === 'provider' ? gameSelectItemTypes.PROVIDER : gameSelectItemTypes.LABEL,
    data: label,
  });
}

export const getUniqueLabelGames = (labels: GameSelectItem[]): GameSelectItem[] => {
  let codes: string[] = []; // array of codes is required for determining unique games
  return labels.reduce((result: GameSelectItem[], {items}: GameSelectItem) => {
    const labelGames = items.filter(item => {
      return codes.indexOf(item.gameInfo.code) === -1;
    });
    codes = [...codes, ...labelGames.map(game => game.gameInfo.code)];
    return [...result, ...labelGames];
  }, []);
};

export const getIntersectedGames = (labels: GameSelectItem[]): GameSelectItem[] => {
  let labelIds = labels.map(label => label.label.id);
  return getUniqueLabelGames(labels).filter(game =>
    labelIds.every(id => (game.gameInfo.labels || []).map(l => l.id).indexOf(id) > -1)
  );
};

export function createLabelIntersection(items: GameSelectItem[]): GameSelectItem {
  const intersectionItems: GameSelectItem[] = items.filter(item => item && item.isLabel);
  return new GameSelectItem({
    type: gameSelectItemTypes.INTERSECTION,
    items: intersectionItems,
    data: {
      games: getIntersectedGames(intersectionItems)
    }
  });
}

export function getLabelClass(item: GameSelectItem | SelectItemLabel): string {
  const group = 'data' in item ? item.label.group : item.group;
  switch (group) {
    case 'platform':
      return 'sw-chip-green';
    case 'class':
      return 'sw-chip-blue';
    case 'feature':
      return 'sw-chip-green';
    default:
      return '';
  }
}

export interface GameSelectItemTypes {
  GAME: gameSelectItemType;
  PROVIDER: gameSelectItemType;
  LABEL: gameSelectItemType;
  INTERSECTION: gameSelectItemType;
  CORRUPTION: gameSelectItemType;
}

export type gameSelectItemType = 'game' | 'provider' | 'label' | 'intersection' | 'corruption';

export const gameSelectItemTypes: GameSelectItemTypes = {
  GAME: 'game',
  PROVIDER: 'provider',
  LABEL: 'label',
  INTERSECTION: 'intersection',
  CORRUPTION: 'corruption'
};

<div class="table-games bordered" [ngStyle]="{height:height}">
  <div class="table-games__header">
    <mat-checkbox
      [(ngModel)]="availableChecked"
      (change)="checkAvailableChanged()"
      [disabled]="disabled || !available?.length">
      {{ 'COMPONENTS.GAMES_SELECT_MANAGER.selectAll' | translate }}
    </mat-checkbox>

    <mat-form-field class="table-games__search no-field-padding" appearance="outline">
      <input
        matInput
        #searchInput
        type="text"
        [placeholder]="'COMPONENTS.GAMES_SELECT_MANAGER.searchGamesPlaceholder' | translate"
        [disabled]="disabled">
      <mat-icon matSuffix>search</mat-icon>
    </mat-form-field>
  </div>

  <div class="table-games__body">
    <cdk-virtual-scroll-viewport
      itemSize="48"
      minBufferPx="480"
      maxBufferPx="960"
      [style.height.px]="scrollPortHeight">
      <table class="mat-table">
        <tbody>
        <tr *ngIf="loading">
          <td colspan="2">
            <div class="loading-overlay"><i class="icon-spinner4 spinner"></i></div>
          </td>
        </tr>

        <tr class="element-row mat-row" *ngIf="(!available || available.length === 0) && !loading">
          <td class="mat-cell" colspan="2">
            {{ 'COMPONENTS.GAMES_SELECT_MANAGER.noGamesToShow' | translate }}
          </td>
        </tr>

        <tr class="element-row mat-row" *cdkVirtualFor="let game of available; trackBy: trackByFn">
          <td class="mat-cell">
            <mat-checkbox
              (change)="gameSelectionChanged()"
              [(ngModel)]="game.checked"
              [disabled]="disabled">
              <span class="table-games__game" title="{{ game.title }}">{{ game.title }}</span>
            </mat-checkbox>
          </td>
          <td class="mat-cell" style="padding: 4px; text-align: right">
            <mat-chip *ngFor="let label of game.gameInfo.labels" [ngClass]="getLabelClass(label)" style="margin: 2px">
              {{ label.title }}
            </mat-chip>
          </td>
        </tr>
        </tbody>
      </table>
    </cdk-virtual-scroll-viewport>
  </div>
</div>


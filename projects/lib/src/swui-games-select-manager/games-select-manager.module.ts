import { ScrollingModule } from '@angular/cdk/scrolling';
import { NgModule } from '@angular/core';

import { SwuiGamesSelectManagerComponent } from './games-select-manager.component';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { AvailableGamesComponent } from './available-games/available-games.component';
import { AvailableLabelsComponent } from './available-labels/available-labels.component';
import { GamesSelectManagerService } from './games-select-manager.service';
// import { DragulaModule } from 'ng2-dragula';
import { SelectedItemsComponent } from './selected-items/selected-items.component';
import { CoinValueColumnComponent } from './game-select-columns/coin-value-column.component';
import { GameCoeffColumnComponent } from './game-select-columns/game-coeff-column.component';
import { ExtraColumnChooserComponent } from './game-select-columns/extra-column-chooser.component';
import { FormsModule } from '@angular/forms';
import { FilteredPipe } from './filtered.pipe';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { MatListModule } from '@angular/material/list';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatChipsModule } from '@angular/material/chips';

@NgModule({
  imports: [
    CommonModule,
    TranslateModule,
    // DragulaModule.forRoot(),
    FormsModule,
    MatTabsModule,
    MatTooltipModule,
    MatButtonModule,
    MatInputModule,
    MatCheckboxModule,
    MatIconModule,
    MatListModule,
    DragDropModule,
    MatExpansionModule,
    MatChipsModule,
    ScrollingModule,
  ],
  exports: [
    SwuiGamesSelectManagerComponent,
  ],
  declarations: [
    SwuiGamesSelectManagerComponent,
    AvailableGamesComponent,
    AvailableLabelsComponent,
    SelectedItemsComponent,
    ExtraColumnChooserComponent,
    CoinValueColumnComponent,
    GameCoeffColumnComponent,
    FilteredPipe,
  ],
  providers: [
    GamesSelectManagerService
  ],
})
export class SwuiGamesSelectManagerModule {
}


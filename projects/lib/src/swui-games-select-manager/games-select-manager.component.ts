import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Observable, Subscription } from 'rxjs';
import { GameCategoryItemInterface } from './game-category-item.model';
import { gameSelectItemTypes } from './game-select-item/game-select-item-types';
import { GameSelectItem } from './game-select-item/game-select-item.model';

import { GamesSelectManagerService, SelectedGamesExtraColumnScheme } from './games-select-manager.service';

export const availableTabNames: { GAMES: string, LABELS: string } = {
  GAMES: 'games',
  LABELS: 'labels',
};

@Component({
    selector: 'lib-swui-games-select-manager',
    templateUrl: 'games-select-manager.component.html',
    styleUrls: [
        './games-select-manager.scss',
    ],
    standalone: false
})
export class SwuiGamesSelectManagerComponent implements OnInit, OnDestroy {

  @Input()
  set availableGames( games: GameSelectItem[] ) {
    if (!games) {
      return;
    }
    this.service.setupAvailableGames(games);
  }

  @Input()
  set availableLabels( labels: GameSelectItem[] ) {
    if (!labels) {
      return;
    }
    this.service.setupAvailableLabels(labels);
  }

  @Input()
  set categoryItems( items: GameCategoryItemInterface[] ) {
    if (!items) {
      return;
    }
    this.service.setupCategoryItems(items);
  }

  @Input()
  set selectedGames( games: GameSelectItem[] ) {
    if (!games) {
      return;
    }
    this.service.setupSelectedGames(games);
  }

  @Input() selectedGamesTitle = 'COMPONENTS.GAMES_SELECT_MANAGER.selectedGamesTitle';
  @Input() emptyGamesText = 'COMPONENTS.GAMES_SELECT_MANAGER.emptySelectedGames';
  @Input() selectedGamesExtraColumn: SelectedGamesExtraColumnScheme | undefined;
  @Input() embedded = false;
  @Input() disabled = false;
  @Input() hideLabelsTab = false;
  @Input() availableGamesLoading = false;
  @Input() firstShowTab = availableTabNames.GAMES;
  @Input() height = '500px';

  @Input()
  set saveSource( source$: Observable<any> ) {
    if (!source$) {
      return;
    }
    let sub = source$.subscribe(() => {
      this.service.setupSelectedItems([]);
      sub.unsubscribe();
    });
  }

  @Output() selectedItemsChanged: EventEmitter<Object> = new EventEmitter();
  @Output() dropListDropped: EventEmitter<GameSelectItem[]> = new EventEmitter();

  gamesTotal = 0;
  labelsTotal = 0;
  selectedGamesTotal = 0;
  intersection: GameSelectItem | undefined;

  checkedAvailable: number | undefined;
  checkedFullAvailable: number | undefined;
  checkedSelected: number | undefined;
  checkedFullSelected: number | undefined;

  availableTabs = [availableTabNames.GAMES, availableTabNames.LABELS];
  tabs = availableTabNames;

  private subs: Subscription[] = [];

  constructor( private service: GamesSelectManagerService ) {
  }

  ngOnInit() {
    this.subs.push(
      this.service.gamesTotal$.subscribe(total => {
        this.gamesTotal = total;
      }),
      this.service.labelsTotal$.subscribe(total => {
        this.labelsTotal = total;
      }),
      this.service.selectedTotal$.subscribe(total => {
        this.selectedGamesTotal = total;
      }),
      this.service.selectedItems$.subscribe(( items: GameSelectItem[] ) => {
        items = items.filter(item => item.type !== gameSelectItemTypes.CORRUPTION); // cleaning up from corrupted items
        const games = items.filter(item => item.type === gameSelectItemTypes.GAME);
        const labels = items.filter(item => item.isLabel);
        this.selectedItemsChanged.emit({ games, labels, items });
      }),
      this.service.createdIntersection$.subscribe(item => {
        this.intersection = item;
      }),
      this.service.intersectionDestroyed$.subscribe(() => {
        this.intersection = undefined;
      }),
      this.service.checkedAvailableGamesCount$.subscribe(count => {
        this.checkedAvailable = count;
      }),
      this.service.checkedAvailableCount$.subscribe(count => {
        this.checkedFullAvailable = count;
      }),
      this.service.checkedSelectedCount$.subscribe(count => {
        this.checkedFullSelected = count;
      }),
      this.service.checkedSelectedGamesCount$.subscribe(count => {
        this.checkedSelected = count;
      })
    );
  }

  ngOnDestroy(): void {
    this.subs.forEach(s => s.unsubscribe());
    this.service.selectedItems$.next([]);
  }

  addToSelectedGames() {
    if (this.disabled) {
      return;
    }
    if (this.intersection) {
      this.service.addIntersection(this.intersection);
    } else {
      this.service.addToSelectedItems();
    }
  }

  removeFromSelectedGames() {
    if (this.disabled) {
      return;
    }
    this.service.removeFromSelectedItems();
  }

  getFirstTabIndex() {
    let idx = this.availableTabs.indexOf(this.firstShowTab);
    if (idx === -1) {
      idx = 0;
    }
    return idx;
  }

  onDropListDropped( items: GameSelectItem[] ) {
    this.dropListDropped.emit(items);
  }
}

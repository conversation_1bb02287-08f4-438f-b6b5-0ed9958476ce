import { Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { GamesSelectManagerService } from '../games-select-manager.service';
import { GameSelectItem, getLabelClass, getUniqueLabelGames, SelectItemLabel } from '../game-select-item/game-select-item.model';
import { fromEvent, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged, map } from 'rxjs/operators';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'sw-available-labels',
    templateUrl: 'available-labels.component.html',
    styleUrls: ['../games-select-manager.scss'],
    standalone: false
})
export class AvailableLabelsComponent implements OnInit, OnDestroy {

  @Input() disabled = false;
  @Input() height = '500px';

  @ViewChild('searchInput', { static: true }) searchInput: ElementRef | undefined;
  searchTerm = '';

  labels: GameSelectItem[] = [];
  checkedLabels: GameSelectItem[] = [];
  checkedLabelsGames: GameSelectItem[] = [];

  intersection: GameSelectItem | null = null;
  gamesPreviewVisible = false;
  intersectionPreviewVisible = false;

  private subscriptions: Subscription[] = [];

  constructor(
    private service: GamesSelectManagerService,
  ) {
  }

  get scrollPortHeight(): number {
    return parseFloat(this.height) - 58;
  }

  ngOnInit() {
    this.createSearchStream();
    this.subscribeToAvailableLabels();
    this.subscribeToIntersection();
    this.subscribeToAddedItems();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  labelCheckChanged() {
    const checked = (this.labels || []).filter(item => item.checked);
    this.setCheckedLabels(checked);
  }

  labelCheck( label: GameSelectItem ) {
    label.toggleCheck();
    this.labelCheckChanged();
  }


  getLabelClass( label: GameSelectItem | SelectItemLabel ): string {
    return getLabelClass(label);
  }

  private removeCheckedLabels() {
    this.checkedLabels.forEach(label => label.checked = false);
    this.checkedLabels = [];
    this.checkedLabelsGames = [];
    this.service.calculateCheckedAvailableLabels(this.checkedLabels);
  }

  private setCheckedLabels( checked: GameSelectItem[] ) {
    this.checkedLabels = checked;
    this.checkedLabelsGames = getUniqueLabelGames(this.checkedLabels)
      .sort(( a, b ) => a.title > b.title ? 1 : -1);

    this.service.calculateCheckedAvailableLabels(this.checkedLabels);
    this.checkForIntersection();
  }

  /**
   * addedItems contains items which were added to category
   * we need to clean some available labels if they were affected
   */
  private subscribeToAddedItems() {
    this.service.addedItems$.subscribe(( added ) => {
      let labelsWereAdded = added.some(item => item.isLabel);
      if (labelsWereAdded) {
        this.removeCheckedLabels();
      }
      this.service.calculateCheckedAvailableLabels(this.checkedLabels);
    });
  }

  private subscribeToIntersection() {
    this.service.createdIntersection$.subscribe(item => {
      this.intersection = item;
      this.service.calculateCheckedAvailableIntersection(item);
    });

    this.service.intersectionDestroyed$.subscribe(( { uncheckLabels } ) => {
      this.intersection = null;
      this.service.calculateCheckedAvailableIntersection(null);
      if (uncheckLabels) {
        this.removeCheckedLabels();
      }
    });
  }

  private subscribeToAvailableLabels() {
    this.service.availableLabels$.subscribe(labels => {
      // this.uncheckCheckedLabels();
      this.labels = labels;
    });
  }

  private checkForIntersection() {
    if (this.checkedLabels.length > 1) {
      this.createIntersection();
    } else {
      this.removeIntersection();
    }
  }

  private createIntersection() {
    this.service.createIntersection(this.checkedLabels);
  }

  private removeIntersection() {
    if (this.intersection) {
      this.service.removeAvailableIntersection();
    }
  }

  private createSearchStream() {
    if (this.searchInput) {
      let sub = fromEvent(this.searchInput.nativeElement, 'input')
        .pipe(
          map(( e: any ) => e['currentTarget']['value']),
          debounceTime(100),
          distinctUntilChanged()
        )
        .subscribe(( search ) => {
          this.searchTerm = search;
        });
      this.subscriptions.push(sub);
    }
  }
}

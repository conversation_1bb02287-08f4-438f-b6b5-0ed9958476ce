<div class="table-games bordered" [ngStyle]="{height:height}">
  <div class="table-games__header">
    <mat-form-field class="table-games__search table-games__search--full no-field-padding" appearance="outline">
      <input
        matInput
        #searchInput
        type="text"
        [placeholder]="'COMPONENTS.GAMES_SELECT_MANAGER.searchLabelsPlaceholder' | translate"
        [disabled]="disabled">
      <mat-icon matSuffix>search</mat-icon>
    </mat-form-field>
  </div>

  <div class="table-games__body">
    <cdk-virtual-scroll-viewport
      itemSize="48"
      minBufferPx="480"
      maxBufferPx="960"
      [style.height.px]="scrollPortHeight">
      <div style="padding:10px;padding-left:24px;" *ngIf="checkedLabels && checkedLabels.length">
        <mat-chip-set>
          <mat-chip *ngFor="let label of checkedLabels" [removable]="true"
                    (removed)="labelCheck(label)" [ngClass]="getLabelClass(label)">
            {{ label.title }}
            <mat-icon matChipRemove>cancel</mat-icon>
          </mat-chip>
        </mat-chip-set>
      </div>

      <mat-accordion>
        <mat-expansion-panel *ngIf="intersection">
          <mat-expansion-panel-header>
            <mat-panel-title class="table-games__panel-title">
              {{ 'COMPONENTS.GAMES_SELECT_MANAGER.countGamesInIntersection' | translate:{count: intersection.intersection.games.length} }}
            </mat-panel-title>
          </mat-expansion-panel-header>
          <mat-list *ngIf="intersection.intersection.games.length" class="intersection">
            <mat-list-item *ngFor="let game of intersection.intersection.games" class="intersection__item">
              <span class="intersection__name">{{ game.title }}</span>
              <div class="intersection__labels">
                <mat-chip *ngFor="let gameLabel of game.labels" class="intersection__chip"
                          [ngClass]="getLabelClass(gameLabel)">{{ gameLabel.title }}</mat-chip>
              </div>
            </mat-list-item>
          </mat-list>
        </mat-expansion-panel>

        <mat-expansion-panel *ngIf="checkedLabelsGames && checkedLabelsGames.length && !intersection">
          <mat-expansion-panel-header>
            <mat-panel-title class="table-games__panel-title">
              {{ 'COMPONENTS.GAMES_SELECT_MANAGER.countGamesInSelectedLabels' | translate:{count: checkedLabelsGames.length} }}
            </mat-panel-title>
          </mat-expansion-panel-header>
          <mat-list class="intersection">
            <mat-list-item *ngFor="let game of checkedLabelsGames" class="intersection__item">
              <span class="intersection__name">{{ game.title }}</span>
              <div class="intersection__labels">
                <mat-chip *ngFor="let gameLabel of game.gameInfo.labels" class="intersection__chip"
                          [ngClass]="getLabelClass(gameLabel)">
                  {{ gameLabel.title }}
                </mat-chip>
              </div>
            </mat-list-item>
          </mat-list>
        </mat-expansion-panel>
      </mat-accordion>

      <table class="mat-table">
        <tbody>
        <tr class="element-row mat-row" *cdkVirtualFor="let label of labels | filtered : searchTerm : 'label'">
          <td class="mat-cell">
            <mat-checkbox (change)="labelCheckChanged()" [(ngModel)]="label.checked" [disabled]="disabled">
              <mat-chip [ngClass]="getLabelClass(label)">
                {{ label.title }}
              </mat-chip>
            </mat-checkbox>
          </td>

          <td class="mat-cell" style="white-space: nowrap">
            {{ 'COMPONENTS.GAMES_SELECT_MANAGER.inlineGamesCount' |
            translate:{count: label.items ? label.items.length : 0} }}
          </td>
        </tr>
        </tbody>
      </table>
    </cdk-virtual-scroll-viewport>
  </div>

</div>

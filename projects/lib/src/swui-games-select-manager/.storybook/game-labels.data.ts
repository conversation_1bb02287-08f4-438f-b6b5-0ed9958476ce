/* eslint-disable:quotemark */
export const labels = [
  {
    "id": "W4RkGRen",
    "title": "slot",
    "group": "class"
  },
  {
    "id": "v5R2nRkE",
    "title": "fish game",
    "group": "class"
  },
  {
    "id": "kx3o8qeK",
    "title": "table game",
    "group": "class"
  },
  {
    "id": "4DR4LR1K",
    "title": "roulette",
    "group": "class"
  },
  {
    "id": "JyRgY3XZ",
    "title": "live",
    "group": "class"
  },
  {
    "id": "okB7nR6J",
    "title": "card",
    "group": "class"
  },
  {
    "id": "NlqP5RZj",
    "title": "arcade",
    "group": "class"
  },
  {
    "id": "p2qOeBEP",
    "title": "mini game",
    "group": "class"
  },
  {
    "id": "zx3Ybq1v",
    "title": "html5",
    "group": "platform"
  },
  {
    "id": "z9R67BjP",
    "title": "flash",
    "group": "platform"
  },
  {
    "id": "zbqLpq7A",
    "title": "downloadable",
    "group": "platform"
  },
  {
    "id": "1G3jaBXk",
    "title": "progressive",
    "group": "feature"
  },
  {
    "id": "pQ3513OE",
    "title": "jackpot",
    "group": "feature"
  },
  {
    "id": "D7q1QqZv",
    "title": "branded",
    "group": "feature"
  },
  {
    "id": "g5BJXq9r",
    "title": "html",
    "group": "platform"
  },
  {
    "id": "QX3xOBaJ",
    "title": "BRANDED",
    "group": "feature"
  },
  {
    "id": "1A3nPRQ7",
    "title": "BRANDEDв",
    "group": "feature"
  },
  {
    "id": "n0Bd6qkY",
    "title": "self-owned",
    "group": "feature"
  },
  {
    "id": "voRVgBkp",
    "title": "test",
    "group": "platform"
  },
  {
    "id": "4DR4aLB1",
    "title": "html51",
    "group": "platform"
  },
  {
    "id": "wYRD5BbO",
    "title": "GP-test",
    "group": "platform"
  }
];

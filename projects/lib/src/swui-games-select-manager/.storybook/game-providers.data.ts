/* eslint-disable:quotemark */
export const gameProviders = [
  {
    "id": "zx3Ybq1v",
    "code": "provider1",
    "title": "provider1",
    "status": "normal",
    "isTest": true,
    "mustStoreExtHistory": false
  },
  {
    "id": "W4RkGRen",
    "code": "SW",
    "title": "Skywind",
    "status": "normal",
    "mustStoreExtHistory": false
  },
  {
    "id": "JyRgY3XZ",
    "code": "apiuser",
    "title": "QuickSpin",
    "status": "normal",
    "mustStoreExtHistory": false
  },
  {
    "id": "4DR4LR1K",
    "code": "PT",
    "title": "Playtech",
    "status": "normal",
    "isTest": true,
    "mustStoreExtHistory": false
  },
  {
    "id": "m0RarBzk",
    "code": "SW2",
    "title": "Skywind2",
    "status": "normal",
    "mustStoreExtHistory": false
  },
  {
    "id": "QdR8eB9e",
    "code": "SW3",
    "title": "Skywind3",
    "status": "normal",
    "mustStoreExtHistory": false
  },
  {
    "id": "GJqNWRkX",
    "code": "DP",
    "title": "Demo_provider",
    "status": "normal",
    "mustStoreExtHistory": false
  },
  {
    "id": "kx3WLq1N",
    "code": "FLC-PT",
    "title": "Playtech2",
    "status": "normal",
    "isTest": true,
    "mustStoreExtHistory": false
  },
  {
    "id": "v5R2nRkE",
    "code": "FLC2",
    "title": "Games OS",
    "status": "normal",
    "isTest": true,
    "mustStoreExtHistory": true
  },
  {
    "id": "W4RkkMRe",
    "code": "PH",
    "title": "phantom",
    "status": "normal",
    "isTest": true,
    "mustStoreExtHistory": false
  },
  {
    "id": "kx3ogP3e",
    "code": "AD",
    "title": "TestProvider",
    "status": "normal",
    "isTest": true,
    "mustStoreExtHistory": false
  },
  {
    "id": "doRlMGBr",
    "code": "EU1",
    "title": "provider_eu1",
    "status": "normal",
    "isTest": true,
    "mustStoreExtHistory": false
  },
  {
    "id": "W4RkKQRe",
    "code": "EU2",
    "title": "provider_eu2",
    "status": "normal",
    "isTest": true,
    "mustStoreExtHistory": false
  },
  {
    "id": "zbqLpq7A",
    "code": "QS",
    "title": "quickspin",
    "status": "normal",
    "isTest": true,
    "mustStoreExtHistory": true
  },
  {
    "id": "pQ35rVqO",
    "code": "ec",
    "title": "eyecon",
    "status": "normal",
    "isTest": true,
    "mustStoreExtHistory": true
  },
  {
    "id": "kx3WWN31",
    "code": "SkywindProvider14",
    "title": "Skywind Provider 14",
    "status": "normal",
    "isTest": true,
    "mustStoreExtHistory": false
  },
  {
    "id": "Eo3z2835",
    "code": "SkywindProvider15",
    "title": "Skywind Provider 15",
    "status": "normal",
    "isTest": true,
    "mustStoreExtHistory": false
  },
  {
    "id": "o0qbNdqz",
    "code": "SkywindProvider16",
    "title": "Skywind Provider 16",
    "status": "normal",
    "isTest": true,
    "mustStoreExtHistory": false
  },
  {
    "id": "g6qQY2q9",
    "code": "SkywindProvider17",
    "title": "Skywind Provider 17",
    "status": "normal",
    "isTest": true,
    "mustStoreExtHistory": false
  },
  {
    "id": "g5BJ2eB9",
    "code": "SkywindProvider18",
    "title": "Skywind Provider 18",
    "status": "normal",
    "isTest": true,
    "mustStoreExtHistory": false
  },
  {
    "id": "mvRXNN3X",
    "code": "SkywindProvider19",
    "title": "Skywind Provider 19",
    "status": "normal",
    "isTest": true,
    "mustStoreExtHistory": false
  },
  {
    "id": "VO3E5k3D",
    "code": "SkywindProviderMock20",
    "title": "Skywind Provider Mock20",
    "status": "normal",
    "isTest": true,
    "mustStoreExtHistory": false
  },
  {
    "id": "r03pOoBw",
    "code": "SkywindProvider6",
    "title": "Skywind Provider 6",
    "status": "normal",
    "isTest": true,
    "mustStoreExtHistory": false
  }
];

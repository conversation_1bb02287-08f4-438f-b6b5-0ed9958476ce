/* eslint-disable:quotemark */

import { GameInfo } from '../game.model';

function mapToGameItem( obj: any ): GameInfo {
  return {
    providerName: obj['providerName'] || '',
    providerCode: obj['providerCode'] || '',
    providerTitle: obj['providerTitle'] || '',
    title: obj['title'] || '',
    type: obj['type'] || '',
    defaultInfo: obj['defaultInfo'] || {},
    info: obj['info'] || {},
    limits: obj['limits'],
    labels: obj['labels'] || [],
    features: obj['features'] || {},
    comment: obj['comment'] || '',
    code: obj['code'] || '',
    status: obj['status'] || '',
    settings: obj['settings'] || {},
    royalties: obj['royalties'] || null,
  } as GameInfo;
}

export const games: GameInfo[] = [
  {
    "code": "sw_20_times",
    "title": "20 Times Pay",
    "type": "slot",
    "defaultInfo": {
      "name": "20 Times Pay",
      "description": "Skywind"
    },
    "info": {},
    "labels": [
      {
        "id": "pQ3513OE",
        "title": "jackpot",
        "group": "feature"
      }
    ],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "isGRCGame": true,
      "jackpotTypes": [
        "sw-srt-challenge",
        "sw-srt-tournament"
      ]
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": "",
    "releaseDate": "2017-08-30T11:05:58.978Z",
    "limitsGroup": null,
    "countries": [],
    "totalBetMultiplier": "1",
    "schemaDefinitionId": "pQ35Q13O",
    "status": "normal",
    "settings": {
      "jackpotId": {
        "sw-srt-challenge": "sw-srt-challenge",
        "sw-srt-tournament": "sw-srt-tournament"
      }
    },
    "royalties": "0.12"
  },
  {
    "code": "sw_20_times_test6394",
    "title": "20 Times Pay Test 6394 updated",
    "type": "slot",
    "defaultInfo": {
      "name": "20 Times Pay",
      "description": "Skywind"
    },
    "info": {},
    "labels": [
      {
        "id": "pQ3513OE",
        "title": "jackpot",
        "group": "feature"
      }
    ],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "isGRCGame": true,
      "jackpotTypes": [
        "sw-dragon-dozer"
      ],
      "transferEnabled": true,
      "isFreebetSupported": true,
      "isBonusCoinsSupported": false
    },
    "clientFeatures": {},
    "historyRenderType": 1,
    "historyUrl": null,
    "releaseDate": "2019-05-21T16:33:33.279Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "1",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "settings": {
      "jackpotId": {
        "sw-dragon-dozer": "sw-ramesses-coin"
      }
    },
    "royalties": "0.12"
  },
  {
    "code": "sw_ewb_clone1",
    "title": "234_clone1",
    "type": "slot",
    "defaultInfo": {
      "name": "East Wind Battle",
      "description": "Sky Wind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "isGRCGame": false,
      "jackpotTypes": null,
      "transferEnabled": false,
      "isFreebetSupported": false,
      "isBonusCoinsSupported": true
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2019-07-31T11:09:31.424Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": null,
    "schemaDefinitionId": null,
    "status": "normal",
    "royalties": "0.12"
  },
  {
    "code": "sw_ewb_clone2",
    "title": "234_clone2",
    "type": "slot",
    "defaultInfo": {
      "name": "East Wind Battle",
      "description": "Sky Wind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "isGRCGame": false,
      "jackpotTypes": null,
      "transferEnabled": false,
      "isFreebetSupported": false,
      "isBonusCoinsSupported": true
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2019-07-31T11:13:07.260Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": null,
    "schemaDefinitionId": null,
    "status": "normal",
    "royalties": "0.12"
  },
  {
    "code": "sw_2pd",
    "title": "2 Powerful Dragons",
    "type": "slot",
    "defaultInfo": {
      "name": "2 Powerful Dragons",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "jackpotTypes": [
        "sw-two-powerful-dragons"
      ]
    },
    "clientFeatures": {},
    "historyRenderType": 1,
    "historyUrl": null,
    "releaseDate": "2018-08-09T11:22:47.336Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "25",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "settings": {
      "jackpotId": {
        "sw-two-powerful-dragons": "SW-TWO-POWERFULL-DRAGONS"
      }
    },
    "royalties": "0.10"
  },
  {
    "code": "sw_2pd2",
    "title": "2 Powerful Dragons cheats",
    "type": "slot",
    "defaultInfo": {
      "name": "2 Powerful Dragons",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "jackpotTypes": [
        "sw-two-powerful-dragons"
      ]
    },
    "clientFeatures": {},
    "historyRenderType": 1,
    "historyUrl": null,
    "releaseDate": "2018-12-06T10:23:00.763Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "25",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "settings": {
      "jackpotId": {
        "sw-two-powerful-dragons": "SW-TWO-POWERFULL-DRAGONS"
      }
    },
    "royalties": "0.15"
  },
  {
    "code": "sw_2pd3",
    "title": "2 Powerful Dragons prod",
    "type": "slot",
    "defaultInfo": {
      "name": "2 Powerful Dragons",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "jackpotTypes": [
        "sw-two-powerful-dragons"
      ]
    },
    "clientFeatures": {},
    "historyRenderType": 1,
    "historyUrl": null,
    "releaseDate": "2018-12-06T10:23:23.725Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "25",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "settings": {
      "jackpotId": {
        "sw-two-powerful-dragons": "SW-TWO-POWERFULL-DRAGONS"
      }
    },
    "royalties": "0.15"
  },
  {
    "code": "sw_tcb_dp",
    "title": "3 Card Brag",
    "type": "table",
    "defaultInfo": {
      "name": "3 Card Brag",
      "limits": {},
      "description": "Slot description"
    },
    "info": {},
    "labels": [],
    "comment": "",
    "providerCode": "DP",
    "providerTitle": "Demo_provider",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2018-02-01T19:09:52.444Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": null,
    "schemaDefinitionId": null,
    "status": "normal",
    "royalties": "0.10"
  },
  {
    "code": "sw_tcb",
    "title": "3 Card Brag",
    "type": "table",
    "defaultInfo": {
      "name": "3 Card Brag",
      "limits": {},
      "description": "Slot description"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 2,
    "historyUrl": null,
    "releaseDate": "2017-08-15T14:26:20.601Z",
    "limitsGroup": null,
    "countries": [],
    "totalBetMultiplier": null,
    "schemaDefinitionId": null,
    "status": "normal",
    "royalties": "0.12"
  },
  {
    "code": "sw_tcb_xxx",
    "title": "3 Card Brag",
    "type": "table",
    "defaultInfo": {
      "name": "3 Card Brag",
      "limits": {},
      "description": "Slot description"
    },
    "info": null,
    "labels": [],
    "comment": null,
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "isGRCGame": false,
      "jackpotTypes": null,
      "transferEnabled": false,
      "isFreebetSupported": false,
      "isBonusCoinsSupported": false
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2019-05-03T09:28:15.530Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": null,
    "schemaDefinitionId": null,
    "status": "normal",
    "royalties": null
  },
  {
    "code": "sw_tcb3",
    "title": "3 Card Brag PROD",
    "type": "table",
    "defaultInfo": {
      "name": "3 Card Brag",
      "limits": {},
      "description": "Slot description #1"
    },
    "info": {},
    "labels": [
      {
        "id": "g5BJXq9r",
        "title": "html",
        "group": "platform"
      }
    ],
    "comment": "edited",
    "providerCode": "SW3",
    "providerTitle": "Skywind3",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 2,
    "historyUrl": null,
    "releaseDate": "2018-01-23T10:03:31.815Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": null,
    "schemaDefinitionId": null,
    "status": "normal",
    "royalties": "0.10"
  },
  {
    "code": "sw_tcb2",
    "title": "3 Card Brag with cheats",
    "type": "table",
    "defaultInfo": {
      "name": "3 Card Brag",
      "limits": {},
      "description": "Slot description"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW2",
    "providerTitle": "Skywind2",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 2,
    "historyUrl": null,
    "releaseDate": "2018-01-16T13:39:24.812Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": null,
    "schemaDefinitionId": null,
    "status": "normal",
    "royalties": "0.10"
  },
  {
    "code": "sw_50_times",
    "title": "50 Times Pay",
    "type": "slot",
    "defaultInfo": {
      "name": "50 Times Pay",
      "description": "Skywind"
    },
    "info": {},
    "labels": [
      {
        "id": "g5BJXq9r",
        "title": "html",
        "group": "platform"
      }
    ],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "isGRCGame": true,
      "jackpotTypes": [
        "sw-srt-challenge",
        "sw-srt-tournament"
      ]
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2017-08-30T11:09:52.020Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "1",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "settings": {
      "jackpotId": {
        "sw-srt-challenge": "sw-srt-challenge",
        "sw-srt-tournament": "sw-srt-tournament"
      }
    },
    "royalties": "0.12"
  },
  {
    "code": "sw_5_times",
    "title": "5 Times Pay",
    "type": "slot",
    "defaultInfo": {
      "name": "5 Times Pay",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "isGRCGame": true,
      "jackpotTypes": [
        "sw-srt-challenge",
        "sw-srt-tournament"
      ]
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2017-08-29T11:29:44.109Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "1",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "settings": {
      "jackpotId": {
        "sw-srt-challenge": "sw-srt-challenge",
        "sw-srt-tournament": "sw-srt-tournament"
      }
    },
    "royalties": "0.12"
  },
  {
    "code": "sw_888t",
    "title": "888 Turtles",
    "type": "slot",
    "defaultInfo": {
      "name": "888 Turtles",
      "description": "SkyWind"
    },
    "info": {},
    "labels": [
      {
        "id": "W4RkGRen",
        "title": "slot",
        "group": "class"
      }
    ],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "isGRCGame": false,
      "jackpotTypes": null,
      "transferEnabled": false,
      "isFreebetSupported": false,
      "isBonusCoinsSupported": false,
      "isMarketplaceSupported": false
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2017-03-07T14:46:51.764Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "25",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": "0.12"
  },
  {
    "code": "sw_888t33",
    "title": "888 Turtles",
    "type": "slot",
    "defaultInfo": {
      "name": "888 Turtles",
      "description": "SkyWind"
    },
    "info": {},
    "labels": [
      {
        "id": "W4RkGRen",
        "title": "slot",
        "group": "class"
      }
    ],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2019-04-25T10:36:06.573Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "25",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": "0.12"
  },
  {
    "code": "sw_888t_dp",
    "title": "888 Turtles",
    "type": "slot",
    "defaultInfo": {
      "name": "888 Turtles",
      "description": "SkyWind"
    },
    "info": {},
    "labels": [],
    "comment": "",
    "providerCode": "DP",
    "providerTitle": "Demo_provider",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2018-02-01T19:02:11.704Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": null,
    "schemaDefinitionId": null,
    "status": "normal",
    "royalties": "0.10"
  },
  {
    "code": "sw_888t3",
    "title": "888 Turtles prod",
    "type": "slot",
    "defaultInfo": {
      "name": "888 Turtles",
      "description": "SkyWind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW3",
    "providerTitle": "Skywind3",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2018-01-03T00:16:18.571Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "25",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": "0.10"
  },
  {
    "code": "sw_888t2",
    "title": "888 Turtles with cheats",
    "type": "slot",
    "defaultInfo": {
      "name": "888 Turtles",
      "description": "SkyWind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW2",
    "providerTitle": "Skywind2",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2018-01-03T00:15:57.722Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "25",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": "0.10"
  },
  {
    "code": "sw_88sf",
    "title": "88 Shi fu",
    "type": "slot",
    "defaultInfo": {
      "name": "88 Shi fu",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "isBonusCoinsSupported": true
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2017-07-07T09:45:11.144Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": "0.12"
  },
  {
    "code": "sw_88sf_dp",
    "title": "88 Shi fu",
    "type": "slot",
    "defaultInfo": {
      "name": "88 Shi fu",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "",
    "providerCode": "DP",
    "providerTitle": "Demo_provider",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2018-02-01T18:46:20.211Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": null,
    "schemaDefinitionId": null,
    "status": "normal",
    "royalties": "0.10"
  },
  {
    "code": "sw_8tr1qu",
    "title": "8 Treasures 1 Queen",
    "type": "slot",
    "defaultInfo": {
      "name": "8 Treasures 1 Queen",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": null,
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "rtp": 97.29,
      "isBonusCoinsSupported": false,
      "isMarketplaceSupported": false
    },
    "clientFeatures": {},
    "historyRenderType": 1,
    "historyUrl": null,
    "releaseDate": "2018-10-31T13:08:29.735Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": null
  },
  {
    "code": "sw_8tr1qu2",
    "title": "8 Treasures 1 Queen cheats",
    "type": "slot",
    "defaultInfo": {
      "name": "8 Treasures 1 Queen",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": null,
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 1,
    "historyUrl": null,
    "releaseDate": "2018-12-11T08:20:43.888Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": null
  },
  {
    "code": "sw_8tr1qu3",
    "title": "8 Treasures 1 Queen prod",
    "type": "slot",
    "defaultInfo": {
      "name": "8 Treasures 1 Queen",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": null,
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 1,
    "historyUrl": null,
    "releaseDate": "2018-12-11T08:21:26.407Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": null
  },
  {
    "code": "sw_npot",
    "title": "9 Pandas On Top",
    "type": "slot",
    "defaultInfo": {
      "name": "9 Pandas On Top",
      "description": "Sky Wind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "isBonusCoinsSupported": true
    },
    "clientFeatures": {},
    "historyRenderType": 1,
    "historyUrl": null,
    "releaseDate": "2018-02-09T12:16:29.213Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": "0.10"
  },
  {
    "code": "sw_npot2",
    "title": "9 Pandas On Top cheats",
    "type": "slot",
    "defaultInfo": {
      "name": "9 Pandas On Top",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW2",
    "providerTitle": "Skywind2",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2018-06-12T09:52:35.980Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": "0.10"
  },
  {
    "code": "sw_npot3",
    "title": "9 Pandas On Top prod",
    "type": "slot",
    "defaultInfo": {
      "name": "9 Pandas On Top",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW3",
    "providerTitle": "Skywind3",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2018-06-12T09:52:53.810Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": "0.10"
  },
  {
    "code": "sw_9s1k_dp",
    "title": "9 Sons, 1 King",
    "type": "slot",
    "defaultInfo": {
      "name": "9 Sons, 1 King",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "",
    "providerCode": "DP",
    "providerTitle": "Demo_provider",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2018-02-01T18:55:00.060Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": null,
    "schemaDefinitionId": null,
    "status": "normal",
    "royalties": "0.10"
  },
  {
    "code": "sw_9s1k",
    "title": "9 Sons, 1 King",
    "type": "slot",
    "defaultInfo": {
      "name": "9 Sons, 1 King",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "isBonusCoinsSupported": true
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2017-07-07T09:44:27.424Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": "0.12"
  },
  {
    "code": "sw_9s1k2",
    "title": "9 Sons, 1 King cheat",
    "type": "slot",
    "defaultInfo": {
      "name": "9 Sons, 1 King",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW2",
    "providerTitle": "Skywind2",
    "features": {
      "isBonusCoinsSupported": true
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2018-04-03T14:51:27.589Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": "0.10"
  },
  {
    "code": "sw_9s1k3",
    "title": "9 Sons, 1 King prod",
    "type": "slot",
    "defaultInfo": {
      "name": "9 Sons, 1 King",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW3",
    "providerTitle": "Skywind3",
    "features": {
      "isBonusCoinsSupported": true
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2018-04-03T14:51:52.498Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": "0.10"
  },
  {
    "code": "gos_pt_aogs",
    "title": "Age of the Gods",
    "type": "slot",
    "defaultInfo": {
      "name": "Age of the Gods",
      "description": "FLC"
    },
    "info": {},
    "labels": [],
    "comment": "",
    "providerCode": "FLC-PT",
    "providerTitle": "Playtech2",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2017-05-04T09:53:45.578Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": null,
    "schemaDefinitionId": null,
    "status": "normal",
    "royalties": "0.10"
  },
  {
    "code": "sw_live_szroul_age",
    "title": "Age Of The Gods Roulette",
    "type": "table",
    "defaultInfo": {
      "name": "Age Of The Gods Roulette",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": null,
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "isGRCGame": false,
      "jackpotTypes": null,
      "transferEnabled": false,
      "isFreebetSupported": false,
      "isBonusCoinsSupported": false
    },
    "clientFeatures": {},
    "historyRenderType": 1,
    "historyUrl": "http://gc.gaming.skywindgroup.com/roulette/latest/history.html",
    "releaseDate": "2019-04-03T12:49:58.039Z",
    "limitsGroup": null,
    "countries": [],
    "totalBetMultiplier": null,
    "schemaDefinitionId": null,
    "status": "normal",
    "royalties": null
  },
  {
    "code": "sw_al",
    "title": "Amazon Lady",
    "type": "slot",
    "defaultInfo": {
      "name": "Amazon Lady Slot",
      "description": "Sky Wind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "isBonusCoinsSupported": true
    },
    "clientFeatures": {
      "turbo": true,
      "fastPlay": false,
      "turboPlus": true
    },
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2017-03-07T13:51:54.331Z",
    "limitsGroup": null,
    "countries": [],
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": "0.12"
  },
  {
    "code": "sw_al_dp",
    "title": "Amazon Lady",
    "type": "slot",
    "defaultInfo": {
      "name": "Amazon Lady Slot",
      "description": "Sky Wind"
    },
    "info": {},
    "labels": [
      {
        "id": "W4RkGRen",
        "title": "slot",
        "group": "class"
      }
    ],
    "comment": "",
    "providerCode": "DP",
    "providerTitle": "Demo_provider",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2018-02-01T16:45:25.978Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": null,
    "schemaDefinitionId": null,
    "status": "normal",
    "royalties": "0.12"
  },
  {
    "code": "sw_al_phantom",
    "title": "Amazon Lady",
    "type": "slot",
    "defaultInfo": {
      "name": "Amazon Lady Slot",
      "description": "Sky Wind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "PH",
    "providerTitle": "phantom",
    "features": {
      "translations": {
        "zh-cn": {
          "title": "titleEN",
          "description": "description in EN"
        }
      }
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2019-04-10T15:31:10.835Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": null
  },
  {
    "code": "sw_al3",
    "title": "Amazon Lady PROD",
    "type": "slot",
    "defaultInfo": {
      "name": "Amazon Lady Slot",
      "description": "Sky Wind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW3",
    "providerTitle": "Skywind3",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2017-12-19T16:10:42.993Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": "0.10"
  },
  {
    "code": "sw_al_ad",
    "title": "Amazon Lady with cheats",
    "type": "slot",
    "defaultInfo": {
      "name": "Amazon Lady Slot",
      "description": "Sky Wind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "AD",
    "providerTitle": "TestProvider",
    "features": {
      "translations": {
        "zh-cn": {
          "title": "titleEN",
          "description": "description in EN"
        }
      }
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2019-04-11T09:18:01.067Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": null,
    "schemaDefinitionId": null,
    "status": "normal",
    "royalties": "0.12"
  },
  {
    "code": "sw_al_gs2",
    "title": "Amazon Lady with cheats",
    "type": "slot",
    "defaultInfo": {
      "name": "Amazon Lady Slot",
      "description": "Sky Wind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2019-04-25T10:33:38.715Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": "0.12"
  },
  {
    "code": "sw_al2",
    "title": "Amazon Lady with cheats",
    "type": "slot",
    "defaultInfo": {
      "name": "Amazon Lady Slot",
      "description": "Sky Wind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW2",
    "providerTitle": "Skywind2",
    "features": {
      "translations": {
        "de": {
          "title": "titleDE",
          "description": "description in DE"
        },
        "zh-cn": {
          "title": "titleEN",
          "description": "description in EN"
        }
      },
      "isBonusCoinsSupported": true
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2017-12-19T16:10:20.769Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": "0.10"
  },
  {
    "code": "sw_al_gs",
    "title": "Amazon Lady with cheats",
    "type": "slot",
    "defaultInfo": {
      "name": "Amazon Lady Slot",
      "description": "Sky Wind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2019-04-25T10:31:26.498Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": "0.12"
  },
  {
    "code": "sw_dztest",
    "title": "Amazon Teddy",
    "type": "slot",
    "defaultInfo": {
      "name": "DZ_TEST",
      "description": "DZ_LIMIT_TEST"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2019-02-18T11:27:37.561Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": null
  },
  {
    "code": "qos_pt_hb",
    "title": "A Night Out",
    "type": "slot",
    "defaultInfo": {
      "name": "A Night Out",
      "description": "FLC"
    },
    "info": {},
    "labels": [],
    "comment": "",
    "providerCode": "FLC-PT",
    "providerTitle": "Playtech2",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2017-05-04T09:51:28.123Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": null,
    "schemaDefinitionId": null,
    "status": "normal",
    "royalties": "0.10"
  },
  {
    "code": "sw_af",
    "title": "Asian Fantasy",
    "type": "slot",
    "defaultInfo": {
      "name": "Asian Fantasy",
      "description": "Asian Fantasy"
    },
    "info": {},
    "labels": [
      {
        "id": "W4RkGRen",
        "title": "slot",
        "group": "class"
      }
    ],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "isBonusCoinsSupported": false
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2018-02-20T12:18:22.953Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "40",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": "0.10"
  },
  {
    "code": "sw_af2",
    "title": "Asian Fantasy cheats",
    "type": "slot",
    "defaultInfo": {
      "name": "Asian Fantasy",
      "description": "Asian Fantasy"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW2",
    "providerTitle": "Skywind2",
    "features": {
      "isBonusCoinsSupported": true
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2018-10-18T10:37:57.859Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "40",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": null
  },
  {
    "code": "sw_af3",
    "title": "Asian Fantasy prod",
    "type": "slot",
    "defaultInfo": {
      "name": "Asian Fantasy",
      "description": "Asian Fantasy"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW3",
    "providerTitle": "Skywind3",
    "features": {
      "isBonusCoinsSupported": true
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2018-10-18T10:38:19.423Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "40",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": null
  },
  {
    "code": "ec_2365",
    "title": "Autumn Gold DH",
    "type": "external",
    "defaultInfo": {
      "name": "Slot Name",
      "limits": {
        "additionalProp1": {},
        "additionalProp2": {},
        "additionalProp3": {}
      },
      "description": "Slot description"
    },
    "info": {
      "USD": {
        "name": "Game name",
        "description": "Game description"
      }
    },
    "labels": [],
    "comment": null,
    "providerCode": "ec",
    "providerTitle": "eyecon",
    "features": {},
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2019-03-12T14:57:10.137Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": null,
    "schemaDefinitionId": null,
    "status": "normal",
    "royalties": null
  },
  {
    "code": "sw_ar",
    "title": "Aztec Reel",
    "type": "slot",
    "defaultInfo": {
      "name": "Aztec Reel",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "jackpotTypes": [
        "sw-fire-reel-major",
        "sw-reel-mega"
      ]
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2018-01-10T14:30:26.418Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "settings": {
      "jackpotId": {
        "sw-reel-mega": "sw-reel-mega",
        "sw-fire-reel-major": "sw-aztec-reel-major"
      }
    },
    "royalties": "0.10"
  },
  {
    "code": "sw_ar_dp",
    "title": "Aztec Reel",
    "type": "slot",
    "defaultInfo": {
      "name": "Aztec Reel",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "",
    "providerCode": "DP",
    "providerTitle": "Demo_provider",
    "features": {
      "jackpotTypes": [
        "sw-fire-reel-major",
        "sw-reel-mega"
      ]
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2018-02-01T18:37:54.355Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": null,
    "schemaDefinitionId": null,
    "status": "normal",
    "settings": {
      "jackpotId": {
        "sw-reel-mega": "sw-reel-mega",
        "sw-fire-reel-major": "sw-aztec-reel-major"
      }
    },
    "royalties": "0.10"
  },
  {
    "code": "sw_azreeu",
    "title": "Aztec Reel EU",
    "type": "slot",
    "defaultInfo": {
      "name": "Aztec Reel EU",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "jackpotTypes": [
        "sw-reel-mega-eu",
        "sw-aztec-reel-major-eu"
      ]
    },
    "clientFeatures": {},
    "historyRenderType": 1,
    "historyUrl": null,
    "releaseDate": "2018-07-11T09:32:22.581Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "settings": {
      "jackpotId": {
        "sw-reel-mega-eu": "SW-REEL-MEGA-EU",
        "sw-aztec-reel-major-eu": "SW-AZTEC-REEL-MAJOR-EU"
      }
    },
    "royalties": "0.10"
  },
  {
    "code": "sw_azreeu2",
    "title": "Aztec Reel EU cheats",
    "type": "slot",
    "defaultInfo": {
      "name": "Aztec Reel EU",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW2",
    "providerTitle": "Skywind2",
    "features": {
      "jackpotTypes": [
        "sw-reel-mega-eu",
        "sw-aztec-reel-major-eu"
      ]
    },
    "clientFeatures": {},
    "historyRenderType": 1,
    "historyUrl": null,
    "releaseDate": "2018-10-17T11:51:09.929Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "settings": {
      "jackpotId": {
        "sw-reel-mega-eu": "SW-REEL-MEGA-EU",
        "sw-aztec-reel-major-eu": "SW-AZTEC-REEL-MAJOR-EU"
      }
    },
    "royalties": null
  },
  {
    "code": "sw_azreeu3",
    "title": "Aztec Reel EU prod",
    "type": "slot",
    "defaultInfo": {
      "name": "Aztec Reel EU",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW3",
    "providerTitle": "Skywind3",
    "features": {
      "jackpotTypes": [
        "sw-reel-mega-eu",
        "sw-aztec-reel-major-eu"
      ]
    },
    "clientFeatures": {},
    "historyRenderType": 1,
    "historyUrl": null,
    "releaseDate": "2018-10-17T11:51:27.131Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "settings": {
      "jackpotId": {
        "sw-reel-mega-eu": "SW-REEL-MEGA-EU",
        "sw-aztec-reel-major-eu": "SW-AZTEC-REEL-MAJOR-EU"
      }
    },
    "royalties": null
  },
  {
    "code": "sw_ar3",
    "title": "Aztec Reel prod",
    "type": "slot",
    "defaultInfo": {
      "name": "Aztec Reel",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW3",
    "providerTitle": "Skywind3",
    "features": {
      "jackpotTypes": [
        "sw-fire-reel-major",
        "sw-reel-mega"
      ]
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2018-02-14T07:49:54.385Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "settings": {
      "jackpotId": {
        "sw-reel-mega": "sw-reel-mega",
        "sw-fire-reel-major": "sw-aztec-reel-major"
      }
    },
    "royalties": "0.10"
  },
  {
    "code": "sw_ar2",
    "title": "Aztec Reel with cheats",
    "type": "slot",
    "defaultInfo": {
      "name": "Aztec Reel",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW2",
    "providerTitle": "Skywind2",
    "features": {
      "jackpotTypes": [
        "sw-fire-reel-major",
        "sw-reel-mega"
      ]
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2018-02-14T07:49:35.806Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "50",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "settings": {
      "jackpotId": {
        "sw-reel-mega": "sw-reel-mega",
        "sw-fire-reel-major": "sw-aztec-reel-major"
      }
    },
    "royalties": "0.10"
  },
  {
    "code": "sw_azre",
    "title": "Aztec Respin",
    "type": "slot",
    "defaultInfo": {
      "name": "Aztec Respin",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "isBonusCoinsSupported": true
    },
    "clientFeatures": {},
    "historyRenderType": 1,
    "historyUrl": null,
    "releaseDate": "2019-07-22T13:42:28.344Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "20",
    "schemaDefinitionId": null,
    "status": "normal",
    "royalties": null
  },
  {
    "code": "sw_bbdf",
    "title": "Ba Ba Da Fa",
    "type": "slot",
    "defaultInfo": {
      "name": "Ba Ba Da Fa",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "isGRCGame": true,
      "jackpotTypes": [
        "sw-srt-challenge",
        "sw-srt-tournament"
      ]
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2017-08-30T11:38:12.023Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "1",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "settings": {
      "jackpotId": {
        "sw-srt-challenge": "sw-srt-challenge",
        "sw-srt-tournament": "sw-srt-tournament"
      }
    },
    "royalties": "0.12"
  },
  {
    "code": "sw_bac",
    "title": "Baccarat",
    "type": "table",
    "defaultInfo": {
      "name": "Baccarat",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "isGRCGame": false,
      "jackpotTypes": null,
      "transferEnabled": false,
      "isFreebetSupported": false,
      "isBonusCoinsSupported": true
    },
    "clientFeatures": {},
    "historyRenderType": 1,
    "historyUrl": "",
    "releaseDate": "2018-10-30T11:55:57.497Z",
    "limitsGroup": null,
    "countries": [],
    "totalBetMultiplier": null,
    "schemaDefinitionId": null,
    "status": "normal",
    "royalties": null
  },
  {
    "code": "sw_bac2",
    "title": "Baccarat cheats",
    "type": "table",
    "defaultInfo": {
      "name": "Baccarat",
      "description": "Skywind"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "isBonusCoinsSupported": true
    },
    "clientFeatures": {},
    "historyRenderType": 1,
    "historyUrl": null,
    "releaseDate": "2019-03-20T13:09:58.930Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": null,
    "schemaDefinitionId": null,
    "status": "normal",
    "royalties": null
  },
  {
    "code": "sw_mwol",
    "title": "Maya Wheel of Luck",
    "type": "slot",
    "defaultInfo": {
      "name": "Maya Wheel of Luck",
      "description": "Sky Wind"
    },
    "info": {},
    "labels": [
      {
        "id": "zx3Ybq1v",
        "title": "html5",
        "group": "platform"
      },
      {
        "id": "W4RkGRen",
        "title": "slot",
        "group": "class"
      }
    ],
    "comment": "edited",
    "providerCode": "SW",
    "providerTitle": "Skywind",
    "features": {
      "isBonusCoinsSupported": true
    },
    "clientFeatures": {},
    "historyRenderType": 0,
    "historyUrl": null,
    "releaseDate": "2017-04-05T09:03:29.575Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": "21",
    "schemaDefinitionId": "W4RkGRen",
    "status": "normal",
    "royalties": "0.12"
  },
  {
    "code": "sw_fufarm_jp",
    "title": "Fu Farm JP",
    "type": "action",
    "defaultInfo": {
      "name": "Fu Farm JP",
      "description": "Fu Farm Jackpot description"
    },
    "info": {},
    "labels": [],
    "comment": "edited",
    "providerCode": "SW2",
    "providerTitle": "Skywind2",
    "features": {
      "jackpotTypes": [
        "sw-fufish-jp"
      ],
      "transferEnabled": true,
      "isBonusCoinsSupported": true
    },
    "clientFeatures": {},
    "historyRenderType": 1,
    "historyUrl": null,
    "releaseDate": "2018-08-08T13:09:07.046Z",
    "limitsGroup": null,
    "countries": null,
    "totalBetMultiplier": null,
    "schemaDefinitionId": null,
    "status": "normal",
    "settings": {
      "jackpotId": {
        "sw-fufish-jp": "FUFARM-JP"
      }
    },
    "royalties": "0.10"
  }
].map(( item ) => mapToGameItem(item));

import { Injectable } from '@angular/core';
import { combineLatest, merge, Observable, of, ReplaySubject, Subject, zip } from 'rxjs';
import { createLabelIntersection, GameSelectItem } from './game-select-item/game-select-item.model';
import {
  map,
  mergeMap,
  startWith,
  tap,
} from 'rxjs/operators';
import { gameSelectItemTypes } from './game-select-item/game-select-item-types';
import { GameCategoryItemInterface, gameCategoryItemTypes } from './game-category-item.model';
import { GameLabel } from './game.model';

export interface SelectedGamesExtraColumnScheme {
  title: string;
  type: string;
  valuesAccessor: ( game: any ) => any[];
  params: Object;
}

export const extraColumnTypes = {
  coinValue: 'coinValue',
  gameCoeff: 'gameCoeff',
};

const reduceObjectIdToObjectHash = ( hash: any, obj: any ) => ({ ...hash, [obj.id]: obj });

@Injectable()
export class GamesSelectManagerService {
  // subjects for components subscriptions
  availableGames$: ReplaySubject<GameSelectItem[]> = new ReplaySubject(1);
  availableLabels$: ReplaySubject<GameSelectItem[]> = new ReplaySubject(1);
  selectedItems$: ReplaySubject<GameSelectItem[]> = new ReplaySubject(1);
  gamesTotal$: Subject<number> = new ReplaySubject(1);
  labelsTotal$: Subject<number> = new ReplaySubject(1);
  selectedTotal$: Subject<number> = new ReplaySubject(1);

  addedItems$: Subject<GameSelectItem[]> = new Subject();
  removedItems$: Subject<GameSelectItem[]> = new Subject();
  createdIntersection$: Subject<GameSelectItem> = new Subject();
  intersectionDestroyed$: Subject<{ remove: boolean, uncheckLabels: boolean }> = new Subject();

  checkedAvailableGamesCount$: Subject<number> = new Subject();
  checkedAvailableCount$: Subject<number> = new Subject();
  checkedSelectedGamesCount$: Subject<number> = new Subject();
  checkedSelectedCount$: Subject<number> = new Subject();

  // internal streams for data manipulation
  private categoryItemsStream = new Subject<GameCategoryItemInterface[]>();
  private availableGameItemsStream = new Subject<GameSelectItem[]>();
  private availableLabelItemsStream = new Subject<GameSelectItem[]>();
  private selectedGameItemsStream = new Subject<GameSelectItem[]>();

  private addStream: Subject<boolean> = new Subject();
  private removeStream: Subject<boolean> = new Subject();
  private refreshSelectedStream: Subject<boolean> = new Subject();
  private addIntersectionStream: Subject<GameSelectItem> = new Subject();

  // internal cached data for proper service working
  private games: GameSelectItem[] = [];
  private labels: GameSelectItem[] = [];
  private selected: GameSelectItem[] = [];

  private checkedAvailableGamesCount:
    { games: number, labelGames: number, intersectionGames: number, labels: number } = {
    games: 0, labelGames: 0, intersectionGames: 0, labels: 0
  };
  private checkedSelectedGamesCount:
    { games: number, labelGames: number, intersectionGames: number, labels: number, intersections: number } = {
    games: 0, labelGames: 0, intersectionGames: 0, labels: 0, intersections: 0
  };

  private static calculateTotalGames( items: GameSelectItem[] ) {
    const reducer = ( total: number, item: GameSelectItem ) => {
      let subTotal = 1;
      if (item.isLabel) {
        subTotal = item.items.length || 0;
      } else if (item.type === gameSelectItemTypes.INTERSECTION) {
        // subTotal = item.data['games'].length || 0;
        subTotal = item.intersection.games.length || 0; // possible intersections bug
      }
      return total + subTotal;
    };
    return items.reduce(reducer, 0);
  }

  private static clearPreviousCheckedState( games: GameSelectItem[], labels: GameSelectItem[] ) {
    let clearChecked = ( item: GameSelectItem ) => {
      item.checked = false;
    };
    games.map(clearChecked);
    labels.map(clearChecked);
  }

  constructor() {
    this.initLabelGames();
    this.initAvailableStreams();
    this.initSelectedStreams();
  }

  setupCategoryItems( items: GameCategoryItemInterface[] ) {
    // this.items = items;
    this.categoryItemsStream.next(items);
  }

  /**
   * Creates GameSelectItem from games (using only in EMBEDDED mode)
   */
  setupSelectedGames( games: GameSelectItem[] ) {
    this.setupSelectedItems(games);
  }

  setupAvailableGames( games: GameSelectItem[] ) {
    this.games = games;
    this.availableGameItemsStream.next(games);
  }

  setupAvailableLabels( labels: GameSelectItem[] ) {
    this.labels = labels;
    this.availableLabelItemsStream.next(labels);
  }

  setupSelectedItems( items: GameSelectItem[] ) {
    this.selected = items;
    this.selectedGameItemsStream.next(items);
  }

  refreshSelected() {
    this.refreshSelectedStream.next(true);
  }

  addToSelectedItems() {
    this.addStream.next(true);
  }

  removeFromSelectedItems() {
    this.removeStream.next(true);
  }

  createIntersection( labels: GameSelectItem[] ) {
    this.createdIntersection$.next(createLabelIntersection(labels));
  }

  removeAvailableIntersection( uncheckLabels: boolean = false ) {
    this.intersectionDestroyed$.next({ remove: true, uncheckLabels });
  }

  addIntersection( intersection: GameSelectItem ) {
    this.addIntersectionStream.next(intersection);
    this.removeAvailableIntersection(true);
  }

  calculateCheckedAvailableGames( games: GameSelectItem[] ) {
    this.checkedAvailableGamesCount.games = games.length;
    this.notifyAboutCheckedAvailableGames();
  }

  calculateCheckedAvailableLabels( labels: GameSelectItem[] ) {
    this.checkedAvailableGamesCount.labelGames = labels.length === 1 ? labels[0].items.length : 0;
    this.checkedAvailableGamesCount.labels = labels.length;
    this.notifyAboutCheckedAvailableGames();
  }

  calculateCheckedAvailableIntersection( item: GameSelectItem | null ) {
    this.checkedAvailableGamesCount.intersectionGames = item !== null ? item.intersection.games.length : 0;
    this.notifyAboutCheckedAvailableGames();
  }

  calculateCheckedSelectedGames( games: GameSelectItem[] ) {
    this.checkedSelectedGamesCount.games = games.length;
    this.notifyAboutCheckedSelectedGames();
  }

  calculateCheckedSelectedLabels( labels: GameSelectItem[] ) {
    this.checkedSelectedGamesCount.labelGames = labels.reduce(( total, label ) => (total + label.items.length), 0);
    this.checkedSelectedGamesCount.labels = labels.length;
    this.notifyAboutCheckedSelectedGames();
  }

  calculateCheckedSelectedIntersections( intersections: GameSelectItem[] ) {
    this.checkedSelectedGamesCount.intersectionGames =
      intersections.reduce(( total, intersection ) => (total + intersection.intersection.games.length), 0);
    this.checkedSelectedGamesCount.intersections = intersections.length;
    this.notifyAboutCheckedSelectedGames();
  }

  filterGames( items: GameSelectItem[], search: string ) {
    return items.filter(( item: GameSelectItem ) => {
      let { title, code, labels } = item.gameInfo;
      let needle = search.toLowerCase();

      return title.toLowerCase().indexOf(needle) > -1
        || code.toLowerCase().indexOf(needle) > -1
        || labels.map(label => label.title.toLowerCase())
          .filter(text => text.indexOf(needle) > -1).length > 0;
    });
  }

  filterLabels( items: GameSelectItem[], search: string ) {
    return items.filter(( item: GameSelectItem ) => item.label.title.toLowerCase().indexOf(search.toLowerCase()) > -1);
  }

  private initAvailableStreams() {
    this.initAvailableGamesStreams();
    this.initAvailableLabelsStreams();
  }

  private initAvailableGamesStreams() {
    const games$ = this.availableGameItemsStream.pipe(
      tap(games => this.games = games),
      map(( games: GameSelectItem[] ) => ({ games, selected: this.selected }))
    );
    const selected$ = this.selectedGameItemsStream.pipe(
      tap(selected => this.selected = selected),
      map(( selected: GameSelectItem[] ) => ({ selected, games: this.games }))
    );

    const filteredAvailableGames$: Observable<GameSelectItem[]> = merge(games$, selected$)
      .pipe(
        startWith({ games: this.games, selected: this.selected }),
        mergeMap(( { games, selected } ): Observable<GameSelectItem[]> => {
          const filtered = games.filter(item => selected.map(game => game.id).indexOf(item.id) === -1);
          this.gamesTotal$.next(filtered.length);
          return of(filtered);
        })
      );

    filteredAvailableGames$.subscribe(games => {
      this.availableGames$.next(games);
    });
  }

  private initAvailableLabelsStreams(): void {
    const labels$ = this.availableLabelItemsStream.pipe(
      tap(labels => {
        this.labels = labels;
      }),
      map(labels => ({ labels, selected: this.selected }))
    );
    const selected$ = this.selectedGameItemsStream.pipe(
      tap(selected => {
        this.selected = selected;
      }),
      map(selected => ({ selected, labels: this.labels }))
    );

    const filteredAvailableLabels$ = merge(labels$, selected$)
      .pipe(
        startWith({ labels: this.labels, selected: this.selected }),
        map(( { labels, selected } ) => {
          // hiding selected labels
          return labels.filter(( { id } ) => selected.find(( { id: selectedId } ) => id === selectedId) !== null);
        }),
        tap(labels => {
          this.labelsTotal$.next(labels.length);
        })
      );

    // const filteredAvailableLabels$ = merge(labels$, selected$)
    //   .pipe(
    //     startWith({labels: this.labels, selected: this.selected}),
    //     mergeMap(({labels, selected}) => {
    //
    //       let realAvailable$: Observable<GameSelectItem[]> = of(labels)
    //         .pipe(
    //           map(items => items.filter(({id}) => // hiding selected labels
    //             selected.map(selectedItem => selectedItem.id).indexOf(id) === -1
    //             )
    //           ),
    //           flatMap((data: GameSelectItem[]) => of(data))
    //         );
    //
    //       realAvailable$.pipe(count()).subscribe(total => this.labelsTotal$.next(total));
    //       return realAvailable$.pipe(
    //         combineAll(),
    //         defaultIfEmpty([])
    //       );
    //     })
    //   );

    filteredAvailableLabels$.subscribe(( labels: GameSelectItem[] ) => {
      this.availableLabels$.next(labels);
    });
  }

  private initSelectedStreams() {

    this.populateCategoryGameItems();

    const added$ = this.addStream.pipe(map(
      ( added: boolean ) => {
        const available = [...this.games, ...this.labels];
        let selected = this.selected;

        if (added) {
          let addedItems = [
            ...available
              .filter(item => this.selected.indexOf(item) === -1)
              .filter(item => {
                let checked = item.checked;
                item.checked = false;
                return checked;
              })
          ];

          const labelsWereAdded: boolean = addedItems.some(item => item.isLabel);
          if (labelsWereAdded) {
            this.removeAvailableIntersection(true);
          }
          this.addedItems$.next(addedItems);

          selected = [...selected, ...addedItems];
        }
        return { selected };
      }
    ));

    const removed$ = this.removeStream.pipe(map(
      ( removed: boolean ) => {
        let selected = this.selected;

        if (removed) {
          let removedItems = selected.filter(item => item.checked);
          selected = selected.filter(item => {
            let itemStillSelected = !item.checked;
            if (!itemStillSelected) {
              item.toggleCheck();
            }
            return itemStillSelected;
          });

          this.removedItems$.next(removedItems);
        }
        return { selected };
      }
    ));

    merge(added$, removed$).subscribe(( { selected } ) => this.setupSelectedItems(selected));

    const refreshed$ = this.refreshSelectedStream
      .pipe(map(() => {
        const selected = this.selected;
        return { selected };
      }));
    refreshed$.subscribe(( { selected } ) => this.setupSelectedItems(selected));

    const intersectionAdded$ = this.addIntersectionStream.pipe(map(
      ( item ) => {
        const selected = this.selected;
        selected.push(item);
        return { selected };
      }
    ));
    intersectionAdded$.subscribe(( { selected } ) => this.setupSelectedItems(selected));

    this.selectedGameItemsStream.subscribe(( items ) => {
      this.selectedItems$.next(items);
      this.selectedTotal$.next(GamesSelectManagerService.calculateTotalGames(items));
    });

  }


  /**
   * Creates items for GameSelectItem type=label (items contains array of GameSelectItem with type=game)
   * same for GameSelectItem type=provider
   */
  private initLabelGames() {
    zip(this.availableGameItemsStream, this.availableLabelItemsStream).subscribe(( [games, labels] ) => {
      let labelsHash: { [key: string]: any } = labels
        .map(label => {
          label.items = [];

          return label;
        })
        .reduce(reduceObjectIdToObjectHash, {});
      games.forEach(( game: GameSelectItem ) => {
        if (game) {
          (game.getGameLabels() || []).forEach(( label: GameLabel ) => {
            if (label && label.id && label.id in labelsHash) {
              labelsHash[label.id].addGameToLabel(game);
            }
          });
        }
      });
    });
  }

  private populateCategoryGameItems() {
    combineLatest([
      this.categoryItemsStream,
      this.availableGameItemsStream,
      this.availableLabelItemsStream
    ]).subscribe(( [items, games, labels] ) => {
      let selected = this.sanitizeCategoryItems(games, labels, items);
      GamesSelectManagerService.clearPreviousCheckedState(games, labels);
      this.setupSelectedItems(selected);
    });
  }

  /**
   *  Method which replaces category items to gameSelectItems with data from available games and labels
   *
   */
  private sanitizeCategoryItems( games: GameSelectItem[], labels: GameSelectItem[], items: GameCategoryItemInterface[] ) {
    let gamesHash: { [id: string]: GameSelectItem } = games.reduce(reduceObjectIdToObjectHash, {});
    let labelsHash: { [id: string]: GameSelectItem } = labels.reduce(reduceObjectIdToObjectHash, {});

    const replaceCategoryItems = ( item: GameCategoryItemInterface ): GameSelectItem => {
      let result: GameSelectItem;
      switch (item.type) {
        case gameCategoryItemTypes.GAME:
          if (item.id && item.id in gamesHash) {
            result = gamesHash[item.id];
          } else {
            result = new GameSelectItem({
              id: item.id,
              type: gameSelectItemTypes.CORRUPTION,
              data: item,
            });
          }
          break;

        case gameCategoryItemTypes.LABEL:
        case gameCategoryItemTypes.PROVIDER:
          if (item.id && item.id in labelsHash) {
            result = labelsHash[item.id];
          } else {
            result = new GameSelectItem({
              id: item.id,
              type: gameSelectItemTypes.CORRUPTION,
              data: item,
            });
          }
          break;

        case gameCategoryItemTypes.INTERSECTION:
          let intersectionItems = (item.items || []).map(intersectionItem => replaceCategoryItems(intersectionItem));
          result = createLabelIntersection(intersectionItems);
          result.id = item.id;
          break;

        default:
          throw new Error('Unknown gameCategoryItemType');
      }

      return result;
    };

    return items.map(replaceCategoryItems);
  }

  private notifyAboutCheckedSelectedGames() {
    const { games, labelGames, intersectionGames, labels, intersections } = this.checkedSelectedGamesCount;
    const totalCount = games + labelGames + intersectionGames;
    this.checkedSelectedGamesCount$.next(totalCount);
    this.checkedSelectedCount$.next(totalCount + labels + intersections);
  }

  private notifyAboutCheckedAvailableGames() {
    const { games, labelGames, intersectionGames, labels } = this.checkedAvailableGamesCount;
    const totalCount = games + labelGames + intersectionGames;
    this.checkedAvailableGamesCount$.next(totalCount);
    this.checkedAvailableCount$.next(totalCount + labels);
  }

}

import { Pipe, PipeTransform } from '@angular/core';

import { GamesSelectManagerService } from './games-select-manager.service';
import { GameSelectItem } from './game-select-item/game-select-item.model';
import { gameSelectItemType, gameSelectItemTypes } from './game-select-item/game-select-item-types';

@Pipe({
    name: 'filtered',
    standalone: false
})
export class FilteredPipe implements PipeTransform {

  constructor(
    private service: GamesSelectManagerService
  ) {
  }

  transform(items: GameSelectItem[], search: string, type: gameSelectItemType): any {
    if (!items) {
      return [];
    }
    if (!search) {
      return items;
    }

    return type === gameSelectItemTypes.GAME
      ? this.service.filterGames(items, search)
      : this.service.filterLabels(items, search);
  }
}

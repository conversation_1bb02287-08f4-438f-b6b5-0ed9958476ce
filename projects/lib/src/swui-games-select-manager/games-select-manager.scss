
.segments {
  display: flex;
  overflow-x: auto;

  &__item {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 400px;

    &--middle {
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 0;
      width: 160px;
      min-width: auto;
      padding: 0 20px;
      box-sizing: border-box;
    }
  }

  &__fake-tab {
    margin-bottom: 12px;
    height: 48px;
    display: flex;
    align-items: center;
  }

  &__button {
    margin-top: 20px;
    padding: 0;
    text-transform: uppercase;

    &-name,
    &-value {
      font-size: 14px;
    }

    &.intersection {
      .intersection-name {
        font-size: 12px;
      }

      .intersection-value {
        font-size: 10px;
      }
    }

  }

  &__btn-icon {
    font-size: 18px;
    height: 18px;
    width: 18px;
    margin-bottom: 1px;
  }

  &--embedded {
    .segments {
      &__button {
        width: 46px;
        min-width: 46px;
      }

      &__item {
        &--middle {
          width: 86px;
        }
      }
    }
  }
}

.mat-cell {
  padding-bottom: 2px;
  padding-top: 2px;
}

.mat-cell.cdk-drag-handle:not(.handle) {
  width: 0;
}

.handle {
  width: 16px;
  min-width: 16px;
  padding: 0 !important;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAYAAAANCAYAAACKCx+LAAABS2lUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4KPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS42LWMxNDIgNzkuMTYwOTI0LCAyMDE3LzA3LzEzLTAxOjA2OjM5ICAgICAgICAiPgogPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4KICA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIi8+CiA8L3JkZjpSREY+CjwveDp4bXBtZXRhPgo8P3hwYWNrZXQgZW5kPSJyIj8+nhxg7wAAACZJREFUGJVj/P//vwkDNlBiXPJ/4/+NJqj0fxNGOumIHZSuwqoDAMKBbKoe7/dzAAAAAElFTkSuQmCC) no-repeat center;
  cursor: grab;

  & + td.mat-cell {
    padding-left: 8px;
  }
}

.label {
  display: inline-block;
  line-height: 28px;
  background-color: #ECEEF1;
  border-radius: 4px;
  padding: 0 10px;
  margin: 2px 4px 2px 0;

  &:last-child {
    margin-right: 0;
  }
}

/// ----------------------------------------------------------------------------------------
/// ----------------------------------------------------------------------------------------
/// ----------------------------------------------------------------------------------------


tr.mat-row, tr.mat-footer-row {
  height: 48px;
}

th.mat-header-cell,
td.mat-cell,
td.mat-footer-cell {
  &:first-of-type {
    padding-left: 24px;
  }

  padding: 0;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}

.bordered {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
}

.table-games {
  &__header {
    display: flex;
    align-items: center;
    height: 56px;
    padding: 0 24px;
    border-bottom: 1px solid rgba(0, 0, 0, .12);
    box-sizing: border-box;
  }

  &__panel-title {
    font-size: 14px;
  }

  &__game {
    max-width: 350px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
  }

  &__body {
    height: calc(100% - 56px);
    overflow: auto;

    &.cdk-drop-list-dragging {
      &.element-row:not(.drag-placeholder) {
        transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
      }
    }
  }

  &__search {
    width: calc(100% - 110px);
    max-width: 330px;
    margin-left: auto;

    &--full {
      width: 100%;
      max-width: 100%;
      margin-left: 0;
    }
  }

  table {
    width: 100%;
    background: transparent;
    border-collapse: collapse;

    td {
      font-size: 14px;
    }

    th {
      background-color: #fff;

      &:first-child {
        border-top-left-radius: 4px;
      }

      &:last-child {
        border-top-right-radius: 4px;
      }
    }
  }
}

.drag-placeholder {
  padding-left: 24px;
  height: 48px;
  border: #3f51b5 2px dashed;
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.drag-preview {
  background-color: white;
  padding: 10px;
  opacity: 0.7;
}

.mat-expansion-panel {
  box-shadow: none !important;
  border-bottom: 1px solid;
  border-top: 1px solid;
  border-radius: 0 !important;
  padding: 0 2px;
  border-color: #E4E2DE;

  &-body {
    border-top: 1px solid #E4E2DE;
  }

  .mat-list {
    &-item {
      font-size: 14px;
      height: 36px;
    }

    &-item-content {
      padding: 0 !important;
    }
  }
}

.mat-tab-header {
  .mat-tab-label {
    min-width: auto;
    padding: 0 20px;
    opacity: 1;
    font-weight: 300;

    &.mat-tab-label-active {
      font-weight: 500;
    }
  }
}

.intersection {
  padding-top: 0;
  background: rgba(0, 0, 0, .03);

  &__item {
    height: auto !important;
    min-height: 36px;
    padding: 4px 0 !important;
    border-left: 1px solid rgba(0, 0, 0, .12);
    border-bottom: 1px solid rgba(0, 0, 0, .12);
  }

  &__name {
    display: flex;
    align-items: center;
    min-height: 24px;
    min-width: 100px;
    margin-right: 10px;
    line-height: 1.2;
  }

  &__labels {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    margin-left: auto;
  }

  &__chip {
    font-size: 12px !important;
    min-height: 24px !important;
    margin: 2px;
  }
}

mat-chip {
  display: inline-flex;
  padding: 7px 12px;
  border-radius: 16px;
  align-items: center;
  cursor: default;
  height: 1px;
  font-size: 14px !important;
  min-height: 28px !important;
}

.corrupted {
  td.mat-cell {
    color: red !important;
  }
}

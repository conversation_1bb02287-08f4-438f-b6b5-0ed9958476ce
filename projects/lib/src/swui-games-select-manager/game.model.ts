export interface LimitsByCurrencyCode {
  [field: string]: {};
}

export interface GameLabel {
  id?: string;
  group: string;
  title: string;
}

export interface GameDescription {
  name: string;
  description: string;
  // field - images, screen shots etc.
  [field: string]: any;
}

export interface GameDescriptionByLocale {
  [field: string]: GameDescription;
}

export interface GameSettings {
  transferEnabled?: boolean;
  jackpotId?: any;
  [field: string]: any;
}

export interface GameFeatures {
  jackpotTypes?: string[];
  isGRCGame?: boolean;
  isFreebetSupported?: boolean;
  isBonusCoinsSupported?: boolean;
  transferEnabled?: boolean;
  [field: string]: any;
}

export interface GameShortInfo {
  code: string;
  status: string;
  royalties?: number;
  settings?: GameSettings;
}

export interface GameInfo extends GameShortInfo {
  providerName: string;
  providerCode: string;
  providerTitle: string;
  title: string;
  type: string;
  defaultInfo: GameDescription;
  info: GameDescriptionByLocale;
  limits: LimitsByCurrencyCode;
  labels: GameLabel[];
  comment?: string;
  features?: GameFeatures;
  coeff?: number;
}

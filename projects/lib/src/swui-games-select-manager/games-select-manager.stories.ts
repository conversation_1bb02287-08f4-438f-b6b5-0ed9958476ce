/* eslint-disable:quotemark */
import { moduleMetadata, storiesOf } from '@storybook/angular';
import { SwuiGamesSelectManagerModule } from './games-select-manager.module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { I18nModule } from '../i18n.module';
import { availableTabNames, SwuiGamesSelectManagerComponent } from './games-select-manager.component';

import { games } from './.storybook/games.data';
import { labels } from './.storybook/game-labels.data';
import { createFromLabel, fromGameInfo } from './game-select-item/game-select-item.model';
import { GameInfo } from './game.model';
import { MatTableModule } from '@angular/material/table';

const EN = require('./.storybook/locale.json');

storiesOf('Games Select Manager', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        MatTableModule,
        BrowserAnimationsModule,
        I18nModule.forRoot({ translations: { en: EN } }),
        SwuiGamesSelectManagerModule,
      ],
      providers: [],
      declarations: []
    })
  )
  .add('Games Only', () => ({
    component: SwuiGamesSelectManagerComponent,
    template: `
                <div style="padding: 32px; height: 100vh; overflow: auto;">
                    <div class="mat-card">
                        <lib-swui-games-select-manager [availableGames]="games" [availableLabels]="labels"
                                                 [embedded]="embedded" [hideLabelsTab]="hideLabelsTab">
                        </lib-swui-games-select-manager>
                    </div>
                </div>`,
    props: {
      games: games.map(( game: GameInfo ) => fromGameInfo(game)),
      labels: labels.map(label => createFromLabel(label)),
      selected: [],
      embedded: true,
      hideLabelsTab: true,
    },
  })).add('Games and Labels', () => ({
    component: SwuiGamesSelectManagerComponent,
    template: `
                <div style="padding: 32px; height: 100vh; overflow: auto;">
                    <div class="mat-card">
                        <lib-swui-games-select-manager
                          [availableGames]="games"
                          [availableLabels]="labels"
                          [firstShowTab]="firstShowTab">
                        </lib-swui-games-select-manager>
                    </div>
                </div>`,
    props: {
      games: games.map(( game: GameInfo ) => fromGameInfo(game)),
      labels: labels.map(label => createFromLabel(label)),
      selected: [],
      firstShowTab: availableTabNames.LABELS,
    },
  }))
  .add('Embedded', () => ({
    component: SwuiGamesSelectManagerComponent,
    template: `
                <div style="padding: 32px; height: 100vh; overflow: auto;">
                    <div class="mat-card">
                        <lib-swui-games-select-manager
                          [embedded]="embedded"
                          [availableGames]="games"
                          [availableLabels]="labels"
                          [firstShowTab]="firstShowTab">
                        </lib-swui-games-select-manager>
                    </div>
                </div>`,
    props: {
      games: games.map(( game: GameInfo ) => fromGameInfo(game)),
      labels: labels.map(label => createFromLabel(label)),
      selected: [],
      embedded: true,
      firstShowTab: availableTabNames.LABELS,
    },
  }))
;

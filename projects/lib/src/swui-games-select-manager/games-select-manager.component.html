<div class="segments" [ngClass]="{'segments--embedded': embedded}">
  <div class="segments__item">
    <mat-tab-group [selectedIndex]="getFirstTabIndex()">
      <mat-tab id="{{ tabs.GAMES }}">
        <ng-template mat-tab-label>
          {{ 'COMPONENTS.GAMES_SELECT_MANAGER.availableGamesTitle' | translate }} ({{ gamesTotal }})
        </ng-template>

        <ng-template matTabContent>
          <sw-available-games [disabled]="disabled" [loading]="availableGamesLoading" [height]="height">
          </sw-available-games>
        </ng-template>
      </mat-tab>

      <mat-tab id="{{ tabs.LABELS }}" *ngIf="!hideLabelsTab">
        <ng-template mat-tab-label>
          {{ 'COMPONENTS.GAMES_SELECT_MANAGER.availableLabelsTitle' | translate }} ({{ labelsTotal }})
        </ng-template>

        <ng-template matTabContent>
          <sw-available-labels [disabled]="disabled" [height]="height"></sw-available-labels>
        </ng-template>
      </mat-tab>
    </mat-tab-group>
  </div>

  <div class="segments__item segments__item--middle">
    <ng-container *ngIf="!embedded; else embeddedModeControls">

      <button
        mat-stroked-button
        class="segments__button"
        [ngClass]="{'intersection': intersection}"
        color="primary"
        [disabled]="disabled || !checkedFullAvailable"
        (click)="addToSelectedGames()">
        <ng-container *ngIf="!intersection; else intersectTpl">
          <span class="segments__button-name">
            {{ 'COMPONENTS.GAMES_SELECT_MANAGER.btnAddGames' | translate }}
          </span>
          <span *ngIf="checkedAvailable && checkedAvailable > 0" class="segments__button-value">
            ({{ checkedAvailable }})
          </span>
        </ng-container>
        <ng-template #intersectTpl>
          <span class="segments__button-name intersection-name">
            {{ 'COMPONENTS.GAMES_SELECT_MANAGER.btnIntersect' | translate }}
          </span>
          <span *ngIf="checkedAvailable && checkedAvailable > 0" class="segments__button-value intersection-value">
            ({{ checkedAvailable }})
          </span>
        </ng-template>
        <mat-icon class="segments__btn-icon">arrow_forward</mat-icon>
      </button>

      <button
        mat-stroked-button
        class="segments__button"
        color="primary"
        [disabled]="disabled || !checkedFullSelected"
        (click)="removeFromSelectedGames()">
        <mat-icon class="segments__btn-icon">arrow_back</mat-icon>
        <span class="segments__button-name">
          {{ 'COMPONENTS.GAMES_SELECT_MANAGER.btnRemoveGames' | translate }}
        </span>
      </button>

    </ng-container>

    <ng-template #embeddedModeControls>

      <button
        mat-stroked-button
        class="segments__button"
        color="primary"
        [disabled]="disabled || !checkedAvailable"
        (click)="addToSelectedGames()">
        <mat-icon class="segments__btn-icon">arrow_forward</mat-icon>
      </button>
      <button
        mat-stroked-button
        class="segments__button"
        color="primary"
        [disabled]="disabled || !checkedSelected"
        (click)="removeFromSelectedGames()">
        <mat-icon class="segments__btn-icon">arrow_back</mat-icon>
      </button>

    </ng-template>
  </div>

  <div class="segments__item">
    <div class="segments__fake-tab">
      {{ selectedGamesTitle | translate }} ({{ selectedGamesTotal }})
    </div>

    <sw-selected-items [extraColumn]="selectedGamesExtraColumn" [disabled]="disabled" [emptyGamesText]="emptyGamesText"
                       [height]="height" (dropListDropped)="onDropListDropped($event)">
    </sw-selected-items>
  </div>
</div>

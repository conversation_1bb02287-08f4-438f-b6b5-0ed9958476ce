import {
  Component,
  ComponentFactoryResolver,
  ComponentRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
  ViewContainerRef
} from '@angular/core';
import { extraColumnTypes, SelectedGamesExtraColumnScheme } from '../games-select-manager.service';
import { CoinValueColumnComponent } from './coin-value-column.component';
import { GameCoeffColumnComponent } from './game-coeff-column.component';
import { DefaultColumnComponent } from './default-column.component';
import { SelectItemGameInfo } from '../game-select-item/game-select-item.model';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: '[extra-column-chooser]',
    template: '<div class="extra-column-chooser" #target></div>',
    standalone: false
})

export class ExtraColumnChooserComponent implements OnInit, OnDestroy {

  @Input() extraColumn: SelectedGamesExtraColumnScheme | undefined;
  @Input() game: SelectItemGameInfo | undefined;

  @Input() set disabled(value: boolean) {
    const valueChanged: boolean = this._disabled !== value;
    this._disabled = value;

    if (valueChanged && this.componentRef && 'instance' in this.componentRef) {
      this.componentRef.instance.disabled = true;
    }
  }

  get disabled(): boolean {
    return this._disabled;
  }

  @Output() valueChange: EventEmitter<any> = new EventEmitter();

  @ViewChild('target', {read: ViewContainerRef, static: true}) private container: ViewContainerRef | undefined;

  private componentRef: ComponentRef<DefaultColumnComponent> | undefined;
  private entryComponentsMap = {
    [extraColumnTypes.coinValue]: CoinValueColumnComponent,
    [extraColumnTypes.gameCoeff]: GameCoeffColumnComponent
  };
  private _disabled = false;

  constructor(
    private resolver: ComponentFactoryResolver,
  ) {

  }

  ngOnInit() {
    this.createComponent();
  }

  ngOnDestroy() {
    if (this.componentRef) {
      this.componentRef.destroy();
    }
  }

  get componentClass(): any {
    if (this.extraColumn) {
      return this.entryComponentsMap[this.extraColumn.type];
    }
  }

  createComponent() {
    if (this.container) {
      this.container.clear();

      const factory = this.resolver.resolveComponentFactory<DefaultColumnComponent>(this.componentClass);
      this.componentRef = this.container.createComponent<DefaultColumnComponent>(factory);

      const data = {
        game: this.game,
        values: this.extraColumn && this.extraColumn.valuesAccessor(this.game),
        params: this.extraColumn && this.extraColumn.params,
        valueChange: this.valueChange,
        disabled: this.disabled,
      };

      this.componentRef.instance.initWithData(data);
    }
  }

}

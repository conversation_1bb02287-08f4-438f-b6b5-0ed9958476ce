import { Component, OnInit } from '@angular/core';
import { DefaultColumnComponent } from './default-column.component';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'sw-coin-value-column',
    templateUrl: 'coin-value-column.component.html',
    styles: [`
    select {
      height: 24px;
    }
  `],
    standalone: false
})
export class CoinValueColumnComponent extends DefaultColumnComponent implements OnInit {
  curcode: string | undefined;

  get coinLimit(): number | undefined {
    if (this.curcode && this.game && 'coins' in this.game && this.game.coins && this.game.coins.length) {
      return this.game.coins[0][this.curcode].coin;
    }
  }

  set coinLimit(coin: number | undefined) {
    if (this.game) {
      this.game.coins = []; // supports only one currency for now
      if (this.curcode && coin) {
        this.game.coins.push({
          [this.curcode]: {coin}
        });
      }
    }
    if (this.valueChange) {
      this.valueChange.emit(coin);
    }
  }

  ngOnInit() {
    this.curcode = this.params['curcode'];
    this.checkAndSetDefaultCoinLimit();
  }

  setLimit(event: Event) {
    let value = (event.target as HTMLSelectElement).value;
    this.coinLimit = parseFloat(value);
  }

  private checkAndSetDefaultCoinLimit() {
    if (this.values && this.values.length > 0) {
      if (this.game && !('coins' in this.game)) {
        this.coinLimit = this.values[0];
      }
    }
  }
}

<div style="display: flex">
  <span *ngIf="game?.coins?.length > 1" class="text-warning mr-5"
        matTooltip="'Warning: multiple currencies are not supported!'">
    <i class="icon-warning text-danger"></i>
  </span><select class="form-control" (change)="setLimit($event)" [disabled]="disabled"
                 [ngClass]="{'disabled': disabled}">
    <option value="{{ limit }}" *ngFor="let limit of values" [selected]="coinLimit === limit">{{ limit }}</option>
  </select>
</div>

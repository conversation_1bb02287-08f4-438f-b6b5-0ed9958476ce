import { EventEmitter } from '@angular/core';
import { SelectItemGameInfo } from '../game-select-item/game-select-item.model';

export abstract class DefaultColumnComponent {
  game?: SelectItemGameInfo;
  values: any[] = [];
  params: { [key: string]: any } = {};
  valueChange?: EventEmitter<any>;
  disabled = false;

  initWithData(data: any) {
    this.game = data && data.game || undefined;
    this.values = data && data.values || [];
    this.params = data && data.params || {};
    this.valueChange = data && data.valueChange || undefined;
    this.disabled = data && data.disabled || false;
  }
}

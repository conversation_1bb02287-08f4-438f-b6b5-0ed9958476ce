import { Component, ElementRef, ViewChild } from '@angular/core';
import { DefaultColumnComponent } from './default-column.component';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'sw-game-coeff-column',
    templateUrl: 'game-coeff-column.component.html',
    styles: [`
    select {
      height: 24px;
    }

    .input-xxs {
      height: 24px;
      padding: 2px 5px;
      font-size: 12px;
      line-height: 1.6666667;
      border-radius: 2px;
    }
  `],
    standalone: false
})
export class GameCoeffColumnComponent extends DefaultColumnComponent {
  @ViewChild('input', {static: true}) public input: ElementRef | undefined;

  max = 1;
  min = 0.01;
  step = 0.01;

  isValueChanged(): boolean {
    return this.coeff !== this.defaultCoeff && !!this.coeff;
  }

  get coeff(): number | undefined {
    return this.game && this.game.coeff;
  }

  set coeff(value: number | undefined) {
    value = this.sanitizeValue(value);
    if (this.game) {
      this.game.coeff = value;
    }
    if (this.input) {
      this.input.nativeElement.value = value;
    }
    if (this.valueChange) {
      this.valueChange.emit(value);
    }
  }

  get defaultCoeff(): number {
    return this.params['defaultCoeff'] || 1;
  }

  valueChanged({target}: Event) {
    this.coeff = target && ('value' in target) ? (target as HTMLInputElement).value : this.input && this.input.nativeElement.value;
  }

  private sanitizeValue(value: number | undefined): number {
    if (value === undefined) {
      value = this.min;
    }
    if (isNaN(value)) {
      value = this.min;
    }
    if (value > this.max) {
      value = this.max;
    }
    if (value < this.min) {
      value = this.min;
    }
    return value;
  }
}

import { SwuiCurrencySymbolPipe } from './swui-currency-symbol.pipe';

describe('SwuiCurrencySymbolPipe', () => {
  let pipe: SwuiCurrencySymbolPipe;

  beforeEach(() => {
    pipe = new SwuiCurrencySymbolPipe();
  });

  it('create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should transform currency into symbol', () => {
    expect(pipe.transform('USD')).toBe('$');
  });

  it('should return currency ig not found', () => {
    expect(pipe.transform('Test')).toBe('Test');
  });
});

import { moduleMetadata, storiesOf } from '@storybook/angular';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { I18nModule } from '../i18n.module';
import { action } from 'storybook/actions';
import moment from 'moment';

import { SwuiDateTimeRangeModule } from './swui-date-time-range.module';
import { SwuiDateTimeRangeComponent } from './swui-date-time-range.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSelectModule } from '@angular/material/select';

const EN = require('./locale.json');

export const template = `
  <mat-form-field appearance="outline" style="width: 500px">
    <mat-label>Test control label</mat-label>
    <lib-swui-date-time-range
      [value]="value"
      [isInline]="isInline"
      [minDate]="minDate"
      [maxDate]="maxDate"
      [hideCustomPeriods]="hideCustomPeriods"
      [formControl]="formControl"
      [fromPlaceholder]="fromPlaceholder"
      [toPlaceholder]="toPlaceholder"
      [smallCustomPeriodsButton]="smallCustomPeriodsButton">
    </lib-swui-date-time-range>
  </mat-form-field>
`;


storiesOf('Date/DateTime Range', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        I18nModule.forRoot({ translations: { en: EN } }),
        ReactiveFormsModule,
        MatCheckboxModule,
        MatSelectModule,
        MatFormFieldModule,
        SwuiDateTimeRangeModule,
      ],
    })
  )
  .add('default', () => ({
    component: SwuiDateTimeRangeComponent,
    template,
    props: {
      fromPlaceholder: 'From date',
      toPlaceholder: 'To date',
      value: {
        from: moment.utc().add(-1, 'd'),
        to: moment.utc().add(1, 'd')
      },
    },
  }))
  .add('disabled', () => ({
    template: `
        <mat-checkbox ngModel="true" #disableCtrl="ngModel">Disabled</mat-checkbox>
        <mat-form-field appearance="outline">
          <lib-swui-date-time-range
            ngModel
            minDate="minDate"
            maxDate="maxDate"
            [isInline]="isInline"
            [fromPlaceholder]="'From Date'"
            [toPlaceholder]="'To Date'"
            [hideCustomPeriods]="hideCustomPeriods"
            [hideTime]="hideTime"
            [disabled]="disableCtrl.value"
            [smallCustomPeriodsButton]="smallCustomPeriodsButton">
          </lib-swui-date-time-range>
        </mat-form-field>
      `,
  }))
  .add('Value', () => ({
    component: SwuiDateTimeRangeComponent,
    props: {
      formControl: new FormControl({ from: moment.utc(), to: moment.utc().add(3, 'd') }),
      fromPlaceholder: 'From date',
      toPlaceholder: 'To date',
      change: action('test'),
    },
    template,
  }))
  .add('Maxdate', () => ({
    component: SwuiDateTimeRangeComponent,
    props: {
      formControl: new FormControl(null),
      maxDate: moment.utc().add(3, 'day'),
      fromPlaceholder: 'From date',
      toPlaceholder: 'To date',
    },
    template: template,
  }))
  .add('Mindate', () => ({
    component: SwuiDateTimeRangeComponent,
    props: {
      formControl: new FormControl(null),
      minDate: moment.utc().add(-3, 'day'),
      fromPlaceholder: 'From date',
      toPlaceholder: 'To date',
    },
    template: template,
  }))
  .add('Inline', () => ({
    component: SwuiDateTimeRangeComponent,
    props: {
      formControl: new FormControl(null),
      isInline: true,
      hideTime: true,
      fromPlaceholder: 'From date',
      toPlaceholder: 'To date',
    },
    template: template,
  }))
  .add('mat-form-field', () => ({
    component: SwuiDateTimeRangeComponent,
    props: {
      formControl: new FormControl(null),
      fromPlaceholder: 'From date',
      toPlaceholder: 'To date',
    },
    template: template,
  }))
  .add('Inline, Hide custom periods', () => ({
    component: SwuiDateTimeRangeComponent,
    props: {
      formControl: new FormControl(null),
      isInline: true,
      hideTime: true,
      fromPlaceholder: 'From date',
      toPlaceholder: 'To date',
      hideCustomPeriods: true,
    },
    template: template,
  }))
  .add('Small periods button', () => ({
    component: SwuiDateTimeRangeComponent,
    props: {
      formControl: new FormControl(null),
      isInline: false,
      hideTime: true,
      fromPlaceholder: 'From date',
      toPlaceholder: 'To date',
      hideCustomPeriods: false,
      smallCustomPeriodsButton: true
    },
    template: template,
  }));


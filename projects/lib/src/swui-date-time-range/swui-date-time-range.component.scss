.sw-date-range {
  position: relative;
  display: flex;
  flex-direction: column;
  &__control {
    margin-bottom: 16px;
    &:last-child {
      margin-bottom: 12px;
    }
  }
  &__fieldset {
    display: flex;
    flex-direction: column;
    &--inline {
      flex-direction: row;
      .sw-date-range{
        &__control {
          width: calc(50% - 4px);
          margin: 0 8px 0 0;
          &:last-child {
            margin: 0;
          }
        }
      }
    }
  }
}
* {
  &:focus {
    outline: none !important;
    outline-color: transparent !important;
  }
}

.sw-control {
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  display: flex;
  align-items: center;
  width: 100%;
  height: 44px;
  padding: 0 0 0 32px;
  background-color: #fff;
  &__input {
    position: relative;
    display: block;
    width: 100%;
    height: 100%;
  }
  input {
    height: calc(100% - 1px);
    width: 100%;
    border: none;
    margin-top: 1px;
    background: transparent;
    &:focus {
      outline: none !important;
    }
    &::-webkit-input-placeholder {
      color: rgba(0, 0, 0, .42) !important;
      -webkit-text-fill-color: rgba(0, 0, 0, .42) !important;
    }

    &:-ms-input-placeholder { /* Internet Explorer 10-11 */
      color: rgba(0, 0, 0, .42) !important;
    }

    &::placeholder {
      color: rgba(0, 0, 0, .42) !important;
      -webkit-text-fill-color: rgba(0, 0, 0, .42) !important;
    }
  }
  &__prefix {
    position: absolute;
    top: 50%;
    left: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: -12px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
  }
  &__close {
    width: 32px !important;
    height: 32px !important;
    line-height: 32px !important;
  }
  &__label {
    position: absolute;
    top: 0;
    left: 32px;
    display: flex;
    align-items: center;
    height: 100%;
    width: calc(100% - 64px);
    color: rgba(0, 0, 0, .42);
    transform: translateY(0);
    transform-origin: left;
    transition: transform 0.15s ease;
    &.rised {
      height: auto;
      width: auto;
      background-color: #fff;
      transform: translateY(-0.5em) scale(0.75);
      padding: 0 0.5em;
      margin-left: -0.3em;
    }
  }
}

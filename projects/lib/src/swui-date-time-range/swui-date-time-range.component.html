<div class="sw-date-range">
  <lib-swui-custom-period
    #period
    class="sw-date-range__custom"
    [smallCustomPeriodsButton]="smallCustomPeriodsButton"
    [hideCustomPeriods]=hideCustomPeriods
    (periodChange)="onPeriodChange($event)"
    [disabled]="disabled">
  </lib-swui-custom-period>

  <div [formGroup]="form" class="sw-date-range__fieldset" [ngClass]="{'sw-date-range__fieldset--inline': isInline}">
    <div class="sw-date-range__control sw-control">
      <mat-icon class="sw-control__prefix">date_range</mat-icon>
      <div class="sw-control__label" [ngClass]="{ 'rised': fromLabelRised }">{{fromPlaceholder}}</div>
      <lib-swui-datetimepicker
        #from
        class="sw-control__input"
        [config]="config"
        [minDate]="minDate"
        [maxDate]="processedFromMaxDate"
        [formControl]="fromControl">
      </lib-swui-datetimepicker>
      <button
        *ngIf="fromControl.value"
        [disabled]="disabled"
        mat-icon-button
        (click)="period.reset();onResetClick($event, fromControl)"
        class="sw-control__close">
        <mat-icon>close</mat-icon>
      </button>

    </div>

    <div class="sw-date-range__control sw-control">
      <mat-icon class="sw-control__prefix">date_range</mat-icon>
      <div class="sw-control__label" [ngClass]="{ 'rised': toLabelRised }">{{toPlaceholder}}</div>
      <lib-swui-datetimepicker
        #to
        class="sw-control__input"
        [minDate]="processedToMinDate"
        [maxDate]="maxDate"
        [config]="config"
        [formControl]="toControl">
      </lib-swui-datetimepicker>
      <button
        *ngIf="toControl.value"
        [disabled]="disabled"
        mat-icon-button
        (click)="period.reset();onResetClick($event, toControl)"
        class="sw-control__close">
        <mat-icon>close</mat-icon>
      </button>
    </div>

  </div>

</div>

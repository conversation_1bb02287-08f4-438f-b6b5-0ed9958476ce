<ng-container *ngIf="!hideCustomPeriods">

  <ng-container *ngIf="!smallCustomPeriodsButton; else smallTpl">
    <button
      mat-stroked-button
      [matMenuTriggerFor]="menu"
      [disabled]="disabled"
      class="active-item">
      <span class="active-item__inner">{{ currentPeriod?.title || title | translate }}</span>
    </button>
  </ng-container>

  <ng-template #smallTpl>
    <div class="active-link">
      <a [matMenuTriggerFor]="menu" class="active-link__button" matRipple>
        {{ currentPeriod?.title || title | translate }}
      </a>
    </div>
  </ng-template>

  <mat-menu #menu="matMenu">
    <button mat-menu-item *ngFor="let period of periods" (click)="onClick($event, period)">
      {{ period?.title | translate }}
    </button>
  </mat-menu>
</ng-container>


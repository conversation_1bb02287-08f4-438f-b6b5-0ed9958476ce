import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatRippleModule } from '@angular/material/core';
import { TranslateModule } from '@ngx-translate/core';

import { CustomPeriodComponent } from './custom-period.component';


export const MODULES = [
  MatMenuModule,
  MatButtonModule,
  MatRippleModule,
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ...MODULES,
  ],
  declarations: [
    CustomPeriodComponent
  ],
  exports: [
    CustomPeriodComponent
  ]
})
export class CustomPeriodModule {
}

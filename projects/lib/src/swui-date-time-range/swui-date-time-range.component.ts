import { Component, ElementRef, HostBinding, Input, OnInit, Optional, Self, ViewChild, ViewEncapsulation } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, FormGroupDirective, NgControl } from '@angular/forms';
import { takeUntil } from 'rxjs/operators';
import { MatFormFieldControl } from '@angular/material/form-field';
import { FocusMonitor } from '@angular/cdk/a11y';
import moment from 'moment';
import { SwuiDateTimepickerConfig } from '../swui-datetimepicker/swui-datetimepicker.interface';
import { SwuiTimepickerTimeDisableLevel } from '../swui-timepicker/swui-timepicker.interface';
import { CustomPeriods } from './custom-period/custom-period.interface';
import { SwuiDatetimepickerComponent } from '../swui-datetimepicker/swui-datetimepicker.component';
import { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';
import { ErrorStateMatcher } from '@angular/material/core';


function toMoment( value: moment.Moment | string | undefined | null ): moment.Moment | null {
  if (typeof value === 'undefined' || value === null) {
    return null;
  }
  if (moment.isMoment(value)) {
    return value;
  }
  const date = moment.parseZone(value);
  if (date.isValid()) {
    return date;
  }
  return null;
}

const CONTROL_NAME = 'lib-swui-date-time-range';
let nextUniqueId = 0;

@Component({
    selector: 'lib-swui-date-time-range',
    templateUrl: './swui-date-time-range.component.html',
    styleUrls: ['./swui-date-time-range.component.scss'],
    providers: [{ provide: MatFormFieldControl, useExisting: SwuiDateTimeRangeComponent }],
    encapsulation: ViewEncapsulation.None,
    standalone: false
})
export class SwuiDateTimeRangeComponent extends SwuiMatFormFieldControl<any> implements OnInit {
  @Input()
  get value(): any {
    return this._value;
  }

  set value( value: any ) {
    this.writeValue(value);
    this.stateChanges.next(undefined);
  }

  @Input()
  get minDate(): moment.Moment | string | undefined {
    return this._minDate;
  }

  set minDate( value: moment.Moment | string | undefined ) {
    this._minDate = value;
    this.processToMinDate(this.fromControl.value);
  }

  @Input()
  get maxDate(): moment.Moment | string | undefined {
    return this._maxDate;
  }

  set maxDate( value: moment.Moment | string | undefined ) {
    this._maxDate = value;
    this.processFromMaxDate(this.toControl.value);
  }

  @Input() isInline = false;
  @Input() hideCustomPeriods = false;
  @Input() smallCustomPeriodsButton = false;

  @Input() from = 'from';
  @Input() to = 'to';

  @Input() fromPlaceholder = '';
  @Input() toPlaceholder = '';

  @Input() dateFormat?: string;
  @Input() timeFormat?: string;
  @Input() timeZone?: string;

  @Input() disableTime?: SwuiTimepickerTimeDisableLevel = {
    hour: false,
    minute: false,
    second: false
  };
  @Input() hideTime = false;

  get empty() {
    return !this.fromControl.value && !this.toControl.value;
  }

  readonly controlType = CONTROL_NAME;

  processedToMinDate?: moment.Moment;
  processedFromMaxDate?: moment.Moment;
  fromLabelRised = false;
  toLabelRised = false;

  readonly form: UntypedFormGroup;

  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;
  @HostBinding('attr.tabindex') tabindex = 0;

  @HostBinding('class.floating')
  get shouldLabelFloat() {
    return true;
  }

  @ViewChild('fromLabel') fromLabelRef?: ElementRef<HTMLElement>;
  @ViewChild('from') fromInput?: SwuiDatetimepickerComponent;
  @ViewChild('to') toInput?: SwuiDatetimepickerComponent;

  private _value: any;

  private _minDate: moment.Moment | string | undefined;
  private _maxDate: moment.Moment | string | undefined;

  constructor( fm: FocusMonitor,
               elRef: ElementRef<HTMLElement>,
               @Optional() @Self() ngControl: NgControl,
               @Optional() parentFormGroup: FormGroupDirective,
               errorStateMatcher: ErrorStateMatcher,
               fb: UntypedFormBuilder ) {
    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);
    this.form = fb.group({
      from: [null],
      to: [null]
    });
  }

  ngOnInit(): void {
    this.form.valueChanges
      .pipe(takeUntil(this.destroyed$))
      .subscribe(( val: { from: moment.Moment, to: moment.Moment } ) => {

        this.fromLabelRised = !!val.from;
        this.toLabelRised = !!val.to;

        this.processFromMaxDate(val.to);
        this.processToMinDate(val.from);
        this.onChange({ [this.from]: val.from, [this.to]: val.to });
      });
  }

  writeValue( obj: any ): void {
    if (obj === null) {
      this.form.reset();
    }
    const value = { [this.from]: null, [this.to]: null, ...obj };
    this._value = value;
    this.form.patchValue(value);
  }

  onContainerClick( event: Event ) {
    if (!this.disabled && (event.target as Element).tagName.toLowerCase() !== 'input') {
      this.elRef.nativeElement.focus();
    }
  }

  get config(): SwuiDateTimepickerConfig {
    return {
      dateFormat: this.dateFormat,
      timeFormat: this.timeFormat,
      timeDisableLevel: this.disableTime,
      disableTimepicker: this.hideTime,
      timeZone: this.timeZone
    };
  }

  onPeriodChange( val: CustomPeriods ) {
    this.form.setValue(val);
  }

  onResetClick( event: Event, control: UntypedFormControl ) {
    event.preventDefault();
    event.stopPropagation();
    this.onTouched();
    control.setValue(null);
  }

  get fromControl(): UntypedFormControl {
    return this.form.get('from') as UntypedFormControl;
  }

  get toControl(): UntypedFormControl {
    return this.form.get('to') as UntypedFormControl;
  }

  protected onDisabledState( disabled: boolean ): void {
    if (disabled) {
      this.form.disable();
    } else {
      this.form.enable();
    }
  }

  protected isErrorState(): boolean {
    if (this.fromInput && this.toInput) {
      return this.fromInput.errorState || this.toInput.errorState;
    }
    return false;
  }

  private processToMinDate( value: moment.Moment ) {
    const min = toMoment(this.minDate) || undefined;
    this.processedToMinDate = min && min.diff(value) > 0 || !value ? min : value;
  }

  private processFromMaxDate( value: moment.Moment ) {
    const max = toMoment(this.maxDate) || undefined;
    this.processedFromMaxDate = max && max.diff(value) < 0 || !value ? max : value;
  }
}

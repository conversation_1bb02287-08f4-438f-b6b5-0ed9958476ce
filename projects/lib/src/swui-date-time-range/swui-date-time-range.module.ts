import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

import { CustomPeriodModule } from './custom-period/custom-period.module';
import { SwuiDatetimepickerModule } from '../swui-datetimepicker/swui-datetimepicker.module';
import { SwuiDateTimeRangeComponent } from './swui-date-time-range.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatRippleModule } from '@angular/material/core';


export const DATE_TIME_RANGE_MODULES = [
  TranslateModule.forChild(),
  MatFormFieldModule,
  MatIconModule,
  MatButtonModule,
  MatInputModule,
  MatRippleModule,
  SwuiDatetimepickerModule,
  CustomPeriodModule,
  ReactiveFormsModule,
];

@NgModule({
  imports: [
    CommonModule,
    ...DATE_TIME_RANGE_MODULES,
  ],
  exports: [
    SwuiDateTimeRangeComponent
  ],
  declarations: [SwuiDateTimeRangeComponent]
})
export class SwuiDateTimeRangeModule {
}

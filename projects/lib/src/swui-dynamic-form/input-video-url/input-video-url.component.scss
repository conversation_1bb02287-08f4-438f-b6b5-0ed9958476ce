.video-control {
  position: relative;
  padding-bottom: 1.09375em;
  margin: .25em 0;

  &__header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }

  &__video {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    width: 100%;
    background-image: linear-gradient(45deg, #cccccc 25%, transparent 25%), linear-gradient(-45deg, #cccccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #cccccc 75%), linear-gradient(-45deg, transparent 75%, #cccccc 75%);
    background-size: 10px 10px;
    background-position: 0 0, 0 5px, 5px -5px, -5px 0px;
    border-radius: 5px;
    overflow: hidden;

    video {
      width: 100%;
      height: 100%;
      object-fit: cover;
      position: absolute;
    }
  }
}


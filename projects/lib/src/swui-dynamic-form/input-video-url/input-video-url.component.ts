import {
  ChangeDetectionStrategy, Component, ElementRef, Input, OnInit, ViewChild
} from '@angular/core';
import { AbstractControl, UntypedFormControl, ValidationErrors } from '@angular/forms';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { VideoUrlInputOption } from '../dynamic-form.model';

const urlValidation = ( control: AbstractControl ): ValidationErrors | null => {
  const regexp = /(^(?:http(s)?:\/\/)?([\{]?)+[\w]+([\-\.]{1}[\w]+)*\.[\w]{2,5}(:[\d]{1,5})?([\}]?)+(\/.*)?$)|(^(?:http(s)?:\/\/)?([\{]?)+[\w]+([\}]?)+(\/.*)?$)/gm;
  const value = control.value === null ? '' : control.value.toString();
  if (!value || value.match(regexp)) {
    return null;
  }
  return { 'urlIsNotCorrect': true };
};

const videoUrlValidation = ( control: AbstractControl ): ValidationErrors | null => {
  const value = control.value === null ? '' : control.value.toString();

  if (value) {
    const extension = value.substring(value.lastIndexOf('.') + 1);
    if (['mp4', '3gp', 'ogg'].includes(extension)) {
      return null;
    }

    return { 'urlIsNotVideo': true };
  }

  return null;
};

@Component({
    selector: 'lib-input-video-url',
    templateUrl: './input-video-url.component.html',
    styleUrls: ['./input-video-url.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class InputVideoUrlComponent implements OnInit {
  @Input() control?: UntypedFormControl;
  @Input() id = '';
  @Input() readonly = false;
  @Input() submitted = false;

  @Input() set componentOptions( value: VideoUrlInputOption | undefined ) {
    this.title = value?.title;
    this.defaultValue = value?.defaultValue;
    this.required = value?.required;
    this.errorMessages = value?.validation?.messages;
  }

  title?: string;
  defaultValue?: string;
  required?: boolean;
  errorMessages?: { [key: string]: string };

  @ViewChild('fileInput') fileInputRef?: ElementRef;

  constructor( private readonly sanitizer: DomSanitizer ) {
  }

  ngOnInit() {
    this.control?.addValidators([urlValidation, videoUrlValidation]);
  }

  get videoUrl(): string | null {
    return this.control?.value;
  }

  get safeVideoUrl(): SafeResourceUrl {
    return this.sanitizer.bypassSecurityTrustUrl(this.videoUrl || '');
  }
}



<ng-container *ngIf="control">
  <div class="video-control" [class.has-error]="control.invalid">
    <div class="video-control__header">
      <mat-form-field appearance="outline">
        <mat-label *ngIf="title">{{ title | translate }}</mat-label>
        <input type="text" [required]="required" [readonly]="readonly" matInput [formControl]="control">
        <mat-error>
          <lib-swui-control-messages [control]="control"
                                     [messages]="errorMessages">
          </lib-swui-control-messages>
        </mat-error>
      </mat-form-field>
    </div>
    <div *ngIf="videoUrl && control.valid" class="video-control__video">
      <video autoplay loop [muted]="'muted'" [src]="safeVideoUrl">
      </video>
    </div>
  </div>
</ng-container>

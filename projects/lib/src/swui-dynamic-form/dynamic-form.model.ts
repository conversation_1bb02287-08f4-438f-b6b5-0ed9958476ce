import { InjectionToken, TypeProvider } from '@angular/core';
import {
  AbstractControl,
  AsyncValidatorFn,
  UntypedFormArray,
  UntypedFormControl,
  UntypedFormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators
} from '@angular/forms';
import { Observable } from 'rxjs';
import { SwuiDateTimepickerConfig } from '../swui-datetimepicker/swui-datetimepicker.interface';
import { SwuiSelectTableOption } from '../swui-select-table/swui-select-table.interface';

export interface MatDynamicFormWidget {
  type: string;
  component: TypeProvider;
}

export interface MatDynamicFormConfig {
  widgets?: MatDynamicFormWidget[];
}

export interface MatDynamicFormWidgetConfig {
  control?: UntypedFormControl;
  id?: string;
  option?: any;
  readonly: boolean;
  submitted: boolean;
}

export const SWUI_DYNAMIC_FORM_CONFIG = new InjectionToken<MatDynamicFormConfig>('SWUI_DYNAMIC_FORM_CONFIG');

export const SWUI_DYNAMIC_FORM_WIDGET_CONFIG = new InjectionToken<MatDynamicFormWidgetConfig>('SWUI_DYNAMIC_FORM_WIDGET_CONFIG');

export interface DynamicFormValidation {
  updateOn?: 'change' | 'blur' | 'submit';
  validators?: ValidatorFn | ValidatorFn[];
  asyncValidators?: AsyncValidatorFn | AsyncValidatorFn[];
  messages?: { [key: string]: string };
}

export interface BaseInputOptionData {
  title?: string;
  required?: boolean;
  defaultValue?: any;
  value?: any;
  validation?: DynamicFormValidation;
  dependsOn?: string;
  dependsOnFieldName?: string;
}

export interface SelectOptionItem {
  [key: string]: any;
}

export interface SelectInputOptionData extends BaseInputOptionData {
  type: 'select';
  data?: SelectOptionItem[] | Observable<SelectOptionItem[]>;
  showSearch?: boolean;
  searchPlaceholder?: string;
  emptyOptionDisabled?: boolean;
  emptyOptionPlaceholder?: string;
  search?: boolean | {
    show?: boolean;
    placeholder?: string;
  };
  emptyOption?: boolean | {
    show?: boolean;
    placeholder?: string;
  };
}

export type SelectInputOption = SelectInputOptionData & { key: string };

export interface SelectTableInputOptionData extends BaseInputOptionData {
  type: 'select-table';
  data?: SwuiSelectTableOption[] | Observable<SwuiSelectTableOption[]>;
  showSearch?: boolean;
  emptyOptionPlaceholder?: string;
  search?: boolean | {
    show?: boolean;
    placeholder?: string;
  };
  columns: any[];
  rowsNumber?: number;
  loading?: boolean;
}

export type SelectTableInputOption = SelectTableInputOptionData & { key: string };

export interface MultiselectInputOptionData extends BaseInputOptionData {
  type: 'multiselect';
  data?: SelectOptionItem[] | Observable<SelectOptionItem[]>;
  showSearch?: boolean;
  searchPlaceholder?: string;
  search?: boolean | {
    show?: boolean;
    placeholder?: string;
  };
  disableAllOption?: boolean;
}

export type MultiselectInputOption = MultiselectInputOptionData & { key: string };

export interface TextInputOptionData extends BaseInputOptionData {
  type: 'text' | 'url' | 'string';
  clearable?: boolean;
}

export type TextInputOption = TextInputOptionData & { key: string };

export interface TextareaInputOptionData extends BaseInputOptionData {
  type: 'textarea';
  clearable?: boolean;
  autosize?: boolean | { minRows: number } | { maxRows: number } | { minRows: number; maxRows: number };
}

export type TextareaInputOption = TextareaInputOptionData & { key: string };

export interface NumberInputOptionData extends BaseInputOptionData {
  type: 'number';
  clearable?: boolean;
  min?: number;
  max?: number;
  step?: number;
}

export type NumberInputOption = NumberInputOptionData & { key: string };

export interface ImageInputOptionData extends BaseInputOptionData {
  type: 'base64image';
  fileInputLabel?: string;
}

export interface VideoUrlInputOptionData extends BaseInputOptionData {
  type: 'video-url';
  fileInputLabel?: string;
}

export type ImageInputOption = ImageInputOptionData & { key: string };
export type VideoUrlInputOption = VideoUrlInputOptionData & { key: string };

export interface ColorInputOptionData extends BaseInputOptionData {
  type: 'color';
}

export type ColorInputOption = ColorInputOptionData & { key: string };

export interface SwitchInputOptionData extends BaseInputOptionData {
  type: 'boolean';
}

export type SwitchInputOption = SwitchInputOptionData & { key: string };

export interface PasswordInputOptionData extends BaseInputOptionData {
  type: 'password';
}

export type PasswordInputOption = PasswordInputOptionData & { key: string };

export interface SearchInputOptionData extends BaseInputOptionData {
  type: 'search';
  fields?: string;
}

export type SearchInputOption = SearchInputOptionData & { key: string };

export interface DateRangeInputOptionData extends BaseInputOptionData {
  type: 'daterange';
  clearable?: boolean;
  config?: SwuiDateTimepickerConfig;
}

export type DateRangeInputOption = DateRangeInputOptionData & { key: string };

export interface DatetimeRangeInputOptionData extends BaseInputOptionData {
  type: 'datetimerange';
}

export type DatetimeRangeInputOption = DatetimeRangeInputOptionData & { key: string };

export interface DateInputOptionData extends BaseInputOptionData {
  type: 'date';
  clearable?: boolean;
  config?: SwuiDateTimepickerConfig;
}

export type DateInputOption = DateInputOptionData & { key: string };

export interface NumericRangeInputOptionData extends BaseInputOptionData {
  type: 'numericrange';
  clearable?: boolean;
  getDivider: (value: string) => number;
}

export type NumericRangeInputOption = NumericRangeInputOptionData & { key: string };

export interface OutletOptionData extends BaseInputOptionData {
  type: string;

  [field: string]: any;
}

export type OutletOption = OutletOptionData & { key: string };

export type InputOptionData =
  SelectInputOptionData |
  SelectTableInputOptionData |
  TextInputOptionData |
  TextareaInputOptionData |
  ImageInputOptionData |
  VideoUrlInputOptionData |
  SwitchInputOptionData |
  ColorInputOptionData |
  PasswordInputOptionData |
  SearchInputOptionData |
  DateRangeInputOptionData |
  DatetimeRangeInputOptionData |
  NumericRangeInputOptionData |
  MultiselectInputOptionData |
  OutletOptionData;

export type InputOption =
  SelectInputOption |
  SelectTableInputOption |
  TextInputOption |
  TextareaInputOption |
  ImageInputOption |
  VideoUrlInputOption |
  SwitchInputOption |
  ColorInputOption |
  PasswordInputOption |
  SearchInputOption |
  DateRangeInputOption |
  DatetimeRangeInputOption |
  NumericRangeInputOption |
  MultiselectInputOption |
  OutletOption;

export type DynamicFormAction = {
  svgIcon?: string;
  fontIcon?: {
    fontSet?: string;
    name: string;
  };
  label?: string;
  class?: string;
};

export interface SequenceOptionData {
  type: 'sequence';
  title?: string;
  maxItems?: number;
  actions?: {
    add?: DynamicFormAction | string;
    remove?: DynamicFormAction | string;
  };
  inputs: {
    [key: string]: InputOptionData;
  };
  value?: { [key: string]: any }[];
}

export interface CollectionOptionData {
  type: 'collection';
  title?: string;
  inputs: {
    [key: string]: InputOptionData;
  };
  value?: { [key: string]: any };
}

export type ControlOptionData = InputOptionData | SequenceOptionData | CollectionOptionData;

export type ControlOption = InputOption | SequenceOption | CollectionOption;

export interface DynamicFormOptionData {
  [key: string]: ControlOptionData;
}

export type SequenceOption = SequenceOptionData & { key: string };
export type CollectionOption = CollectionOptionData & { key: string };

function fileFormatValidator( control: AbstractControl ): ValidationErrors | null {
  const value = control.value;
  if (!value || !(value instanceof Error)) {
    return null;
  }
  return { fileFormatNotSupported: true };
}

export function createFormControl( option: BaseInputOptionData & { type: string }, value?: any ): UntypedFormControl {
  const { validation, required, type, defaultValue } = option;
  const formValidators = validation?.validators;
  const validators = formValidators ? Array.isArray(formValidators) ? formValidators : [formValidators] : [];
  if (required) {
    validators.push(Validators.required);
  }
  if (type === 'base64image') {
    validators.push(fileFormatValidator);
  }
  return new UntypedFormControl(value === null || value === undefined ? defaultValue : value, {
    updateOn: validation?.updateOn,
    validators,
    asyncValidators: validation?.asyncValidators
  });
}

export class ControlInputItem {
  readonly type = 'input';
  readonly control: UntypedFormControl;
  constructor( readonly option: InputOption ) {
    this.control = createFormControl(option, option.value);
  }
}

export class ControlSequenceItem {
  readonly type = 'sequence';
  readonly control = new UntypedFormArray([]);
  readonly items: ControlItem[][] = [];
  constructor( readonly option: SequenceOption ) {
    (option.value || []).forEach(value => {
      this.add(value);
    });
  }
  add( values?: { [key: string]: any } ): void {
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    const newItems = Object.entries(this.option.inputs || {}).map(( [key, input] ) => createControlItem({
      ...input,
      key,
      value: values ? values[key] : undefined
    }));
    if (newItems.length) {
      this.items.push(newItems);
      this.control.push(new UntypedFormGroup(newItems.reduce(( result, item ) => ({
        ...result,
        [item.option.key]: item.control
      }), {})));
    }
  }
  remove( index: number ): void {
    this.items.splice(index, 1);
    this.control.removeAt(index);
  }
}

export class ControlCollectionItem {
  readonly type = 'collection';
  readonly control: UntypedFormGroup;
  readonly items: ControlItem[];
  constructor( readonly option: CollectionOption ) {
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    this.items = Object.entries(option.inputs || {}).map(( [key, input] ) => createControlItem({
      ...input,
      key,
      value: option.value ? option.value[key] : undefined
    }));
    this.control = new UntypedFormGroup(this.items.reduce(( result, item ) => ({
      ...result,
      [item.option.key]: item.control
    }), {}));
  }
}

export type ControlItem = ControlInputItem | ControlSequenceItem | ControlCollectionItem;

export function createControlItem( option: ControlOption ): ControlItem {
  if (option.type === 'sequence') {
    return new ControlSequenceItem(option as SequenceOption);
  } else if (option.type === 'collection') {
    return new ControlCollectionItem(option as CollectionOption);
  } else {
    return new ControlInputItem(option);
  }
}



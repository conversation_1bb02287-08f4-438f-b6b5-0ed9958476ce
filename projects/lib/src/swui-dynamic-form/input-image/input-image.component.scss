.img-control {
  position: relative;
  padding-bottom: 1.09375em;
  margin: .25em 0;
  &__header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }
  &__button {
    margin-left: auto;
    padding: 0;
  }
  &__label {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    padding: 0 16px;
    cursor: pointer;
  }
  &__icon {
    margin-right: 8px;
  }
  &__image {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    width: 100%;
    background-image: linear-gradient(45deg, #cccccc 25%, transparent 25%), linear-gradient(-45deg, #cccccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #cccccc 75%), linear-gradient(-45deg, transparent 75%, #cccccc 75%);
    background-size: 10px 10px;
    background-position: 0 0, 0 5px, 5px -5px, -5px 0px;
    border-radius: 5px;
    overflow: hidden;
    img {
      display: block;
      height: auto;
      width: auto;
      max-width: 100%;
      max-height: 100%;
    }
  }
  &__input {
    display: none;
  }
  &__error {
    position: absolute;
    bottom: 0;
    color: #f44336;
    font-size: 12px;
    line-height: 1;
    padding-left: 16px;
  }
}


<ng-container *ngIf="control">
  <div class="img-control" [class.has-error]="control.invalid">
    <div class="img-control__header">
      <div *ngIf="title" class="img-control__title">
        {{title | translate}}
        <span
          class="mat-placeholder-required mat-form-field-required-marker"
          aria-hidden="true" *ngIf="required">&#32;*</span>
      </div>
      <button type="button" class="img-control__button" mat-flat-button color="primary">
        <label class="img-control__label">
          <mat-icon class="img-control__icon" fontSet="material-icons-outline">image</mat-icon>
          {{(fileInputLabel || 'LOBBY.THEMES.chooseFile') | translate}}
          <input #fileInput class="img-control__input" type="file" (change)="handleChange($event)">
        </label>
      </button>
      <button type="button"
              mat-icon-button
              matTooltip="{{(defaultValue ? 'ALL.resetDefault' : 'ALL.delete') | translate}}"
              class="sw-color__reset"
              *ngIf="!isIdentical"
              (click)="onResetClick($event)">
        <mat-icon *ngIf="defaultValue">replay</mat-icon>
        <mat-icon *ngIf="!defaultValue" fontSet="material-icons-outline">delete_outline</mat-icon>
      </button>
    </div>
    <div *ngIf="image" class="img-control__image">
      <img [src]="safeImage" alt>
    </div>
    <div class="img-control__error">
      <lib-swui-control-messages
        [control]="control"
        [force]="submitted"
        [messages]="errorMessages"
      ></lib-swui-control-messages>
    </div>
  </div>
</ng-container>

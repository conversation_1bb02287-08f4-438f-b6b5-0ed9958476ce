import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { Observable, of, pipe, Subject, Subscriber, UnaryFunction } from 'rxjs';
import { catchError, map, switchMap, takeUntil } from 'rxjs/operators';
import { ImageInputOption } from '../dynamic-form.model';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';


function validateImageFile(): UnaryFunction<Observable<File | null>, Observable<File | null>> {
  const supportedFileTypes = new Set(['png', 'svg', 'jpg', 'jpeg', 'gif']);
  return pipe(
    switchMap(file => of(file).pipe(
      map(() => {
          const ext = file?.name.substring(file?.name.lastIndexOf('.') + 1);
          return ext && supportedFileTypes.has(ext) ? file : null;
        }
      )
    ))
  );
}

function mapToBase64(): UnaryFunction<Observable<File | null>, Observable<string | null>> {
  return pipe(switchMap(file => file ? new Observable<string>(( subscriber: Subscriber<string> ) => {
    const fileReader = new FileReader();
    fileReader.onloadend = () => {
      if (fileReader.readyState === FileReader.DONE) {
        if (fileReader.error) {
          subscriber.error(fileReader.error);
        } else {
          if (typeof fileReader.result === 'string') {
            subscriber.next(fileReader.result);
          }
          subscriber.complete();
        }
      }
    };
    fileReader.readAsDataURL(file);
    return () => {
      fileReader.onloadend = null;
      fileReader.abort();
    };
  }) : of(null)));
}

@Component({
    selector: 'lib-input-image',
    templateUrl: './input-image.component.html',
    styleUrls: ['./input-image.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class InputImageComponent implements OnInit, OnDestroy {
  @Input() control?: UntypedFormControl;
  @Input() id = '';
  @Input() readonly = false;
  @Input() submitted = false;

  @Input() set componentOptions( value: ImageInputOption | undefined ) {
    this.title = value?.title;
    this.defaultValue = value?.defaultValue;
    this.fileInputLabel = value?.fileInputLabel;
    this.required = value?.required;
    this.errorMessages = value?.validation?.messages;
  }

  title?: string;
  fileInputLabel?: string;
  defaultValue?: string;
  required?: boolean;
  errorMessages?: { [key: string]: string };

  @ViewChild('fileInput') fileInputRef?: ElementRef;

  private readonly selectedFile = new Subject<File | null>();
  private readonly destroy$ = new Subject<void>();

  constructor( private readonly cd: ChangeDetectorRef,
               private readonly sanitizer: DomSanitizer ) {
  }

  ngOnInit() {
    this.selectedFile.pipe(
      validateImageFile(),
      mapToBase64(),
      catchError(() => of(null)),
      takeUntil(this.destroy$)
    ).subscribe(value => {
      if (this.control && value) {
        this.control.setValue(value);
      }
      this.cd.detectChanges();
    });
  }

  ngOnDestroy() {
    this.destroy$.next(undefined);
    this.destroy$.complete();
  }

  get isIdentical(): boolean {
    if (!Boolean(this.image) && !Boolean(this.defaultValue)) {
      return true;
    }
    return this.image === this.defaultValue;
  }

  get image(): string | null {
    return this.control?.value;
  }

  get safeImage(): SafeResourceUrl {
    return this.sanitizer.bypassSecurityTrustUrl(this.image || '');
  }

  handleChange( event: Event ) {
    event.preventDefault();
    event.stopPropagation();

    const target = event.target as HTMLInputElement;
    const file: File | null = target.files && target.files[0];
    if (file) {
      this.selectedFile.next(file);
    }
  }

  onResetClick( event: Event ) {
    event.preventDefault();
    event.stopPropagation();

    if (this.isIdentical) {
      return;
    }
    if (this.control) {
      this.control.setValue(this.defaultValue || '');
      if (this.fileInputRef) {
        const input = this.fileInputRef.nativeElement as HTMLInputElement;
        input.value = '';
      }
    }
  }
}

<ng-container *ngIf="data?.fontIcon || data?.svgIcon; else actionText;">
  <button mat-icon-button type="button" (click)="onClick($event)" [ngClass]="data?.class">
    <mat-icon
      [svgIcon]="data?.svgIcon"
      [fontSet]="data?.fontIcon?.fontSet"
      [fontIcon]="data?.fontIcon?.name"
    >{{data?.fontIcon?.name}}</mat-icon>
  </button>
</ng-container>
<ng-template #actionText>
  <button *ngIf="data?.label" mat-flat-button type="button" (click)="onClick($event)" [ngClass]="data?.class">
    {{ data?.label | translate }}
  </button>
</ng-template>

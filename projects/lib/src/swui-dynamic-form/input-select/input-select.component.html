<ng-container *ngIf="control">
  <mat-form-field [class.has-error]="control.invalid" class="select" appearance="outline">
    <mat-label *ngIf="title" [attr.for]="id">{{title | translate}}</mat-label>
    <lib-swui-select
      [formControl]="control"
      [attr.id]="id"
      [data]="selectOptions | async"
      [multiple]="multiple"
      [emptyOptionPlaceholder]="emptyOptionPlaceholder"
      [disableEmptyOption]="emptyOptionDisabled"
      [disableAllOption]="disableAllOption"
      [showSearch]="showSearch"
      [searchPlaceholder]="searchPlaceholder"
    ></lib-swui-select>
    <button
      class="clear-button"
      *ngIf="isClearButtonVisible()"
      matSuffix
      mat-icon-button>
      <mat-icon (click)="clear($event)">close</mat-icon>
    </button>
    <mat-error>
      <lib-swui-control-messages
        [control]="control"
        [force]="submitted"
        [messages]="errorMessages"
      ></lib-swui-control-messages>
    </mat-error>
  </mat-form-field>
</ng-container>

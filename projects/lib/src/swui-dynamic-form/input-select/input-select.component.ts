import { Component, Input, OnDestroy } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { isObservable, Observable, of, ReplaySubject, Subject, Subscription } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { MultiselectInputOptionData, SelectInputOption, SelectOptionItem } from '../dynamic-form.model';

@Component({
    selector: 'lib-input-select',
    templateUrl: './input-select.component.html',
    styleUrls: ['input-select.component.scss'],
    standalone: false
})
export class InputSelectComponent implements OnDestroy {
  @Input() control?: UntypedFormControl;
  @Input() id = '';
  @Input() readonly = false;
  @Input() submitted = false;

  @Input() set componentOptions( value: SelectInputOption | MultiselectInputOptionData | undefined ) {
    this.multiple = value?.type === 'multiselect';
    this.setSelectOptions(value?.data || []);
    this.title = value?.title;
    this.errorMessages = value?.validation?.messages;

    this.showSearch = value?.showSearch;
    this.searchPlaceholder = value?.searchPlaceholder;

    const search = typeof value?.search === 'undefined' ? {
      show: this.showSearch,
      placeholder: this.searchPlaceholder
    } : value?.search;

    if (typeof search === 'boolean') {
      this.showSearch = search;
    } else if (search.placeholder) {
      this.showSearch = typeof search.show === 'undefined' ? true : search.show;
      this.searchPlaceholder = search.placeholder;
    }

    if (value?.type === 'select') {
      this.emptyOptionPlaceholder = value?.emptyOptionPlaceholder;
      this.emptyOptionDisabled = value?.emptyOptionDisabled;

      const emptyOption = typeof value?.emptyOption === 'undefined' ? {
        show: typeof this.emptyOptionDisabled === 'undefined' ? undefined : !this.emptyOptionDisabled,
        placeholder: this.emptyOptionPlaceholder
      } : value?.emptyOption;

      if (typeof emptyOption === 'boolean') {
        this.emptyOptionDisabled = !emptyOption;
      } else if (emptyOption.placeholder) {
        this.emptyOptionDisabled = typeof emptyOption.show === 'undefined' ? false : !emptyOption.show;
        this.emptyOptionPlaceholder = emptyOption.placeholder;
      }
    }

    if (value?.type === 'multiselect') {
      this.disableAllOption = value?.disableAllOption;
    }
  }

  multiple = false;
  title?: string;
  selectOptions = new ReplaySubject<SelectOptionItem[]>(1);
  errorMessages?: { [key: string]: string };

  showSearch?: boolean;
  searchPlaceholder?: string;

  emptyOptionDisabled?: boolean;
  emptyOptionPlaceholder?: string;

  disableAllOption?: boolean;

  private destroy$ = new Subject();
  private subscription = new Subscription();

  constructor() {
    this.selectOptions
      .pipe(
        takeUntil(this.destroy$)
      )
      .subscribe(data => {
        if (!this.control || !this.control.value) {
          return;
        }

        if (this.multiple) {
          const value = (this.control?.value || []).filter(( val: string ) => data.find(item => item.id === val));

          if (this.control.value.length !== value.length) {
            this.control.setValue(value);
          }
        } else {
          if (!data.find(item => item.id === this.control?.value)) {
            this.clear();
          }
        }
      });
  }

  clear( event?: MouseEvent ) {
    event?.stopPropagation();
    if (this.control) {
      this.control.setValue(null);
    }
  }

  isClearButtonVisible(): boolean {
    return !this.emptyOptionDisabled &&
      (this.multiple ?
        this.control && Array.isArray(this.control.value) && this.control.value.filter(i => i)?.length :
        this.control?.value);
  }

  ngOnDestroy() {
    this.destroy$.next(undefined);
    this.destroy$.complete();
  }

  private setSelectOptions( data: SelectOptionItem[] | Observable<SelectOptionItem[]> ) {
    this.subscription.unsubscribe();
    const source = isObservable(data) ? data : of(data || []);
    this.subscription = source.pipe(
      map(opts => opts.map(opt => ({
          text: opt.text || opt.title || opt.displayName,
          id: opt.value || opt.id,
          disabled: opt.disabled,
          data: opt
        }))
      ),
      map(opts => opts.filter(( { id, text } ) => !!id && !!text)),
      takeUntil(this.destroy$)
    ).subscribe(items => {
      this.selectOptions.next(items);
    });
  }
}

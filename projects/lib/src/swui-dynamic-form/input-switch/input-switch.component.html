<ng-container *ngIf="control">
  <div class="sw-boolean" [class.has-error]="control.invalid">
    <mat-slide-toggle [formControl]="control" [attr.id]="id" [title]="title | translate">
      {{title | translate}}
    </mat-slide-toggle>
    <div class="sw-boolean__error">
      <lib-swui-control-messages
        [control]="control"
        [force]="submitted"
        [messages]="errorMessages"
      ></lib-swui-control-messages>
    </div>
  </div>
</ng-container>

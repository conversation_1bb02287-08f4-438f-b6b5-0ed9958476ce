import { Component, Input } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { SwitchInputOption } from '../dynamic-form.model';

@Component({
    selector: 'lib-input-switch',
    templateUrl: './input-switch.component.html',
    styleUrls: ['./input-switch.component.scss'],
    standalone: false
})
export class InputSwitchComponent {
  @Input() control?: UntypedFormControl;
  @Input() id = '';
  @Input() readonly = false;
  @Input() submitted = false;

  @Input() set componentOptions( value: SwitchInputOption | undefined ) {
    this.title = value?.title || '';
    this.errorMessages = value?.validation?.messages;
  }

  title = '';
  errorMessages?: { [key: string]: string };
}

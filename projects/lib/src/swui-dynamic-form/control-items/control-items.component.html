<ng-container *ngTemplateOutlet="controlItems; context:{ items: items, id: prefixId }"></ng-container>

<ng-template #controlItems let-controls="items" let-id="id">
  <ng-container *ngFor="let control of controls" [ngSwitch]="control.type">
    <ng-container *ngSwitchCase="'collection'">
      <ng-container *ngTemplateOutlet="controlCollection; context:{ item: control, id: id }"></ng-container>
    </ng-container>
    <ng-container *ngSwitchCase="'sequence'">
      <ng-container *ngTemplateOutlet="controlSequence; context:{ item: control, id: id }"></ng-container>
    </ng-container>
    <lib-control-input
      *ngSwitchDefault
      [item]="control"
      [prefixId]="id"
      [readonly]="readonly"
      [submitted]="submitted"
    ></lib-control-input>
  </ng-container>
</ng-template>

<ng-template #controlCollection let-id="id" let-control="item">
  <div class="sw-collection">
    <div class="sw-collection__title" *ngIf="control?.option?.title">{{control?.option?.title | translate}}</div>
    <div class="sw-collection__list" *ngIf="control?.items?.length">
      <ng-container *ngTemplateOutlet="controlItems; context:{ items: control?.items, id: id + (control?.option?.key || '') }"></ng-container>
    </div>
  </div>
</ng-template>

<ng-template #controlSequence let-id="id" let-control="item">
  <div class="sw-sequence">
    <div class="sw-sequence__header">
      <div class="sw-sequence__title" *ngIf="control?.option?.title">{{control?.option?.title | translate}}</div>
      <lib-button-action
        *ngIf="control?.option?.maxItems ? control?.items?.length < control?.option?.maxItems : true"
        [value]="control?.option?.actions?.add"
        (action)="control?.add()">
      </lib-button-action>
    </div>

    <div class="sw-sequence__body">
      <div class="sw-sequence__item" *ngFor="let subItems of control?.items; let i = index;">
        <div class="sw-sequence__item-header">
          <div class="sw-sequence__index">#{{i + 1}}</div>
          <lib-button-action [value]="actionRemove($any(control?.option?.actions?.remove))" (action)="control?.remove(i)">
          </lib-button-action>
        </div>
        <ng-container *ngTemplateOutlet="controlItems; context:{ items: subItems, id: id + '_' + i + '_' + (control?.option?.key || '') }"></ng-container>
      </div>
    </div>

  </div>
</ng-template>

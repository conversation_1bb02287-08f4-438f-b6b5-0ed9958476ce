import { Component, Input } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { PasswordInputOption } from '../dynamic-form.model';
import { shuffle } from 'lodash';

interface PasswordConfig {
  length: number;
  uppercase?: boolean;
  digits?: boolean;
  symbols?: boolean;
}

const CHARS_MAP: { [id: string]: string[] } = {
  'lowercase': ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r',
    's', 't', 'u', 'v', 'w', 'x', 'y', 'z'],
  'uppercase': ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q',
    'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
  'symbols': ['!', '#', '$', '%', '&', '\'', '(', ')', '*', '+', ',', '-', '.', '/', ':', ';', '<', '=', '>', '?',
    '@', '[', '\\', ']', '^', '_', '`', '{', '|', '}', '~'],
  'digits': ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'],
};

function setArray( array: string[], amount: number ): any[] {
  let result = [];
  for (let i = 0; i <= (amount - 1); i++) {
    result.push(array[Math.floor(Math.random() * array.length)]);
  }
  return result;
}

// Random amount of chars of chosen type. Amount min is 1 (max depends on amount of active types)
function generatePassword( passwordConfig: PasswordConfig ): string {
  const resultArray = [];

  let passwordCharsCounter = passwordConfig.length; // counts how many characters are left
  const activePasswordTypes = Object.keys(passwordConfig).filter(el => {
    const key = el as keyof PasswordConfig;
    return key !== 'length' && passwordConfig[key] !== false;
  });
  const amount = Math.floor(Math.random() * passwordConfig.length / (activePasswordTypes.length + 1)) + 1;
  activePasswordTypes.forEach(el => {
    const processedArray = setArray(CHARS_MAP[el], amount);
    resultArray.push(...processedArray);
    passwordCharsCounter -= processedArray.length;
  });

  const lowercaseArray = setArray(CHARS_MAP.lowercase, passwordCharsCounter);
  resultArray.push(...lowercaseArray);

  return shuffle(resultArray).join('');
}

@Component({
    selector: 'lib-input-password',
    templateUrl: './input-password.component.html',
    standalone: false
})
export class InputPasswordComponent {
  @Input() control?: UntypedFormControl;
  @Input() id = '';
  @Input() readonly = false;
  @Input() submitted = false;

  @Input() set componentOptions( value: PasswordInputOption | undefined ) {
    this.title = value?.title;
    this.required = value?.required;
    this.errorMessages = value?.validation?.messages;
  }

  title?: string;
  required?: boolean;
  errorMessages?: { [key: string]: string };

  generatePassword() {
    if (this.control) {
      this.control.setValue(generatePassword({
        length: 16,
        uppercase: true,
        digits: true,
      }));
    }
  }
}

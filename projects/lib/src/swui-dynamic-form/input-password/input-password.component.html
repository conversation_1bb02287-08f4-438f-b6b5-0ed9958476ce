<ng-container *ngIf="control">
  <mat-form-field [class.has-error]="control.invalid" appearance="outline">
    <mat-label *ngIf="title" [attr.for]="id">{{title | translate}}</mat-label>
    <input matInput type="text" style="font-family: monospace" [formControl]="control" [attr.id]="id"
           [readonly]="readonly" select-on-click [required]="required">
    <button matSuffix mat-icon-button type="button" (click)="generatePassword()">
      <mat-icon fontSet="icomoon" fontIcon="icon-dice"></mat-icon>
    </button>
    <mat-error>
      <lib-swui-control-messages
        [control]="control"
        [force]="submitted"
        [messages]="errorMessages"
      ></lib-swui-control-messages>
    </mat-error>
  </mat-form-field>
</ng-container>

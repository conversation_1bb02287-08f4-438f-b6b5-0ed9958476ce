import { ChangeDetectorRef, Component, Input, <PERSON><PERSON><PERSON>roy, Optional } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { MatDynamicFormService } from '../mat-dynamic-form.service';
import { DateRangeInputOption } from '../dynamic-form.model';
import { SwuiDateTimepickerConfig } from '../../swui-datetimepicker/swui-datetimepicker.interface';

@Component({
    selector: 'lib-input-date-range',
    templateUrl: './input-date-range.component.html',
    styleUrls: ['./input-date-range.component.scss'],
    standalone: false
})
export class InputDateRangeComponent implements OnDestroy {
  @Input() control?: UntypedFormControl;
  @Input() id = '';
  @Input() readonly = false;
  @Input() submitted = false;

  @Input() set componentOptions( value: DateRangeInputOption | undefined ) {
    this.title = value?.title;
    this.clearable = value?.clearable;
    if (value?.config) {
      this.config = { ...this.config, ...value.config };
    }
  }

  title?: string;
  clearable?: boolean;
  config: SwuiDateTimepickerConfig = {};
  private readonly destroy$ = new Subject<void>();

  constructor( @Optional() service: MatDynamicFormService,
               private cdr: ChangeDetectorRef) {
    if (service) {
      service.timezone
        .pipe(
          takeUntil(this.destroy$)
        )
        .subscribe(timeZone => {
          this.config = { ...this.config, timeZone };
          this.cdr.markForCheck();
        });
    }
  }

  clear( event: MouseEvent ) {
    event.stopPropagation();
    if (this.control) {
      this.control.setValue({});
    }
  }

  isEmpty() {
    const value = this.control?.value;
    return !value || Object.values(value).every(item => !item);
  }

  ngOnDestroy() {
    this.destroy$.next(undefined);
    this.destroy$.complete();
  }
}

import { Component, Input } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';

import { NumberInputOption } from '../dynamic-form.model';


@Component({
    selector: 'lib-input-number',
    templateUrl: './input-number.component.html',
    styleUrls: ['./input-number.component.scss'],
    standalone: false
})
export class InputNumberComponent {
  @Input() control?: UntypedFormControl;
  @Input() id = '';
  @Input() readonly = false;
  @Input() submitted = false;

  @Input() set componentOptions( value: NumberInputOption | undefined ) {
    this.title = value?.title;
    this.clearable = value?.clearable;
    this.min = value?.min;
    this.max = value?.max;
    this.step = value?.step;
    this.required = value?.required;
    this.errorMessages = value?.validation?.messages;
  }

  title?: string;
  clearable?: boolean;
  min?: number;
  max?: number;
  step?: number;
  required?: boolean;
  errorMessages?: { [key: string]: string };

  clear( event: MouseEvent ) {
    event.stopPropagation();
    if (this.control) {
      this.control.setValue(null);
    }
  }
}

<ng-container *ngIf="control">
  <mat-form-field [class.has-error]="control?.invalid" appearance="outline">
    <mat-label *ngIf="title" [attr.for]="id">{{title | translate}}</mat-label>
    <input
      matInput
      type="number"
      [min]="min"
      [max]="max"
      [step]="step"
      [formControl]="control"
      [attr.id]="id"
      [readonly]="readonly"
      [required]="required">
    <button
      class="clear-button"
      *ngIf="clearable && control.value"
      matSuffix
      mat-icon-button>
      <mat-icon (click)="clear($event)">close</mat-icon>
    </button>
    <mat-error>
      <lib-swui-control-messages
        [control]="control"
        [force]="submitted"
        [messages]="errorMessages"
      ></lib-swui-control-messages>
    </mat-error>
  </mat-form-field>
</ng-container>

import { Component, Input } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { SearchInputOption } from '../dynamic-form.model';

@Component({
    selector: 'lib-input-search',
    templateUrl: './input-search.component.html',
    standalone: false
})
export class InputSearchComponent {
  @Input() control?: UntypedFormControl;
  @Input() id = '';
  @Input() readonly = false;
  @Input() submitted = false;

  @Input() set componentOptions( value: SearchInputOption | undefined ) {
    this.title = value?.title;
    this.fields = value?.fields;
  }

  title?: string;
  fields?: string;
}

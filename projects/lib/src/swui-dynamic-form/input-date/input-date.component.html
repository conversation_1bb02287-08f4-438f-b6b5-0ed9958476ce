<ng-container *ngIf="control">
  <mat-form-field [class.has-error]="control.invalid" appearance="outline">
    <mat-label *ngIf="title" [attr.for]="id">{{title | translate}}</mat-label>
    <lib-swui-date-picker
      [attr.id]="id"
      [formControl]="control"
      [config]="config"
    ></lib-swui-date-picker>
    <button
      class="clear-button"
      *ngIf="clearable && !isEmpty()"
      matSuffix
      mat-icon-button>
      <mat-icon (click)="clear($event)">close</mat-icon>
    </button>
  </mat-form-field>
</ng-container>

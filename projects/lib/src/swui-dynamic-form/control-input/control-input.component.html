<ng-container *ngIf="item && option" [ngSwitch]="type">
  <lib-input-image
    *ngSwitchCase="'base64image'"
    [control]="formControl"
    [id]="id"
    [readonly]="readonly"
    [submitted]="submitted"
    [componentOptions]="option"
  ></lib-input-image>
  <lib-input-video-url
    *ngSwitchCase="'video-url'"
    [control]="formControl"
    [id]="id"
    [readonly]="readonly"
    [submitted]="submitted"
    [componentOptions]="option"
  ></lib-input-video-url>
  <lib-input-color
    *ngSwitchCase="'color'"
    [control]="formControl"
    [id]="id"
    [readonly]="readonly"
    [submitted]="submitted"
    [componentOptions]="option"
  ></lib-input-color>
  <lib-input-switch
    *ngSwitchCase="'boolean'"
    [control]="formControl"
    [id]="id"
    [readonly]="readonly"
    [submitted]="submitted"
    [componentOptions]="option"
  ></lib-input-switch>
  <lib-input-password
    *ngSwitchCase="'password'"
    [control]="formControl"
    [id]="id"
    [readonly]="readonly"
    [submitted]="submitted"
    [componentOptions]="option"
  ></lib-input-password>
  <lib-input-date-range
    *ngSwitchCase="'daterange'"
    [control]="formControl"
    [id]="id"
    [readonly]="readonly"
    [submitted]="submitted"
    [componentOptions]="option"
  ></lib-input-date-range>
  <lib-input-date
    *ngSwitchCase="'date'"
    [control]="formControl"
    [id]="id"
    [readonly]="readonly"
    [submitted]="submitted"
    [componentOptions]="option"
  ></lib-input-date>
  <lib-input-numeric-range
    *ngSwitchCase="'numericrange'"
    [control]="formControl"
    [id]="id"
    [readonly]="readonly"
    [submitted]="submitted"
    [componentOptions]="option"
  ></lib-input-numeric-range>
  <lib-input-search
    *ngSwitchCase="'search'"
    [control]="formControl"
    [id]="id"
    [readonly]="readonly"
    [submitted]="submitted"
    [componentOptions]="option"
  ></lib-input-search>
  <lib-input-select
    *ngSwitchCase="'select'"
    [control]="formControl"
    [id]="id"
    [readonly]="readonly"
    [submitted]="submitted"
    [componentOptions]="option"
  ></lib-input-select>
  <lib-input-text
    *ngSwitchCase="'text'"
    [control]="formControl"
    [id]="id"
    [readonly]="readonly"
    [submitted]="submitted"
    [componentOptions]="option"
  ></lib-input-text>
  <lib-input-number
    *ngSwitchCase="'number'"
    [control]="formControl"
    [id]="id"
    [readonly]="readonly"
    [submitted]="submitted"
    [componentOptions]="option"
  ></lib-input-number>
  <lib-input-select-table
    *ngSwitchCase="'select-table'"
    [control]="formControl"
    [id]="id"
    [readonly]="readonly"
    [submitted]="submitted"
    [componentOptions]="option"
  ></lib-input-select-table>
  <lib-input-outlet
    *ngSwitchDefault
    [type]="type"
    [control]="formControl"
    [id]="id"
    [readonly]="readonly"
    [submitted]="submitted"
    [option]="option"
  ></lib-input-outlet>
</ng-container>

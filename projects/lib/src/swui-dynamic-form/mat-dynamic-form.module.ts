import { CommonModule } from '@angular/common';
import { ModuleWithProviders, NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { ColorPickerModule } from 'ngx-color-picker';
import { SelectOnClickModule } from '../directives/selectOnClick/select-on-click.module';
import { SwuiControlMessagesModule } from '../swui-control-messages/swui-control-messages.module';
import { SwuiDateRangeModule } from '../swui-date-range/swui-date-range.module';
import { SwuiNumericRangeMenuModule } from '../swui-numeric-range-menu/swui-numeric-range-menu.module';
import { SwuiSearchModule } from '../swui-search/swui-search.module';
import { SwuiSelectTableModule } from '../swui-select-table/swui-select-table.module';
import { SwuiSelectModule } from '../swui-select/swui-select.module';
import { SwitcheryModule } from '../swui-switchery/switchery.module';
import { InputColorComponent } from './input-color/input-color.component';
import { InputImageComponent } from './input-image/input-image.component';
import { ControlInputComponent } from './control-input/control-input.component';
import { InputPasswordComponent } from './input-password/input-password.component';
import { InputSelectTableComponent } from './input-select-table/input-select-table.component';
import { InputSelectComponent } from './input-select/input-select.component';
import { InputSwitchComponent } from './input-switch/input-switch.component';
import { InputTextComponent } from './input-text/input-text.component';
import { InputVideoUrlComponent } from './input-video-url/input-video-url.component';
import { MatDynamicFormComponent } from './mat-dynamic-form.component';
import { InputDateRangeComponent } from './input-date-range/input-date-range.component';
import { InputSearchComponent } from './input-search/input-search.component';
import { InputNumericRangeComponent } from './input-numeric-range/input-numeric-range.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { InputOutletComponent } from './input-outlet/input-outlet.component';
import { MatDynamicFormConfig, SWUI_DYNAMIC_FORM_CONFIG } from './dynamic-form.model';
import { ControlItemsComponent } from './control-items/control-items.component';
import { ButtonActionComponent } from './button-action/button-action.component';
import { MatTooltipModule } from '@angular/material/tooltip';
import { InputNumberComponent } from './input-number/input-number.component';
import { InputDateComponent } from './input-date/input-date.component';
import { SwuiDatePickerModule } from '../swui-date-picker/swui-date-picker.module';

@NgModule({
    imports: [
        CommonModule,
        ReactiveFormsModule,
        SwuiControlMessagesModule,
        TranslateModule,
        SwitcheryModule,
        ColorPickerModule,
        SelectOnClickModule,
        MatFormFieldModule,
        MatInputModule,
        MatButtonModule,
        MatSelectModule,
        MatSlideToggleModule,
        MatIconModule,
        MatTooltipModule,
        SwuiDateRangeModule,
        SwuiSelectModule,
        SwuiSearchModule,
        SwuiNumericRangeMenuModule,
        SwuiDatePickerModule,
        SwuiSelectTableModule
    ],
  exports: [MatDynamicFormComponent],
  declarations: [
    MatDynamicFormComponent,
    ControlItemsComponent,
    ControlInputComponent,
    InputOutletComponent,
    InputTextComponent,
    InputSelectComponent,
    InputImageComponent,
    InputColorComponent,
    InputSwitchComponent,
    InputPasswordComponent,
    InputDateRangeComponent,
    InputSearchComponent,
    InputNumericRangeComponent,
    ButtonActionComponent,
    InputNumberComponent,
    InputDateComponent,
    InputVideoUrlComponent,
    InputSelectTableComponent
  ],
  providers: [],
})

export class MatDynamicFormModule {
  static forRoot( config?: MatDynamicFormConfig ): ModuleWithProviders<MatDynamicFormConfig> {
    return {
      ngModule: MatDynamicFormModule,
      providers: [
        {
          provide: SWUI_DYNAMIC_FORM_CONFIG,
          useValue: config,
        }
      ]
    };
  }
}

import { AbstractControl, FormGroup, ReactiveFormsModule, ValidationErrors, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { action } from 'storybook/actions';
import { moduleMetadata, storiesOf } from '@storybook/angular';
import { of } from 'rxjs';
import { delay, map } from 'rxjs/operators';
import { I18nModule } from '../../i18n.module';
import { DynamicFormOptionData, SequenceOptionData } from '../dynamic-form.model';
import { MatDynamicFormModule } from '../mat-dynamic-form.module';
import { TestWidgetComponent } from './test-widget/test-widget.component';
import { TestWidgetModule } from './test-widget/test-widget.module';

const EN = {
  ALL: {
    resetDefault: 'Reset',
    delete: 'Delete'
  },
  LOBBY: {
    THEMES: {
      chooseFile: 'chooseFile',
      btnAdd: 'add'
    }
  },
  VALIDATION: {
    required: 'en_required',
    ip: 'en_ip',
    min: 'en_min actual: {{actual}} min: {{min}}'
  },
  COMPONENTS: {
    DATE_RANGE: {
      clear: 'Clear',
      cancel: 'Cancel',
      apply: 'Apply'
    }
  }
};

function ipValidator( { value }: AbstractControl ): ValidationErrors | null {
  if (!value) {
    return null;
  }
  return /(\d{1,3}\.){3}\d{1,3}/.test(value) ? null : { ip: true };
}

const MESSAGES = {
  required: 'VALIDATION.required',
  ip: 'VALIDATION.ip',
  min: 'VALIDATION.min'
};

const template = `
  <mat-card class="mat-elevation-z0" style="margin: 32px">
      <form [formGroup]="f" (ngSubmit)="submit(f.value)" #form="ngForm">
      <lib-dynamic-form [form]="f" [controlName]="controlName" [options]="options" [submitted]="form.submitted">
        <button mat-flat-button color="primary" type="submit">submit</button>
      </lib-dynamic-form>
    </form>
  </mat-card>
`;

storiesOf('Dynamic Form', module)
  .addDecorator(moduleMetadata({
    imports: [
      BrowserAnimationsModule,
      ReactiveFormsModule,
      I18nModule.forRoot({ translations: { en: EN } }),
      TestWidgetModule,
      MatCardModule,
      MatButtonModule,
      MatIconModule,
      MatDynamicFormModule.forRoot({
        widgets: [{
          type: 'qwerty',
          component: TestWidgetComponent
        }]
      }),
    ],
  }))
  .add('text', () => ({
    template,
    props: {
      f: new FormGroup({}),
      submit: action('submit'),
      options: {
        qwerty: {
          type: 'text'
        }
      }
    },
  }))
  .add('textarea', () => ({
    template,
    props: {
      f: new FormGroup({}),
      submit: action('submit'),
      options: {
        qwerty: {
          type: 'textarea'
        }
      }
    },
  }))
  .add('textarea / autosize / boolean', () => ({
    template,
    props: {
      f: new FormGroup({}),
      submit: action('submit'),
      options: {
        qwerty: {
          type: 'textarea',
          autosize: true
        }
      }
    },
  }))
  .add('textarea / autosize / minRows', () => ({
    template,
    props: {
      f: new FormGroup({}),
      submit: action('submit'),
      options: {
        qwerty: {
          type: 'textarea',
          autosize: { minRows: 3 }
        }
      }
    },
  }))
  .add('textarea / autosize / maxRows', () => ({
    template,
    props: {
      f: new FormGroup({}),
      submit: action('submit'),
      options: {
        qwerty: {
          type: 'textarea',
          autosize: { maxRows: 2 }
        }
      }
    },
  }))
  .add('textarea / autosize', () => ({
    template,
    props: {
      f: new FormGroup({}),
      submit: action('submit'),
      options: {
        qwerty: {
          type: 'textarea',
          autosize: { minRows: 2, maxRows: 4 }
        }
      }
    },
  }))
  .add('number', () => ({
    template,
    props: {
      f: new FormGroup({}),
      submit: action('submit'),
      options: {
        qwerty: {
          type: 'number',
          min: 0,
          max: 100,
          step: 10
        }
      }
    },
  }))
  .add('url', () => ({
    template,
    props: {
      f: new FormGroup({}),
      submit: action('submit'),
      options: {
        qwerty: {
          type: 'url',
        }
      }
    },
  }))
  .add('image', () => ({
    template,
    props: {
      f: new FormGroup({}),
      submit: action('submit'),
      options: {
        qwerty: {
          required: true,
          type: 'base64image',
          title: 'Image control title',
          defaultValue: 'data:image/jpeg;base64,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****************************************************************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'
        }
      }
    },
  }))
  .add('video-url', () => ({
    template,
    props: {
      f: new FormGroup({}),
      submit: action('submit'),
      options: {
        qwerty: {
          required: true,
          type: 'video-url',
          title: 'Video control title',
          defaultValue: 'http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
          validation: {
            messages: {
              urlIsNotCorrect: 'Url is not correct',
              urlIsNotVideo: 'Url is not a video',
              required: 'Required'
            }
          }
        },
      }
    },
  }))
  .add('color', () => ({
    template,
    props: {
      f: new FormGroup({}),
      submit: action('submit'),
      options: {
        qwerty: {
          type: 'color'
        }
      }
    },
  }))
  .add('switch', () => ({
    template,
    props: {
      f: new FormGroup({}),
      submit: action('submit'),
      options: {
        qwerty: {
          type: 'boolean'
        }
      }
    },
  }))
  .add('password', () => ({
    template,
    props: {
      f: new FormGroup({}),
      submit: action('submit'),
      options: {
        qwerty: {
          type: 'password'
        }
      }
    },
  }))
  .add('search', () => ({
    template,
    props: {
      f: new FormGroup({}),
      submit: action('submit'),
      options: {
        qwerty: {
          type: 'search'
        }
      }
    },
  }))
  .add('date range', () => ({
    template,
    props: {
      f: new FormGroup({}),
      submit: action('submit'),
      options: {
        qwerty: {
          type: 'daterange'
        }
      }
    },
  }))
  .add('date', () => ({
    template,
    props: {
      f: new FormGroup({}),
      submit: action('submit'),
      options: {
        qwerty: {
          type: 'date',
          config: {
            timePicker: true
          }
        }
      }
    },
  }))
  .add('numeric range', () => ({
    template,
    props: {
      f: new FormGroup({}),
      submit: action('submit'),
      options: {
        qwerty: {
          type: 'numericrange'
        }
      }
    },
  }))
  .add('multi select', () => {
    const options: DynamicFormOptionData = {
      qwerty: {
        type: 'multiselect',
        data: [
          { id: 'qwerty_a', title: 'qwerty_a' },
          { id: 'qwerty_b', title: 'qwerty_b' },
          { id: 'qwerty_c', title: 'qwerty_c' }
        ]
      }
    };
    return ({
      template,
      props: {
        f: new FormGroup({}),
        submit: action('submit'),
        options
      },
    });
  })
  .add('select', () => {
    const options = {
      qwerty: {
        type: 'select',
        data: [
          { id: 'qwerty_a', title: 'qwerty_a' },
          { id: 'qwerty_b', title: 'qwerty_b' },
          { id: 'qwerty_c', title: 'qwerty_c' }
        ]
      }
    };
    return ({
      template,
      props: {
        f: new FormGroup({}),
        submit: action('submit'),
        options
      },
    });
  })
  .add('select-table', () => {
    const options = {
      qwerty: {
        type: 'select-table',
        columns: [{
          field: 'title',
          name: 'Name'
        }],
        title: 'Title',
        rowsNumber: 3,
        showSearch: true,
        data: of(null)
          .pipe(
            delay(10000),
            map(() => {
              return [
                { id: 'qwerty_a', title: 'qwerty_a' },
                { id: 'qwerty_b', title: 'qwerty_b' },
                { id: 'qwerty_c', title: 'qwerty_c' }
              ];
            })
          )
      }
    };
    return ({
      template,
      props: {
        f: new FormGroup({}),
        submit: action('submit'),
        options
      },
    });
  })
  .add('collection', () => ({
    template,
    props: {
      submit: action('submit'),
      f: new FormGroup({}),
      options: {
        qwerty: {
          type: 'collection',
          title: 'Title collection',
          inputs: {
            input_a: {
              type: 'text',
              title: 'Title text'
            },
            input_b: {
              type: 'base64image',
              title: 'Image control title'
            },
            input_c: {
              type: 'number',
              title: 'Title number'
            },
            input_d: {
              type: 'color',
              title: 'Color title'
            },
            input_e: {
              type: 'boolean',
              title: 'Title boolean'
            },
            input_f: {
              type: 'text',
              title: 'Title text'
            },
            input_g: {
              type: 'collection',
              title: 'Title collection child',
              inputs: {
                input_d: {
                  type: 'text',
                  title: 'Title text 2'
                },
                input_f: {
                  type: 'number',
                  title: 'Title number 2'
                }
              }
            }
          }
        }
      }
    },
  }))
  .add('sequence', () => {
    const data: SequenceOptionData = {
      type: 'sequence',
      title: 'Players',
      actions: {
        add: 'Add player',
        remove: {
          fontIcon: {
            fontSet: 'material-icons-outline',
            name: 'clear'
          }
        }
      },
      inputs: {
        input_a: {
          type: 'text',
          title: 'Name'
        },
        input_b: {
          type: 'number',
          title: 'Amount'
        }
      }
    };
    return ({
      template,
      props: {
        f: new FormGroup({}),
        submit: action('submit'),
        options: {
          qwerty: data
        }
      },
    });
  })
  .add('unknown', () => ({
    template,
    props: {
      f: new FormGroup({}),
      submit: action('submit'),
      options: {
        qwerty: {
          type: 'unknown'
        }
      }
    },
  }))
  .add('widget', () => ({
    template,
    props: {
      f: new FormGroup({}),
      submit: action('submit'),
      options: {
        qwerty: {
          type: 'qwerty'
        }
      }
    },
  }))
  .add('validation', () => ({
    template,
    props: {
      f: new FormGroup({}),
      submit: action('submit'),
      options: {
        ip: {
          type: 'text',
          title: 'text/ip',
          validation: {
            validators: ipValidator,
            messages: MESSAGES
          }
        },
        min: {
          type: 'number',
          title: 'number/min',
          defaultValue: 0,
          required: true,
          validation: {
            validators: Validators.min(2),
            messages: MESSAGES
          }
        }
      }
    },
  }));

import { Component, Inject } from '@angular/core';
import { MatDynamicFormWidgetConfig, SWUI_DYNAMIC_FORM_WIDGET_CONFIG } from '../../dynamic-form.model';
import { MatDynamicFormWidgetComponent } from '../../mat-dynamic-form-widget.component';

@Component({
    template: '<ng-container *ngIf="option">{{ id }}: {{ option | json }}; submitted:{{ submitted }}</ng-container>',
    standalone: false
})
export class TestWidgetComponent extends MatDynamicFormWidgetComponent<any> {

  constructor( @Inject(SWUI_DYNAMIC_FORM_WIDGET_CONFIG) config: MatDynamicFormWidgetConfig ) {
    super(config);
  }
}

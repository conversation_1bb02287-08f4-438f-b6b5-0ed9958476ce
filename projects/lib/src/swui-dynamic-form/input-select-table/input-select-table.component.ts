import { Component, Input } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { BehaviorSubject, isObservable, Observable, of } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { SwuiSelectTableOption } from '../../swui-select-table/swui-select-table.interface';
import { SelectTableInputOption } from '../dynamic-form.model';

function asSelectOptions( data: SwuiSelectTableOption[] | Observable<SwuiSelectTableOption[]>,
                          loading$: BehaviorSubject<boolean> ): Observable<any> {
  loading$.next(true);
  const source = isObservable(data) ? data : of(data || []);
  return source.pipe(
    map(opts => opts.map(opt => ({
        ...opt,
        text: opt.text || opt.title || opt.displayName,
        id: opt.value || opt.id,
        disabled: opt.disabled,
        data: opt
      }))
    ),
    map(opts => opts.filter(( { id, text } ) => !!id && !!text)),
    tap(() => {
      loading$.next(false);
    })
  );
}

@Component({
    selector: 'lib-input-select-table',
    templateUrl: './input-select-table.component.html',
    styleUrls: ['input-select-table.component.scss'],
    standalone: false
})
export class InputSelectTableComponent {
  @Input() control?: UntypedFormControl;
  @Input() id = '';
  @Input() readonly = false;
  @Input() submitted = false;

  @Input() set componentOptions( value: SelectTableInputOption | undefined ) {
    this.selectOptions = asSelectOptions(value?.data || [], this.loading$);
    this.columns = value?.columns || [];
    this.rowsNumber = value?.rowsNumber;
    this.title = value?.title;
    this.showSearch = value?.showSearch;
    this.loading = value?.loading || false;
  }

  multiple = false;
  title?: string;
  selectOptions: Observable<any> = of([]);
  errorMessages?: { [key: string]: string };
  loading = false;

  showSearch?: boolean;
  searchPlaceholder?: string;

  emptyOptionDisabled?: boolean;

  disableAllOption?: boolean;
  columns: any[] = [];
  rowsNumber?: number;
  loading$ = new BehaviorSubject(false);

  clear( event: MouseEvent ) {
    event.stopPropagation();
    if (this.control) {
      this.control.setValue(null);
    }
  }
}

<div *ngIf="control" class="select-table">
  <div *ngIf="title" class="select-table-title">{{ title | translate }}</div>
  <lib-swui-select-table
    [formControl]="control"
    [attr.id]="id"
    [data]="selectOptions | async"
    [disableEmptyOption]="emptyOptionDisabled"
    [disableAllOption]="disableAllOption"
    [showSearch]="showSearch"
    [columns]="columns"
    [rowsNumber]="rowsNumber"
    [searchPlaceholder]="searchPlaceholder"
    [loading]="loading || (loading$ | async)"
  ></lib-swui-select-table>
</div>

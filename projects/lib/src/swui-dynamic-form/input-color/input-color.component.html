<ng-container *ngIf="control">
  <div class="sw-color">
    <div class="sw-color__wrapper">
      <input matInput
             readonly="readonly"
             class="sw-color__input"
             onKeyDown="return false"
             [colorPicker]="control.value"
             [style.background]="control.value"
             cpPosition="right"
             (cpToggleChange)="onToggleDialog($event)"
             (colorPickerChange)="handleColorChange($event)"
             [formControl]="control"
             [attr.id]="id"
             [required]="required">
      <button mat-icon-button
              matTooltip="{{'ALL.resetDefault' | translate}}"
              class="sw-color__reset"
              [disabled]="isIdentical"
              (click)="onResetClick($event)">
        <mat-icon>replay</mat-icon>
      </button>
    </div>
    <div class="sw-color__error">
      <lib-swui-control-messages
        [messages]="errorMessages"
        [control]="control"
        [force]="submitted"
      ></lib-swui-control-messages>
    </div>
    <div class="sw-color__label" *ngIf="title">
      {{title | translate}}
    </div>
  </div>
</ng-container>

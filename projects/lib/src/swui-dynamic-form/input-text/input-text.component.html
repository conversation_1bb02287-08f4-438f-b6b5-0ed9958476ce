<ng-container *ngIf="control" [ngSwitch]="type">
  <ng-container *ngSwitchCase="'textarea'">
    <mat-form-field [class.has-error]="control?.invalid" appearance="outline">
      <mat-label *ngIf="title" [attr.for]="id">{{title | translate}}</mat-label>
      <textarea
        *ngIf="autosize; else fixedSize"
        matInput
        cdkTextareaAutosize
        [cdkAutosizeMinRows]="autosize.minRows"
        [cdkAutosizeMaxRows]="autosize.maxRows"
        [formControl]="control"
        [attr.id]="id"
        [readonly]="readonly"
        [required]="required"></textarea>
      <ng-template #fixedSize>
        <textarea
          matInput
          [formControl]="control"
          [attr.id]="id"
          [readonly]="readonly"
          [required]="required"></textarea>
      </ng-template>
      <button
        class="clear-button"
        *ngIf="clearable && control.value"
        matSuffix
        mat-icon-button>
        <mat-icon (click)="clear($event)">close</mat-icon>
      </button>
      <mat-error>
        <lib-swui-control-messages
          [control]="control"
          [force]="submitted"
          [messages]="errorMessages"
        ></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
  </ng-container>
  <ng-container *ngSwitchCase="'url'">
    <mat-form-field [class.has-error]="control?.invalid" appearance="outline">
      <mat-label *ngIf="title" [attr.for]="id">{{title | translate}}</mat-label>
      <input matInput type="url" [formControl]="control" [attr.id]="id" [readonly]="readonly" [required]="required">
      <mat-error>
        <lib-swui-control-messages
          [control]="control"
          [force]="submitted"
          [messages]="errorMessages"
        ></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
  </ng-container>
  <ng-container *ngSwitchDefault>
    <mat-form-field [class.has-error]="control?.invalid" appearance="outline">
      <mat-label *ngIf="title" [attr.for]="id">{{title | translate}}</mat-label>
      <input matInput type="text" [formControl]="control" [attr.id]="id" [readonly]="readonly" [required]="required">
      <button
        class="clear-button"
        *ngIf="clearable && control.value"
        matSuffix
        mat-icon-button>
        <mat-icon (click)="clear($event)">close</mat-icon>
      </button>
      <mat-error>
        <lib-swui-control-messages
          [control]="control"
          [force]="submitted"
          [messages]="errorMessages"
        ></lib-swui-control-messages>
      </mat-error>
    </mat-form-field>
  </ng-container>
</ng-container>

<div class="swui-calendar">
  <div class="swui-calendar__header">
    <button
      (click)="prevMonth($event)"
      mat-icon-button
      class="swui-calendar__arrow swui-calendar__arrow--prev">
    </button>
    <div class="swui-calendar__date">
      {{monthNames[currentDate.month()]}}&nbsp;{{currentDate.year()}}
    </div>
    <button
      (click)="nextMonth($event)"
      mat-icon-button
      class="swui-calendar__arrow swui-calendar__arrow--next">
    </button>
  </div>
  <div class="swui-calendar__body">
    <table class="swui-calendar__table">
      <thead>
        <tr>
          <th *ngFor="let weekDay of weekDayNames" class="swui-calendar__th">
            {{weekDay}}
          </th>
        </tr>
      </thead>
      <tbody>
      <tr *ngFor="let week of currentMonth">
        <td *ngFor="let day of week" class="swui-calendar__td">
          <div
            (click)="selectDay(day, $event)"
            class="swui-calendar__inner"
            [ngClass]="{
              'swui-calendar__inner--today': isDayToday(day),
              'swui-calendar__inner--selected': isDaySelected(day),
              'swui-calendar__inner--disabled': isDayDisabled(day)
            }">
            {{day?.format('D')}}
          </div>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</div>

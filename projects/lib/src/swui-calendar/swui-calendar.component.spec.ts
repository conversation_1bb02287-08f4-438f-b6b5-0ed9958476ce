import { DebugElement } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import moment from 'moment';

import { SwuiCalendarComponent } from './swui-calendar.component';
import { CALENDAR_MODULES } from './swui-calendar.module';

function createFakeEvent( type: string ) {
  const event = document.createEvent('Event');
  event.initEvent(type, true, true);
  return event;
}

function dispatchFakeEvent( node: Node | Window, type: string ) {
  node.dispatchEvent(createFakeEvent(type));
}

describe('SwuiCalendarComponent', () => {
  let component: SwuiCalendarComponent;
  let fixture: ComponentFixture<SwuiCalendarComponent>;
  let testDate: string;
  let testMoment: moment.Moment;
  let host: DebugElement;
  let minDate: moment.Moment;
  let maxDate: moment.Moment;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [SwuiCalendarComponent],
      imports: [...CALENDAR_MODULES]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiCalendarComponent);
    component = fixture.componentInstance;
    host = fixture.debugElement;
    minDate = moment.utc().add(-2, 'days');
    maxDate = moment.utc().add(2, 'days');
    testDate = '2019-01-14T09:20:06.246Z';
    testMoment = moment.parseZone(testDate);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should disable calendar', () => {
    component.setDisabledState(true);
    expect(component.isCalendarDisabled).toBe(true);
  });

  it('should enable calendar', () => {
    component.setDisabledState(false);
    expect(component.isCalendarDisabled).toBe(false);
  });

  it('should set current date', () => {
    component.writeValue(testDate);
    expect(component.currentDate).toEqual(testMoment.startOf('day'), true);
  });

  it('should set current month weeks', () => {
    component.writeValue(testDate);
    expect(component.currentMonth.every(week => week instanceof Array)).toBe(true);
  });

  it('should set current month days', () => {
    component.writeValue(testDate);
    expect(component.currentMonth.every(week => week.every(day => day instanceof moment))).toBe(true);
  });

  it('should test isDayDisabled when false', () => {
    component.minDate = undefined;
    expect(component.isDayDisabled(moment.utc())).toBe(false);
  });

  it('should test isDayDisabled when it in minDate range', () => {
    component.minDate = minDate;
    expect(component.isDayDisabled(moment.utc().add(-3, 'days'))).toBe(true);
  });

  it('should test isDayDisabled when it in maxDate range', () => {
    component.maxDate = maxDate;
    expect(component.isDayDisabled(moment.utc().add(3, 'days'))).toBe(true);
  });

  it('should test isDaySelected when true', () => {
    component.selectedDate = moment.utc();
    expect(component.isDaySelected(moment.utc())).toBe(true);
  });

  it('should test isDaySelected when false', () => {
    component.selectedDate = moment.utc().clone().add(2, 'days');
    expect(component.isDaySelected(moment.utc())).toBe(false);
  });

  it('should test isDayToday when true', () => {
    expect(component.isDayToday(moment.utc())).toBe(true);
  });

  it('should test isDayToday when false', () => {
    expect(component.isDayToday(moment.utc().clone().add(2, 'days'))).toBe(false);
  });

  it('should set selected day on selectDay', () => {
    const day = moment.utc().clone();
    component.selectDay(day);
    expect(day.isSame(component.selectedDate)).toBe(true);
  });

  it('should preventDefault if event', () => {
    const event = new Event('click');
    spyOn(event, 'preventDefault');
    component.selectDay(testMoment, event);
    expect(event.preventDefault).toHaveBeenCalled();
  });

  it('should set month on selectDay if the month not the same', () => {
    const day = moment.utc().clone();
    component.currentDate = moment.utc().clone().add(1, 'months');
    component.selectDay(day);
    expect(component.currentDate.isSame(component.selectedDate, 'month')).toBe(true);
  });

  it('should not set selected day on selectDay when day is disabled', () => {
    component.maxDate = maxDate;
    const day = moment.utc().add(3, 'days').clone();
    component.selectDay(day);
    expect(day.isSame(component.selectedDate)).toBe(false);
  });

  it('should set prev month on prevMonth', () => {
    const prevMonth = moment.utc().clone().add(-1, 'months');
    component.currentDate = moment.utc().clone();
    component.prevMonth(new MouseEvent('test'));
    expect(component.currentDate.isSame(prevMonth, 'month')).toBe(true);
  });

  it('should set next month on nextMonth', () => {
    const nextMonth = moment.utc().clone().add(1, 'months');
    component.currentDate = moment.utc().clone();
    component.nextMonth(new MouseEvent('test'));
    expect(component.currentDate.isSame(nextMonth, 'month')).toBe(true);
  });

  it('should call onTouched on blur', () => {
    spyOn(component, 'onTouched');
    dispatchFakeEvent(host.nativeElement, 'blur');
    expect(component.onTouched).toHaveBeenCalled();
  });

  it('should set tabindex', () => {
    expect(host.nativeElement.getAttribute('tabindex')).toBe('0');
  });

  it('should set onChange in registerOnChange', () => {
    let test = false;
    const fn = () => {
      test = true;
    };
    component.registerOnChange(fn);
    component.selectDay(testMoment);
    expect(test).toBe(true);
  });

  it('should set onChange in registerOnTouched', () => {
    let test = false;
    const fn = () => {
      test = true;
    };
    component.registerOnTouched(fn);
    dispatchFakeEvent(host.nativeElement, 'blur');
    expect(test).toBe(true);
  });

});

$color-source: rgba(0,0,0,1);
$color-highlight: #3f51b5;
:host {
  &:focus {
    outline: none !important;
  }
}
.swui-calendar {
  width: 280px;
  min-height: 314px;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 44px;
  }
  &__body {
    height: 270px;
  }
  &__arrow {
    &:after {
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      position: absolute;
      content: '';
      margin: 15.5px;
      border: 0 solid rgba($color-source, .54);
      border-top-width: 2px;
    }
    &--prev {
      &:after {
        border-left-width: 2px;
        transform: translateX(2px) rotate(-45deg);
      }
    }
    &--next {
      &:after {
        border-right-width: 2px;
        transform: translateX(-2px) rotate(45deg);
      }
    }
  }
  &__table {
    width: 100%;
    border-collapse: collapse;
  }
  &__th {
    padding: 8px 0;
    font-size: 11px;
    font-weight: 400;
    text-transform: uppercase;
    color: rgba($color-source, .38);
    border-bottom: 1px solid rgba($color-source, .12);
  }
  &__td {
    position: relative;
    width: 14.2857%;
    padding-top: 7.14286%;
    padding-bottom: 7.14286%;
  }
  &__inner {
    position: absolute;
    top: 5%;
    left: 5%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    width: 90%;
    height: 90%;
    font-size: 13px;
    line-height: 1;
    border-width: 1px;
    border-style: solid;
    border-radius: 999px;
    color: rgba($color-source, .87);
    border-color: transparent;
    cursor: pointer;
    &:hover:not(.swui-calendar__inner--selected){
      background-color: rgba(0,0,0,.04);
    }
    &--today {
      border-color: rgba($color-source, .38);
    }
    &--selected {
      color: #fff;
    }
    &--disabled {
      color: rgba($color-source, .12);
      &:hover {
        background: transparent;
        cursor: not-allowed;
      }
    }
  }
}

import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import type { Meta, StoryObj } from '@storybook/angular';
import { moduleMetadata } from '@storybook/angular';

import { SwuiInputComponent } from './swui-input.component';
import { SwuiInputModule } from './swui-input.module';

const meta: Meta<SwuiInputComponent> = {
  title: 'Forms/Input',
  component: SwuiInputComponent,
  decorators: [
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        SwuiInputModule
      ],
    })
  ],
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<SwuiInputComponent>;

export const Default: Story = {
  args: {},
};

export const WithLabelAndPlaceholder: Story = {
  args: {
    label: 'Input label',
    placeholder: 'Search Item',
  },
  render: (args) => ({
    props: {
      ...args,
      ngModel: 'Init value',
    },
    template: `<lib-swui-input [label]="label" [placeholder]="placeholder" [ngModel]="ngModel"></lib-swui-input>`,
  }),
};

<div class="swui-timepicker" [formGroup]="form">

  <mat-form-field
    appearance="outline"
    [style.width.%]="elemWidth"
    class="swui-timepicker__item"
    *ngIf="!disable.hour">
    <mat-label>Hours</mat-label>
    <mat-select formControlName="hour">
      <mat-option *ngFor="let v of hours; let i = index" [disabled]="i < minTimes.hours || i > maxTimes.hours" [value]="i">{{ v }}</mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field
    appearance="outline"
    [style.width.%]="elemWidth"
    class="swui-timepicker__item"
    *ngIf="!disable.minute">
    <mat-label>Minutes</mat-label>
    <mat-select formControlName="minute">
      <mat-option *ngFor="let v of minutes; let i = index" [disabled]="i < minTimes.minutes || i > maxTimes.minutes" [value]="i">{{ v }}</mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field
    appearance="outline"
    [style.width.%]="elemWidth"
    class="swui-timepicker__item"
    *ngIf="!disable.second">
    <mat-label>Seconds</mat-label>
    <mat-select formControlName="second">
      <mat-option *ngFor="let v of seconds; let i = index" [disabled]="i < minTimes.seconds || i > maxTimes.seconds" [value]="i">{{ v }}</mat-option>
    </mat-select>
  </mat-form-field>

</div>

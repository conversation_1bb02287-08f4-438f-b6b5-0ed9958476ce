import { moduleMetadata, storiesOf } from '@storybook/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { SwuiTimepickerModule } from './swui-timepicker.module';
import { action } from 'storybook/actions';
import { Component, EventEmitter, Input, NgModule, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { SwuiTimepickerTimeDisableLevel } from './swui-timepicker.interface';


@Component({
  template: `
    <div [formGroup]="form">
      <lib-swui-timepicker formControlName="time" [format]="format" [timeDisableLevel]="timeDisableLevel"></lib-swui-timepicker>
    </div>
  `,
})
export class NoopComponent {
  @Input() set disabled( isDisabled: boolean | undefined ) {
    if (typeof isDisabled === 'undefined') {
      return;
    }
    isDisabled ? this.form.disable() : this.form.enable();
  }

  @Input() format: 'default' | 'minutes' | 'seconds' = 'default';
  @Input() timeDisableLevel: SwuiTimepickerTimeDisableLevel | undefined;
  @Output() changed: EventEmitter<any> = new EventEmitter();
  readonly form = new FormGroup({
    time: new FormControl()
  });

  constructor() {
    this.form.valueChanges.subscribe(values => {
      this.changed.emit(values);
    });
  }
}

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    SwuiTimepickerModule,
  ],
  declarations: [
    NoopComponent,
  ],
})
export class NoopModule {
}

storiesOf('Date/Timepicker', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        NoopModule,
      ],
    })
  )
  .add('default', () => ({
    component: NoopComponent,
    props: {
      changed: action('changed'),
    },
  }))
  .add('disabled/form', () => ({
    component: NoopComponent,
    props: {
      disabled: true,
      changed: action('changed'),
    },
  }))
  .add('hide[minutes,seconds]', () => ({
    component: NoopComponent,
    props: {
      changed: action('changed'),
      timeDisableLevel: {
        hour: true,
        minute: false,
        second: false
      }
    },
  }))
  .add('hide[seconds]', () => ({
    component: NoopComponent,
    props: {
      changed: action('changed'),
      timeDisableLevel: {
        second: false
      }
    },
  }))
  .add('format/minutes', () => ({
    component: NoopComponent,
    props: {
      changed: action('changed'),
      format: 'minutes'
    },
  }))
  .add('format/seconds', () => ({
    component: NoopComponent,
    props: {
      changed: action('changed'),
      format: 'seconds'
    },
  }));

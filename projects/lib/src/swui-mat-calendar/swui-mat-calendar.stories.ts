import type { Meta, StoryObj } from '@storybook/angular';
import { moduleMetadata } from '@storybook/angular';
import { MatCardModule } from '@angular/material/card';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import moment from 'moment';

import { MAT_CALENDAR_MODULES, SwuiMatCalendarModule } from './swui-mat-calendar.module';
import { SwuiMatCalendarComponent } from './swui-mat-calendar.component';


const template = `
  <mat-card style="margin: 32px">
    <lib-swui-mat-calendar
      [disabled]="disabled"
      [timeZone]="timeZone"
      [maxDate]="maxDate"
      [minDate]="minDate"
      [(ngModel)]="value">
    </lib-swui-mat-calendar>
  </mat-card>
`;

const meta: Meta<SwuiMatCalendarComponent> = {
  title: 'Date/MatCalendar',
  component: SwuiMatCalendarComponent,
  decorators: [
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        SwuiMatCalendarModule,
        MAT_CALENDAR_MODULES,
        MatCardModule,
      ],
    })
  ],
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<SwuiMatCalendarComponent>;

export const Default: Story = {
  args: {},
  render: (args) => ({
    props: args,
    template,
  }),
};

export const TimeZonePlus13: Story = {
  args: {
    timeZone: 'Pacific/Tongatapu',
  },
  render: (args) => ({
    props: args,
    template,
  }),
};

export const ValueWithTimezoneMinus1: Story = {
  args: {
    timeZone: 'America/Godthab',
    value: '2020-06-30T00:00:00.000Z'
  },
  render: (args) => ({
    props: args,
    template,
  }),
};

export const MinDateTodayMinus2: Story = {
  args: {
    minDate: moment().add(-2, 'days').toISOString(),
  },
  render: (args) => ({
    props: args,
    template,
  }),
};

export const MaxDateTodayPlus2: Story = {
  args: {
    maxDate: moment().add(2, 'days').toISOString()
  },
  render: (args) => ({
    props: args,
    template,
  }),
};

export const Disabled: Story = {
  args: {
    isDisabled: true
  },
  render: (args) => ({
    props: args,
    template,
  }),
};


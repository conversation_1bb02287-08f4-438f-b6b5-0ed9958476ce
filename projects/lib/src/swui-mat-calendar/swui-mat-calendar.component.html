<div
  class="swui-mat-calendar"
  [ngClass]="{
    'disabled': isDisabled,
    'date-range': isFromRange || isToRange,
    'date-from': isFromRange,
    'date-to': isToRange
  }">
  <div class="swui-mat-calendar__actions" [formGroup]="selectDateForm">
    <mat-form-field appearance="outline" class="swui-mat-calendar__field">
      <mat-select formControlName="month">
        <mat-option *ngFor="let month of months" [value]="month.id">{{month.text}}</mat-option>
      </mat-select>
    </mat-form-field>
    <mat-form-field appearance="outline" class="swui-mat-calendar__field">
      <mat-select formControlName="year">
        <mat-option *ngFor="let year of years" [value]="year">{{year}}</mat-option>
      </mat-select>
    </mat-form-field>
  </div>
  <table class="swui-mat-calendar__table">
    <thead>
    <tr>
      <th *ngFor="let weekDay of weekDayNames" class="swui-mat-calendar__th">
        <div class="swui-mat-calendar__th-inner">
          {{weekDay}}
        </div>
      </th>
    </tr>
    </thead>
    <tbody>
    <tr *ngFor="let week of currentMonth">
      <td *ngFor="let day of week" class="swui-mat-calendar__td">
        <div
          class="swui-mat-calendar__inner"
          (click)="selectDay(day, $event)"
          [ngClass]="{
              'selected': isDaySelected(day),
              'disabled': isDayDisabled(day),
              'min-date': isMinDate(day),
              'max-date': isMaxDate(day),
              'to-date': isToDate(day),
              'from-date': isFromDate(day),
              'range-date': isInRangeDate(day)
            }">
          <div
            class="swui-mat-calendar__border"
            [ngClass]="{ 'today': isToday(day) }">
            {{day?.format('D')}}
          </div>
        </div>
      </td>
    </tr>
    </tbody>
  </table>
</div>


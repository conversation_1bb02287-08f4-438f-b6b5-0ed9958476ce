import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SwuiMatCalendarComponent } from './swui-mat-calendar.component';
import { ReactiveFormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';


export const MAT_CALENDAR_MODULES = [
  MatSelectModule,
  MatFormFieldModule,
  ReactiveFormsModule,
];

@NgModule({
  declarations: [SwuiMatCalendarComponent],
  imports: [
    CommonModule,
    ...MAT_CALENDAR_MODULES,
  ],
  exports: [
    SwuiMatCalendarComponent,
  ]
})
export class SwuiMatCalendarModule {
}

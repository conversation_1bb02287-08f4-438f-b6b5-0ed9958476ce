$color-main: #2a2c44;
$color-sub: #C9CCD3;
$color-selected: #86AFFE;
$color-range: #EDF3FF;

:host:focus {
  outline: none !important;
}

.swui-mat-calendar {
  width: 210px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  &.date-range {
    .swui-mat-calendar {
      &__inner {
        &.range-date {
          background-color: $color-range;
        }
      }
    }

    &.date-from {
      .swui-mat-calendar {
        &__inner {
          &.selected {
            background-color: $color-selected;
            border-top-left-radius: 50%;
            border-bottom-left-radius: 50%;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
          }
          &.to-date {
            background-color: $color-selected;
            border-top-right-radius: 50%;
            border-bottom-right-radius: 50%;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            &.selected {
              border-top-left-radius: 50%;
              border-bottom-left-radius: 50%;
            }
          }
        }
      }
    }
    &.date-to {
      .swui-mat-calendar {
        &__inner {
          &.selected {
            background-color: $color-selected;
            border-top-right-radius: 50%;
            border-bottom-right-radius: 50%;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
          }
          &.from-date {
            background-color: $color-selected;
            border-top-left-radius: 50%;
            border-bottom-left-radius: 50%;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            &.selected {
              border-top-right-radius: 50%;
              border-bottom-right-radius: 50%;
            }
          }
        }
      }
    }
  }
  &__table {
    border-collapse: collapse;
    border-spacing: 0;
  }
  &__th,
  &__td {
    width: 30px;
    height: 34px;
    padding: 2px 0;
    font-size: 10px;
    font-weight: 400;
    line-height: 1;
    color: $color-main;
    text-align: center;
    vertical-align: middle;
  }
  &__th {
    padding: 0 0 8px;
    color: $color-sub;
    text-transform: uppercase;
  }
  &__th-inner {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    border-bottom: 1px solid $color-sub;
  }
  &__td {
    font-weight: 500;
  }
  &__inner {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    &.selected {
      border-radius: 50%;
      background: $color-selected;
    }
    &.disabled {
      color: $color-sub;
      cursor: not-allowed;
    }
  }
  &__border {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    cursor: pointer;
    &.today {
      border: 1px solid $color-main;
      border-radius: 50%;
    }

  }
  &__actions {
    display: flex;
    justify-content: center;
  }
  &__field {
    font-size: 10px;
    width: 75px;
    margin: 0 8px;
  }
}

import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { MAT_CALENDAR_MODULES } from './swui-mat-calendar.module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { DebugElement } from '@angular/core';
import moment from 'moment';

import { SwuiMatCalendarComponent } from './swui-mat-calendar.component';

function createFakeEvent( type: string ) {
  const event = document.createEvent('Event');
  event.initEvent(type, true, true);
  return event;
}

function dispatchFakeEvent( node: Node | Window, type: string ) {
  node.dispatchEvent(createFakeEvent(type));
}

describe('SwuiCalendarComponent', () => {
  let component: SwuiMatCalendarComponent;
  let fixture: ComponentFixture<SwuiMatCalendarComponent>;
  let host: DebugElement;
  let testIsoString: string;
  let testTimeZone: string;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [SwuiMatCalendarComponent],
      imports: [
        BrowserAnimationsModule,
        ...MAT_CALENDAR_MODULES
      ]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiMatCalendarComponent);
    component = fixture.componentInstance;
    host = fixture.debugElement;
    testIsoString = '2020-06-30T00:00:00.000Z';
    testTimeZone = 'Pacific/Tongatapu'; // -13
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should disable calendar', () => {
    expect(component.isDisabled).toBeFalsy();

    component.isDisabled = false;
    expect(component.isDisabled).toBeFalsy();

    component.isDisabled = true;
    expect(component.isDisabled).toBeTruthy();
  });

  it('should set tabindex', () => {
    expect(host.nativeElement.getAttribute('tabindex')).toBe('0');
  });

  it('should call onTouched on blur', () => {
    spyOn(component, 'onTouched');
    dispatchFakeEvent(host.nativeElement, 'blur');
    expect(component.onTouched).toHaveBeenCalled();
  });

  it('should set onChange in registerOnChange', () => {
    let test = false;
    const fn = () => {
      test = true;
    };
    component.registerOnChange(fn);
    component.selectDay(moment());
    expect(test).toBe(true);
  });

  it('should set onChange in registerOnTouched', () => {
    let test = false;
    const fn = () => {
      test = true;
    };
    component.registerOnTouched(fn);
    dispatchFakeEvent(host.nativeElement, 'blur');
    expect(test).toBe(true);
  });

  it('should set value', () => {
    component.value = testIsoString;
    expect(component.value).toBe(testIsoString);

    const nextMonthDay = moment(testIsoString).add(1, 'month').toISOString();
    component.value = nextMonthDay;
    expect(component.value).toBe(nextMonthDay);

    component.value = 'wrong_string';
    expect(component.value).toBe('');
  });

  it('should writeValue', () => {
    component.writeValue(testIsoString);
    expect(component.value).toBe(testIsoString);

    component.writeValue('wrong_string');
    expect(component.value).toBe('');
  });

  it('should set timeZone', () => {
    component.timeZone = testTimeZone;
    expect(component.timeZone).toBe(testTimeZone);
  });

  it('should set currentMonth', () => {
    component.ngOnInit();
    expect(component.currentMonth).toBeTruthy();
  });

  it('should set weekDayNames', () => {
    expect(component.weekDayNames).toEqual(moment.weekdaysShort());
  });

  it('should init selectDateForm', () => {
    expect(component.selectDateForm).toBeTruthy();
  });

  it('should select day', () => {
    spyOn(component, 'onChange');
    const day = moment().clone().add(1, 'month').utc().startOf('day');
    component.selectDay(day, createFakeEvent('click'));

    expect(component.onChange).toHaveBeenCalledWith(day.toISOString());
    expect(component.value).toBe(day.toISOString());
  });

  it('should not select day when disabled', () => {
    const today = moment().clone().utc().startOf('day');
    component.setDisabledState(true);
    component.selectDay(today);

    expect(component.value).toBe('');
  });

  it('should return isDaySelected', () => {
    const today = moment().clone().utc().startOf('day');

    component.selectDay(today.clone().add(1, 'day'));
    expect(component.isDaySelected(today)).toBe(false);

    component.selectDay(today.clone());
    expect(component.isDaySelected(today)).toBe(true);
  });

  it('should set minDate', () => {
    const testDate = moment().clone().add(-1, 'day');
    component.minDate = testDate.toISOString();

    expect(component.minDate).toEqual(testDate.toISOString());
  });

  it('should set maxDate', () => {
    const testDate = moment().clone().add(-1, 'day');
    component.maxDate = testDate.toISOString();

    expect(component.maxDate).toEqual(testDate.toISOString());
  });

  it('should return isDayDisabled', () => {
    const today = moment().clone().utc().startOf('day');
    component.minDate = today.clone().add(-1, 'day').toISOString();
    component.maxDate = today.clone().add(1, 'day').toISOString();
    component.timeZone = testTimeZone;

    expect(component.isDayDisabled(today)).toBe(false);
    expect(component.isDayDisabled(today.clone().add(-3, 'day'))).toBe(true);
    expect(component.isDayDisabled(today.clone().add(3, 'day'))).toBe(true);

    component.setDisabledState(true);
    expect(component.isDayDisabled(today)).toBe(true);
  });

});

import { Directive, ElementRef, HostListener } from '@angular/core';

@Directive({
    // eslint-disable-next-line @angular-eslint/directive-selector
    selector: 'input[select-on-click], textarea[select-on-click]',
    standalone: false
})
export class SelectOnClickDirective {

  constructor( elementRef: ElementRef ) {
    elementRef.nativeElement.style.cursor = 'text';
  }

  @HostListener('click', ['$event']) onClick( { target }: any ) {
    target.select();
  }
}

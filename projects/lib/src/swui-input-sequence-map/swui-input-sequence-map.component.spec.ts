import { ComponentFix<PERSON>, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { SwuiInputSequenceMapComponent } from './swui-input-sequence-map.component';

describe('SwuiInputSequenceMapComponent', () => {
  let component: SwuiInputSequenceMapComponent;
  let fixture: ComponentFixture<SwuiInputSequenceMapComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [SwuiInputSequenceMapComponent],
      imports: [
        ReactiveFormsModule,
        MatFormFieldModule,
        MatSelectModule,
        MatButtonModule,
        MatIconModule,
        NoopAnimationsModule
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(SwuiInputSequenceMapComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with empty form array when no value', () => {
    component.writeValue({});
    expect(component.formArray.length).toBe(1);
  });

  it('should initialize with existing values', () => {
    const initialValue = {
      'USD': 'US Dollar',
      'EUR': 'Euro'
    };
    component.writeValue(initialValue);
    expect(component.formArray.length).toBe(2);
    expect(component.getSourceControl(0)?.value).toBe('USD');
    expect(component.getTargetControl(0)?.value).toBe('US Dollar');
    expect(component.getSourceControl(1)?.value).toBe('EUR');
    expect(component.getTargetControl(1)?.value).toBe('Euro');
  });

  it('should add pair', () => {
    component.writeValue({});
    const initialLength = component.formArray.length;
    component.addPair('USD', 'US Dollar');
    expect(component.formArray.length).toBe(initialLength + 1);
  });

  it('should remove pair', () => {
    component.writeValue({});
    component.addPair('USD', 'US Dollar');
    component.addPair('EUR', 'Euro');
    const initialLength = component.formArray.length;
    component.removePair(0);
    expect(component.formArray.length).toBe(initialLength - 1);
  });

  it('should not remove last pair', () => {
    component.writeValue({});
    expect(component.formArray.length).toBe(1);
    component.removePair(0);
    expect(component.formArray.length).toBe(1); // Should still have one pair
  });

  it('should call onChange when form value changes', () => {
    const onChangeSpy = jasmine.createSpy('onChange');
    component.registerOnChange(onChangeSpy);

    component.writeValue({});
    component.addPair('USD', 'US Dollar');

    // Trigger form change
    component.getSourceControl(0).setValue('EUR');

    expect(onChangeSpy).toHaveBeenCalled();
  });

  it('should disable form when setDisabledState is called', () => {
    component.writeValue({});
    component.setDisabledState(true);

    expect(component.readonly).toBe(true);
    expect(component.formArray.disabled).toBe(true);
  });

  it('should enable form when setDisabledState is called with false', () => {
    component.writeValue({});
    component.setDisabledState(false);

    expect(component.readonly).toBe(false);
    expect(component.formArray.enabled).toBe(true);
  });
});

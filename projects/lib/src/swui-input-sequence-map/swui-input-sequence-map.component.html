<div class="sequence-map-container">
  <div class="sequence-map-header" *ngIf="options?.title">
    <label class="sequence-map-title">{{ options?.title }}</label>
  </div>

  <div class="sequence-map-items">
    <div
      class="sequence-map-item"
      *ngFor="let group of formArray.controls; let i = index"
      [formGroup]="group">

      <div class="sequence-map-fields">
        <mat-form-field appearance="outline" class="sequence-source-field" *ngIf="options?.sourceOptions">
          <mat-label *ngIf="options?.sourcePlaceholder">{{ options?.sourcePlaceholder }}</mat-label>
          <mat-select
            formControlName="source"
            [attr.id]="id + '_source_' + i"
            [disabled]="readonly">
            <mat-option
              *ngFor="let option of options?.sourceOptions"
              [value]="option.id"
              [disabled]="option.disabled">
              {{ option.text }}
            </mat-option>
          </mat-select>
          <mat-error *ngIf="hasSourceError(i)">
            {{ getSourceErrorMessage(i) }}
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="sequence-target-field" *ngIf="options?.targetOptions">
          <mat-label *ngIf="options?.targetPlaceholder">{{ options?.targetPlaceholder }}</mat-label>
          <mat-select
            formControlName="target"
            [attr.id]="id + '_target_' + i"
            [disabled]="readonly">
            <mat-option
              *ngFor="let option of options?.targetOptions"
              [value]="option.id"
              [disabled]="option.disabled">
              {{ option.text }}
            </mat-option>
          </mat-select>
          <mat-error *ngIf="hasTargetError(i)">
            {{ getTargetErrorMessage(i) }}
          </mat-error>
        </mat-form-field>

        <button
          type="button"
          mat-icon-button
          class="sequence-remove-button"
          [disabled]="readonly || formArray.length <= 1"
          (click)="removePair(i)"
          [attr.aria-label]="options?.removeButtonLabel">
          <mat-icon>delete</mat-icon>
          <span *ngIf="options?.removeButtonLabel">{{ options?.removeButtonLabel }}</span>
        </button>
      </div>
    </div>
  </div>

  <div class="sequence-map-actions" *ngIf="!readonly">
    <button
      type="button"
      mat-stroked-button
      class="sequence-add-button"
      (click)="addPair()"
      [attr.aria-label]="options?.addButtonLabel">
      <mat-icon>add</mat-icon>
      <span *ngIf="options?.addButtonLabel">{{ options?.addButtonLabel }}</span>
    </button>
  </div>
</div>

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

import { SwuiInputSequenceMapComponent } from './swui-input-sequence-map.component';

@NgModule({
  declarations: [
    SwuiInputSequenceMapComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule
  ],
  exports: [
    SwuiInputSequenceMapComponent
  ]
})
export class SwuiInputSequenceMapModule { }

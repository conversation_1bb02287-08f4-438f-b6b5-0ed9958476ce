.sequence-map-container {
  width: 100%;
}

.sequence-map-header {
  margin-bottom: 16px;
}

.sequence-map-title {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
  font-size: 14px;
}

.sequence-map-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.sequence-map-item {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;
}

.sequence-map-fields {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.sequence-source-field {
  flex: 0 0 200px;
  min-width: 150px;
}

.sequence-target-field {
  flex: 1;
  min-width: 200px;
}

.sequence-remove-button {
  flex: 0 0 auto;
  margin-top: 8px;
  color: rgba(0, 0, 0, 0.54);

  &:hover:not(:disabled) {
    color: #f44336;
  }

  &:disabled {
    opacity: 0.5;
  }
}

.sequence-map-actions {
  margin-top: 16px;
  display: flex;
  justify-content: flex-start;
}

.sequence-add-button {
  display: flex;
  align-items: center;
  gap: 8px;

  mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .sequence-map-fields {
    flex-direction: column;
    gap: 8px;
  }

  .sequence-source-field,
  .sequence-target-field {
    flex: 1;
    width: 100%;
  }

  .sequence-remove-button {
    align-self: flex-end;
    margin-top: 0;
  }
}

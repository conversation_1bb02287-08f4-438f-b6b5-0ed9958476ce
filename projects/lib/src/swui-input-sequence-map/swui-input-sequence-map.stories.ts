import { moduleMetadata, storiesOf } from '@storybook/angular';
import { action } from 'storybook/actions';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { SwuiInputSequenceMapComponent, SwuiSequenceMapOptions } from './swui-input-sequence-map.component';

storiesOf('Forms/SequenceMap', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        ReactiveFormsModule,
        MatFormFieldModule,
        MatSelectModule,
        MatButtonModule,
        MatIconModule,
        BrowserAnimationsModule
      ],
    })
  )
  .add('ControlValueAccessor', () => ({
    component: SwuiInputSequenceMapComponent,
    props: {
      ngModel: true,
      ngModelChange: action('ngModelChange'),
        options: {
          title: 'Currency Mapping',
          addButtonLabel: 'Add',
          removeButtonLabel: 'Remove',
          sourcePlaceholder: 'From',
          targetPlaceholder: 'To',
          sourceOptions: [
            { id: 'USD', text: 'US Dollar' },
            { id: 'EUR', text: 'Euro' },
            { id: 'GBP', text: 'British Pound' },
            { id: 'JPY', text: 'Japanese Yen' },
            { id: 'CAD', text: 'Canadian Dollar' },
            { id: 'AUD', text: 'Australian Dollar' }
          ],
          targetOptions: [
            { id: 'USD', text: 'US Dollar' },
            { id: 'EUR', text: 'Euro' },
            { id: 'GBP', text: 'British Pound' },
            { id: 'JPY', text: 'Japanese Yen' },
            { id: 'CAD', text: 'Canadian Dollar' },
            { id: 'AUD', text: 'Australian Dollar' }
          ]
        } as SwuiSequenceMapOptions

    },
  }));

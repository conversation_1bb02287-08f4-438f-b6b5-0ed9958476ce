# SwuiInputSequenceMapComponent

A standalone Angular component that implements ControlValueAccessor for mapping one value to another using select dropdowns. The component manages a `Record<string, string>` data structure and can be used as a form control.

## Features

- **ControlValueAccessor Implementation**: Works seamlessly with Angular reactive forms
- **Standalone Component**: No dependency on external form controls
- **Customizable Options**: Configurable labels, placeholders, and select options
- **Responsive Design**: Adapts to different screen sizes
- **Validation Support**: Built-in required validation with error messages
- **Dynamic Management**: Add/remove sequence pairs dynamically

## Installation

This component is part of the `@skywind-group/lib-swui` library.

```bash
npm install @skywind-group/lib-swui
```

## Usage

### Basic Usage

```typescript
import { FormControl } from '@angular/forms';
import { SwuiInputSequenceMapModule } from '@skywind-group/lib-swui';

// In your module
@NgModule({
  imports: [SwuiInputSequenceMapModule]
})

// In your component
export class MyComponent {
  sequenceControl = new FormControl({
    'USD': 'US Dollar',
    'EUR': 'Euro'
  });
}
```

```html
<lib-swui-input-sequence-map
  [formControl]="sequenceControl"
  [options]="sequenceOptions">
</lib-swui-input-sequence-map>
```

### Advanced Configuration

```typescript
import { SwuiSequenceMapOptions } from '@skywind-group/lib-swui';

export class MyComponent {
  sequenceOptions: SwuiSequenceMapOptions = {
    title: 'Currency Mapping',
    addButtonLabel: 'Add Currency Mapping',
    removeButtonLabel: 'Remove Mapping',
    sourcePlaceholder: 'Source Currency',
    targetPlaceholder: 'Target Currency',
    sourceOptions: [
      { id: 'USD', text: 'US Dollar' },
      { id: 'EUR', text: 'Euro' },
      { id: 'GBP', text: 'British Pound' }
    ],
    targetOptions: [
      { id: 'USD', text: 'US Dollar' },
      { id: 'EUR', text: 'Euro' },
      { id: 'GBP', text: 'British Pound' }
    ]
  };
}
```

## API

### Inputs

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `id` | `string` | `undefined` | Unique identifier for the component |
| `readonly` | `boolean` | `false` | Whether the component is read-only |
| `submitted` | `boolean` | `false` | Whether the form has been submitted (affects validation display) |
| `options` | `SwuiSequenceMapOptions` | `undefined` | Configuration options for the component |

### SwuiSequenceMapOptions Interface

| Property | Type | Description |
|----------|------|-------------|
| `title` | `string` | Optional title displayed above the component |
| `addButtonLabel` | `string` | Label for the add button (default: "Add Currency") |
| `removeButtonLabel` | `string` | Label for remove buttons (default: "Remove") |
| `sourcePlaceholder` | `string` | Placeholder for source select (default: "Source Sequence") |
| `targetPlaceholder` | `string` | Placeholder for target select (default: "Target Sequence") |
| `sourceOptions` | `SelectOptionItem[]` | Options for source select dropdown |
| `targetOptions` | `SelectOptionItem[]` | Options for target select dropdown |

### SelectOptionItem Interface

| Property | Type | Description |
|----------|------|-------------|
| `id` | `any` | Unique identifier for the option |
| `text` | `string` | Display text for the option |
| `disabled` | `boolean` | Whether the option is disabled |

## Output Format

The component outputs a `Record<string, string>` where:
- **Key**: Source sequence identifier
- **Value**: Target sequence identifier

Example output:
```json
{
  "USD": "US Dollar",
  "EUR": "Euro",
  "GBP": "British Pound"
}
```

## Validation

The component includes built-in validation:
- Each source and target field is required
- Error messages are displayed when validation fails
- Validation state is managed through the ControlValueAccessor interface

## Styling

The component uses Angular Material theming and includes responsive design. Custom styles can be applied by targeting the component's CSS classes:

```scss
lib-swui-input-sequence-map {
  .sequence-map-container {
    // Custom container styles
  }
  
  .sequence-map-item {
    // Custom item styles
  }
}
```

## Examples

See the Storybook stories for interactive examples and different configuration options.

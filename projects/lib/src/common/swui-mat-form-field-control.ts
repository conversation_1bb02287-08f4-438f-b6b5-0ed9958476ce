import { ControlValueAccessor, UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';
import { Directive, Do<PERSON><PERSON><PERSON>, ElementRef, HostBinding, Input, OnDestroy } from '@angular/core';
import { ErrorStateMatcher } from '@angular/material/core';
import { Subject } from 'rxjs';
import { FocusMonitor } from '@angular/cdk/a11y';
import { MatFormFieldControl } from '@angular/material/form-field';
import { coerceBooleanProperty } from '@angular/cdk/coercion';

@Directive()
// eslint-disable-next-line:directive-class-suffix
export abstract class SwuiMatFormFieldControl<T> implements <PERSON><PERSON><PERSON><PERSON>, OnDestroy, ControlValueAccessor, MatFormFieldControl<T> {
  abstract id: string;
  abstract value: T | null;
  abstract empty: boolean;
  abstract shouldLabelFloat: boolean;

  @Input() placeholder = '';

  @Input()
  set required( required: boolean ) {
    this._required = coerceBooleanProperty(required);
    this.stateChanges.next(undefined);
  }

  get required() {
    return this._required;
  }

  @Input()
  get disabled(): boolean {
    return this._disabled;
  }

  set disabled( value: boolean ) {
    this._disabled = coerceBooleanProperty(value);
    this.onDisabledState(value);
    this.stateChanges.next(undefined);
  }

  @HostBinding('attr.aria-describedby') describedBy = '';

  controlType?: string;
  autofilled?: boolean;
  userAriaDescribedBy?: string;

  errorState = false;
  focused = false;

  readonly stateChanges = new Subject<void>();
  readonly destroyed$ = new Subject<void>();

  private _required = false;
  private _disabled = false;

  onChange = ( _: any ) => {
  };

  onTouched = () => {
  };

  protected constructor( private readonly fm: FocusMonitor,
                         readonly elRef: ElementRef<HTMLElement>,
                         readonly ngControl: NgControl,
                         private readonly parentFormGroup: FormGroupDirective,
                         private readonly errorStateMatcher: ErrorStateMatcher ) {
    if (this.ngControl !== null) {
      this.ngControl.valueAccessor = this;
    }
    fm.monitor(elRef.nativeElement, true).subscribe(origin => {
      if (this.focused && !origin) {
        this.onTouched();
      }
      this.focused = !!origin;
      this.stateChanges.next(undefined);
    });
  }

  ngDoCheck() {
    if (this.ngControl) {
      const oldState = this.errorState;
      const parent = this.parentFormGroup;
      const matcher = this.errorStateMatcher;
      const control = this.ngControl ? this.ngControl.control as UntypedFormControl : null;
      const newState = matcher.isErrorState(control, parent) || this.isErrorState();
      if (newState !== oldState) {
        this.errorState = newState;
        this.stateChanges.next(undefined);
      }
    }
  }

  ngOnDestroy(): void {
    this.fm.stopMonitoring(this.elRef.nativeElement);
    this.stateChanges.complete();
    this.destroyed$.next(undefined);
    this.destroyed$.complete();
  }

  registerOnChange( fn: any ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( value: boolean ) {
    this._disabled = value;
    this.onDisabledState(value);
  }

  abstract writeValue( value: any ): void;

  setDescribedByIds( ids: string[] ) {
    this.describedBy = ids.join(' ');
  }

  abstract onContainerClick( event: MouseEvent ): void;

  protected onDisabledState( _: boolean ): void {
  }

  protected isErrorState(): boolean {
    return false;
  }
}

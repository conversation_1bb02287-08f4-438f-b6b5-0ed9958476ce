import { Component, ElementRef, forwardRef, Input, ViewChild } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { ThemePalette } from '@angular/material/core';
import { FloatLabelType } from '@angular/material/form-field';

@Component({
    selector: 'lib-swui-search',
    templateUrl: './swui-search.component.html',
    styleUrls: [
        './swui-search.component.scss',
    ],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => SwuiSearchComponent),
            multi: true,
        }
    ],
    standalone: false
})
export class SwuiSearchComponent implements ControlValueAccessor {

  @Input() label = '';
  @Input() placeholder = '';
  @ViewChild('input', { static: true }) input: ElementRef | undefined;

  @Input() color: ThemePalette;
  @Input() floatLabel: FloatLabelType | undefined;
  @Input() hideRequiredMarker: boolean | undefined;
  @Input() hintLabel: string | undefined;
  @Input() fields = '';

  disabled = false;

  onChange: any = () => {
  };

  onTouch: any = () => {
  };

  writeValue( value: any ) {
    if (this.input) {
      this.input.nativeElement.value = value && value.text || '';
    }
  }

  registerOnChange( fn: any ) {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ) {
    this.onTouch = fn;
  }

  setDisabledState?( isDisabled: boolean ): void {
    this.disabled = isDisabled;
  }

  onInputChange( value: string ) {
    if (!value) {
      this.onChange(null);
      return;
    }

    this.onChange({
      text: value,
      fields: this.fields
    });
  }
}

import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatToolbarModule } from '@angular/material/toolbar';
import { TranslateModule } from '@ngx-translate/core';
import { SwHubAuthService } from '../services/sw-hub-auth/sw-hub-auth.service';
import { SwHubConfigService } from '../services/sw-hub-config/sw-hub-config.service';
import { SwHubEntityService } from '../services/sw-hub-entity/sw-hub-entity.service';
import { SwHubInitService } from '../services/sw-hub-init/sw-hub-init.service';
import { SWUI_HUB_MESSAGE_CONFIG } from '../services/sw-hub-init/sw-hub-init.token';
import { HubSelectorModule } from './hub-selector/hub-selector.module';
import { LanguageSelectorModule } from './language-selector/language-selector.module';
import { SwuiTopMenuComponent } from './swui-top-menu.component';
import { UserMenuModule } from './user-menu/user-menu.module';


describe('TopMenuComponent', () => {
  let component: SwuiTopMenuComponent;
  let fixture: ComponentFixture<SwuiTopMenuComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [SwuiTopMenuComponent],
      imports: [
        TranslateModule.forRoot(),
        MatToolbarModule,
        MatButtonModule,
        MatIconModule,
        HubSelectorModule,
        UserMenuModule,
        LanguageSelectorModule,
        MatMenuModule
      ],
      providers: [
        { provide: SWUI_HUB_MESSAGE_CONFIG, useValue: {} },
        { provide: SwHubConfigService, useValue: {} },
        {
          provide: SwHubAuthService, useValue: {
            isLogged() {
              return true;
            },
            allowedTo() {
              return true;
            },
          }
        },
        { provide: SwHubInitService, useValue: {} },
        SwHubEntityService,
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiTopMenuComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

import { Component, EventEmitter, Inject, Input, Output } from '@angular/core';
import { AppSettings } from '../services/settings/app-settings';
import { SwHubAuthService } from '../services/sw-hub-auth/sw-hub-auth.service';
import { HubConfig, SwHubConfigService } from '../services/sw-hub-config/sw-hub-config.service';
import { SwHubMessageModuleConfig } from '../services/sw-hub-init/sw-hub-init.model';
import { SwHubInitService } from '../services/sw-hub-init/sw-hub-init.service';
import { SWUI_HUB_MESSAGE_CONFIG } from '../services/sw-hub-init/sw-hub-init.token';

@Component({
    selector: 'lib-swui-top-menu',
    templateUrl: './swui-top-menu.component.html',
    styleUrls: ['./swui-top-menu.component.scss'],
    standalone: false
})
export class SwuiTopMenuComponent {
  @Output() sidebarChanges = new EventEmitter<void>();
  @Output() settingsClick = new EventEmitter<string>();
  @Input() showSearch = false;

  readonly logo: string | undefined;
  readonly logoSymbols: string | undefined;

  constructor( @Inject(SWUI_HUB_MESSAGE_CONFIG) { logo, logoSymbols }: SwHubMessageModuleConfig,
               private readonly auth: SwHubAuthService,
               private readonly hubService: SwHubInitService,
               private readonly configService: SwHubConfigService ) {
    this.logo = configService.logo?.main ?? logo;
    this.logoSymbols = configService.logo?.white ?? logoSymbols;
  }

  get isLogged(): boolean {
    return this.auth.isLogged();
  }

  get username(): string | undefined {
    return this.auth.username;
  }

  get entityKey(): string | undefined {
    return this.auth.entityKey;
  }

  get isSuperAdminOnly(): boolean {
    return this.auth.isSuperAdmin;
  }

  get hasTwoFactor(): boolean {
    return this.auth.isTwoFactor;
  }

  get config(): HubConfig {
    return this.configService;
  }

  get envName(): string {
    return this.config.envName ? this.config.envName.toLocaleUpperCase() : '-';
  }

  get locationName(): string {
    return this.config.locationName ? this.config.locationName.toLocaleUpperCase() : '-';
  }

  selectLang( lang: string ) {
    this.hubService.sendLocale(lang);
  }

  setSettings( value: AppSettings ) {
    this.hubService.sendAppSettings(value);
  }

  onLogout() {
    this.hubService.sendLogout();
  }
}

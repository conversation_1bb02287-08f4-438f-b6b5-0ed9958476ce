.sw-tool-bar {
  padding: 0;
  background: #333758;
  color: #fff;
  height: 54px;

  &__hubs {
    height: 100%;
  }

  &__toggle {
    display: none;
    @media (max-width: 767px) {
      display: block;
    }
  }

  &__right {
    display: flex;
    height: 100%;
    margin-left: auto;
  }

  &__item {
    height: 100%;
  }
}

.sw-logo {
  display: flex;
  align-items: center;
  height: 100%;
  width: 250px;
  padding: 0 16px;
  transition: width 0.3s ease;

  &__icon {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    background-color: #333758;
    flex-shrink: 0;

    img {
      display: block;
      height: auto;
      width: 100%;
    }
  }

  &__text {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 7px;
    font-size: 14px;
    font-weight: 400;
    text-transform: uppercase;

    img {
      width: 70px;
    }
  }

  &__menu {
    display: block;
    margin: 8px;
    font-size: 16px;
    align-self: center;
    border-radius: 2px;
    padding: 0 8px;
  }

  @media(max-width: 1024px) {
    width: 54px;
    &__text {
      display: none;
    }
  }
  @media(max-width: 767px) {
    display: none;
  }
}

.bg-color {
  &__qa {
    background-color: #f57705;
  }

  &__stg {
    background-color: #0764f5;
  }

  &__prod {
    background-color: #1fa91d;
  }
}

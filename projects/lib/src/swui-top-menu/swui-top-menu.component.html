<mat-toolbar class="sw-tool-bar">
  <div class="sw-tool-bar__logo sw-logo">
    <div class="sw-logo__icon" *ngIf="logo">
      <img [src]="logo" alt>
    </div>
    <div class="sw-logo__text" *ngIf="logo">
      <img [src]="logoSymbols" alt>
    </div>
  </div>
  <lib-swui-hub-selector class="sw-tool-bar__hubs" *ngIf="isLogged"></lib-swui-hub-selector>

  <div class="sw-tool-bar__right">
    <div class="sw-logo__menu" [ngClass]="'bg-color__' + envName.toLowerCase()">
      {{ envName }} / {{ locationName }}
    </div>

    <lib-swui-entity-picker
      *ngIf="isLogged"
      (settingsClick)="settingsClick.emit($event)"
      [showSearch]="showSearch"
      class="sw-tool-bar__item">
    </lib-swui-entity-picker>

    <lib-swui-user-menu
      *ngIf="isLogged"
      class="sw-tool-bar__item"
      [username]="username"
      [entityKey]="entityKey"
      [hasTwoFactor]="hasTwoFactor"
      (languageChanges)="selectLang($event)"
      (settingsChanges)="setSettings($event)"
      (logout)="onLogout()">
    </lib-swui-user-menu>

    <lib-swui-language-selector
      *ngIf="!isLogged"
      class="sw-tool-bar__item"
      (valueChanges)="selectLang($event)">
    </lib-swui-language-selector>
  </div>
</mat-toolbar>

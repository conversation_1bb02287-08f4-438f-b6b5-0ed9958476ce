$search-color: rgba(0, 0, 0, 0.6);
$text-color-main: rgba(0, 0, 0, 0.87);
$text-color-selected: #fff;
$bg-color-main: #fff;
$bg-color-sub: #F6F6F6;
$color-primary: #1468cf;

.entity-picker {
  position: relative;
  display: flex;
  align-items: center;
  height: 100%;
  min-height: 48px;
  width: 100%;
  padding: 0 38px 0 40px;

  &__postfix {
    position: absolute;
    top: 50%;
    left: 8px;
    margin-top: -12px;
    color: $text-color-selected;
    cursor: pointer;

    &.disabled {
      opacity: .5;
      cursor: default;
      pointer-events: none;
    }
  }

  &__suffix {
    position: absolute;
    top: 50%;
    right: 8px;
    margin-top: -12px;
    color: $text-color-selected;
    cursor: pointer;
  }

  &__text {
    width: 100%;
    font-size: 14px;
    font-weight: 400;
    color: $text-color-selected;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    cursor: pointer;
  }

  &__menu {
    background: $bg-color-main;
    border-radius: 8px;
    box-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
    overflow: hidden;
  }

  &__search {
    position: relative;
  }

  &__loop {
    display: block;
    position: absolute;
    left: 13px;
    top: 50%;
    margin-top: -8px;
    font-size: 19px;
    line-height: 18px;
    width: 19px;
    height: 18px;
    color: $search-color;
  }

  &__input {
    display: block;
    width: 100%;
    height: 40px;
    margin: 0;
    padding: 0 15px 0 38px;
    font-size: 14px;
    line-height: 20px;
    border: unset;
    color: $search-color;
    background: $bg-color-sub;
    transition: box-shadow .15s ease-in-out;
    &:hover {
      cursor: pointer;
      box-shadow: inset 0px -1px 0px #000;
    }
    &:focus {
      outline: none !important;
      box-shadow: inset 0px -2px 0px $color-primary
    }
  }

  &__list {
    height: 360px;
    overflow: auto;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c4c4c4;
      border-radius: 0;
    }

    &::-webkit-scrollbar-track {
      background: $bg-color-main;
      border-radius: 0;
    }
  }

  &__item {
    position: relative;
    height: 32px;
    padding-right: 60px;

    &:hover {
      background-color: $bg-color-sub;

      .entity-picker {
        &__select {
          visibility: visible;
        }
      }
    }

    &--selected {
      color: $color-primary;
      background-color: $bg-color-sub;
    }
  }

  &__name {
    position: relative;
    display: flex;
    align-items: center;
    height: 100%;
    width: 100%;
    padding-left: 17px;
    font-size: 14px;
    line-height: 20px;

    &--parent {
      &:after {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        margin-top: -3px;
        display: block;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 4px 0 4px 6px;
        border-color: transparent transparent transparent $text-color-main;
      }
    }

    &--expanded {
      &:after {
        margin-top: -4px;
        border-width: 6px 4px 0 4px;
        border-color: $text-color-main transparent transparent transparent;
      }
    }

    &--root {
      padding-left: 12px;
    }
  }

  &__inner {
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  &__select {
    position: absolute;
    top: 50%;
    right: 8px;
    margin-top: -12px;
    display: inline-flex;
    align-items: center;
    height: 24px;
    padding: 0 8px;
    font-size: 12px;
    line-height: 20px;
    color: $color-primary;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 12px;
    background-color: $bg-color-main;
    visibility: hidden;
    cursor: pointer;
    transition: border-color .15s ease-in-out;
    &:hover {
      border-color: $color-primary;
    }
  }
}

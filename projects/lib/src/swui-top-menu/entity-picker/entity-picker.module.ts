import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { EntityPickerComponent } from './entity-picker.component';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatRippleModule } from '@angular/material/core';
import { ReactiveFormsModule } from '@angular/forms';


@NgModule({
    imports: [
        CommonModule,
        TranslateModule,
        MatRippleModule,
        MatMenuModule,
        MatIconModule,
        MatInputModule,
        ReactiveFormsModule,
        MatTooltipModule,
    ],
  declarations: [EntityPickerComponent],
  exports: [EntityPickerComponent],
})
export class EntityPickerModule {
}

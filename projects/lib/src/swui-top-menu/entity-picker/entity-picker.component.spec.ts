import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';
import { EntityPickerComponent } from './entity-picker.component';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatRippleModule } from '@angular/material/core';
import { LanguageSelectorModule } from '../language-selector/language-selector.module';
import { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';
import { SwuiSettingsDialogModule } from '../settings-dialog/swui-settings-dialog.module';
import { SwHubEntityService } from '../../services/sw-hub-entity/sw-hub-entity.service';
import { SwHubInitService } from '../../services/sw-hub-init/sw-hub-init.service';


describe('EntityPickerComponent', () => {
  let component: EntityPickerComponent;
  let fixture: ComponentFixture<EntityPickerComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [EntityPickerComponent],
      imports: [
        TranslateModule.forRoot(),
        MatRippleModule,
        MatIconModule,
        MatMenuModule,
        MatDividerModule,
        MatDialogModule,
        LanguageSelectorModule,
        SwuiSettingsDialogModule
      ],
      providers: [
        {
          provide: MatDialog, useValue: {
            open: () => {
            }
          }
        },
        { provide: SWUI_HUB_MESSAGE_CONFIG, useValue: {} },
        { provide: SwHubInitService, useValue: {} },
        SwHubEntityService,
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {}
            }
          }
        },
        {
          provide: SwHubAuthService, useValue: {
            isLogged() {
              return true;
            }
          }
        },
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(EntityPickerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

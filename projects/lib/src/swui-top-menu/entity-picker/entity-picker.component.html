<div
  *ngIf="isReseller"
  #pickerTrigger="matMenuTrigger"
  mat-ripple
  class="entity-picker"
  [matMenuTriggerFor]="entityMenu"
  (menuOpened)="onOpen()"
  (menuClosed)="onClosed()">
  <mat-icon class="entity-picker__postfix" matTooltip="Settings" [class.disabled]="isSettingsDisabled" (click)="onSettingsClick($event)">settings</mat-icon>
  <div class="entity-picker__text">{{ selected?.name }}</div>
  <mat-icon class="entity-picker__suffix">arrow_drop_down</mat-icon>

  <mat-menu #entityMenu="matMenu" xPosition="before" [class]="menuClass">
    <div class="entity-picker__menu">
      <div class="entity-picker__search">
        <mat-icon class="entity-picker__loop">search</mat-icon>
        <input
          #search
          type="text"
          autocomplete="off"
          class="entity-picker__input"
          [placeholder]="searchPlaceholder"
          [formControl]="searchInputControl"
          (click)="stopPropagation($event)">
      </div>

      <div class="entity-picker__list">
        <div
          *ngFor="let row of entities"
          [id]="row.id"
          class="entity-picker__item"
          [class.entity-picker__item--selected]="row.id === selected?.id"
          [style.padding-left]="(row.level) * 12 + 'px'"
          (click)="onItemClick($event, row)">

          <div
            class="entity-picker__name"
            [ngClass]="{
              'entity-picker__name--root': row.level === 0,
              'entity-picker__name--parent': row?.children?.length > 0 && row.level !== 0,
              'entity-picker__name--expanded': isEntityExpanded(row.id)
             }">
            <div class="entity-picker__inner">{{ row.name }}</div>
          </div>

          <div class="entity-picker__select" (click)="select(row)">Select</div>
        </div>
      </div>
    </div>
  </mat-menu>
</div>

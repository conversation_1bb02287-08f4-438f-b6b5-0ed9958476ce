import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiTopMenuComponent } from './swui-top-menu.component';
import { HubSelectorModule } from './hub-selector/hub-selector.module';
import { UserMenuModule } from './user-menu/user-menu.module';
import { LanguageSelectorModule } from './language-selector/language-selector.module';
import { EntityPickerModule } from './entity-picker/entity-picker.module';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatMenuModule } from '@angular/material/menu';


@NgModule({
  imports: [
    CommonModule,
    TranslateModule,
    MatToolbarModule,
    HubSelectorModule,
    UserMenuModule,
    LanguageSelectorModule,
    EntityPickerModule,
    MatMenuModule,
  ],
  declarations: [
    SwuiTopMenuComponent,
  ],
  exports: [
    SwuiTopMenuComponent,
  ]
})
export class SwuiTopMenuModule {
}

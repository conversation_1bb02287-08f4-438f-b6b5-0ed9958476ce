import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SwuiSelectModule } from '../../swui-select/swui-select.module';
import { SwuiSettingsDialogComponent } from './swui-settings-dialog.component';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { MatDialogModule } from '@angular/material/dialog';

import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    MatButtonModule,
    MatSelectModule,
    MatDialogModule,

    SwuiSelectModule,
  ],
  declarations: [
    SwuiSettingsDialogComponent
  ],
})
export class SwuiSettingsDialogModule {
}

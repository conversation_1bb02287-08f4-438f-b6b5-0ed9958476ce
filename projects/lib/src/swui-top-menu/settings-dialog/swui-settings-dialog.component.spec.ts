import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { SwuiSelectModule } from '../../swui-select/swui-select.module';

import { SwuiSettingsDialogComponent } from './swui-settings-dialog.component';
import { TranslateModule } from '@ngx-translate/core';
import { of } from 'rxjs';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { SettingsService } from '../../services/settings/settings.service';

class MockSettingsService {
  get appSettings$() {
    return of();
  }
}

describe('SettingsDialogComponent', () => {
  let component: SwuiSettingsDialogComponent;
  let fixture: ComponentFixture<SwuiSettingsDialogComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        NoopAnimationsModule,
        TranslateModule.forRoot(),
        ReactiveFormsModule,
        MatDialogModule,
        MatFormFieldModule,
        MatSelectModule,
        SwuiSelectModule,
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
      declarations: [
        SwuiSettingsDialogComponent
      ],
      providers: [
        { provide: SettingsService, useClass: MockSettingsService },
      ]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiSettingsDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { MatRippleModule } from '@angular/material/core';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';
import { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';
import { SwHubConfigService } from '../../services/sw-hub-config/sw-hub-config.service';
import { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';
import { LanguageSelectorModule } from '../language-selector/language-selector.module';
import { SwuiSettingsDialogModule } from '../settings-dialog/swui-settings-dialog.module';
import { UserMenuComponent } from './user-menu.component';


describe('UserMenuComponent', () => {
  let component: UserMenuComponent;
  let fixture: ComponentFixture<UserMenuComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [UserMenuComponent],
      imports: [
        TranslateModule.forRoot(),
        MatRippleModule,
        MatIconModule,
        MatMenuModule,
        MatDividerModule,
        MatDialogModule,
        LanguageSelectorModule,
        SwuiSettingsDialogModule
      ],
      providers: [
        {
          provide: MatDialog, useValue: {
            open: () => {
            }
          }
        },
        { provide: SwHubConfigService, useValue: {} },
        { provide: SWUI_HUB_MESSAGE_CONFIG, useValue: {} },
        SwHubAuthService,
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(UserMenuComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

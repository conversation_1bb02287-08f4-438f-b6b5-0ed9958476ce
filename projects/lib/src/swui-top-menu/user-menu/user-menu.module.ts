import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatRippleModule } from '@angular/material/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { LanguageSelectorModule } from '../language-selector/language-selector.module';
import { SwuiSettingsDialogModule } from '../settings-dialog/swui-settings-dialog.module';
import { UserMenuComponent } from './user-menu.component';


@NgModule({
  imports: [
    CommonModule,
    TranslateModule,
    MatRippleModule,
    MatIconModule,
    MatMenuModule,
    MatDividerModule,
    LanguageSelectorModule,
    SwuiSettingsDialogModule,
    MatTooltipModule
  ],
  declarations: [UserMenuComponent],
  exports: [UserMenuComponent],
})
export class UserMenuModule {
}

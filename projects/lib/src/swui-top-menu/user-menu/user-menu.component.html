<div mat-ripple [mat-menu-trigger-for]="profileMenu" class="user-menu">
  <mat-icon class="user-menu__prefix">account_circle</mat-icon>
  <div class="user-menu__text" *ngIf="username">{{ username }}</div>
  <mat-icon class="user-menu__suffix">arrow_drop_down</mat-icon>
  <mat-menu #profileMenu="matMenu" xPosition="before">
    <div class="user-menu__dropdown">
      <lib-swui-language-selector [inMenu]="true"
                                  (valueChanges)="languageChanges.emit($event)"></lib-swui-language-selector>
      <mat-divider></mat-divider>
      <a (click)="showSettings()" mat-menu-item>
        <mat-icon>settings</mat-icon>
        {{ 'ALL.settings' | translate }}
      </a>
      <mat-divider></mat-divider>
      <ng-container *ngIf="hasTwoFactor">
        <a (click)="twoFactorSettings()" mat-menu-item>
          <mat-icon>fingerprint</mat-icon>
          2FA Options
        </a>
        <mat-divider></mat-divider>
      </ng-container>

      <ng-container *ngIf="changePasswordAllowed()">
        <a mat-menu-item
           matTooltip="Change password"
           (click)="changePassword()">
          <mat-icon>lock</mat-icon>
          Change password
        </a>
        <mat-divider></mat-divider>
      </ng-container>

      <a (click)="logout.emit()" mat-menu-item>
        <mat-icon>power_settings_new</mat-icon>
        {{ 'HEADER.Logout' | translate }}
      </a>
    </div>
  </mat-menu>
</div>

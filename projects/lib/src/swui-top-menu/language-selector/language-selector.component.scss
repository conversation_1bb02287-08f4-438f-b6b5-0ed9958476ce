.sw-lang{
  &__selected {
    display: flex;
    align-items: center;
    height: 48px;
    padding: 0 16px;
    cursor: pointer;
  }
  &__text {
    margin-left: 24px;
  }
  &__prefix {
    width: 20px;
    padding-left: 2px;
  }
  &__suffix {
    margin-left: auto;
  }
  &__icon {
    display: block;
    width: 16px;
    height: 10px;
    margin-right: 10px;
  }
  &__item {
    display: flex;
    align-items: center;
    height: 42px;
    padding: 0 16px 0 32px;
    font-size: 13px;
    border-bottom: 1px solid #ddd;
    cursor: pointer;
    &:last-child {
      border-bottom: none;
    }
  }
  &__dropdown {
    display: none;
    background-color: #eaedf1;
    &.open {
      display: block;
    }
  }
}

.sw-lang-menu {
  height: 100%;

  &__selected {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 10px 0 16px;
    font-size: 14px;
    font-weight: 400;
    color: #fff;
    cursor: pointer;
  }
  &__icon {
    margin-right: 7px;
  }
  &__prefix {
    margin-right: 5px;
    width: 16px;
  }
}

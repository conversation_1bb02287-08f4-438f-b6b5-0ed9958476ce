<ng-container *ngIf="inMenu; else tplSolo">
  <div class="sw-lang" (click)="prevent($event)" *ngIf="active">
    <div class="sw-lang__selected" mat-ripple (click)="triggerDropdown($event)">
      <img *ngIf="active.image" class="sw-lang__prefix" [src]="active.image" alt>
      <div *ngIf="active.title" class="sw-lang__text">{{ active.title | translate }}</div>
      <mat-icon class="sw-lang__suffix">arrow_drop_down</mat-icon>
    </div>
    <div class="sw-lang__dropdown" [ngClass]="{'open': isOpen}">
      <div
        mat-ripple
        class="sw-lang__item"
        *ngFor="let row of items"
        (click)="select(row)"
        [ngClass]="{'active': active === row}">
        <img *ngIf="row.image" class="sw-lang__icon" [src]="row.image" alt>
        <span *ngIf="active.title">{{ row.title | translate }}</span>
      </div>
    </div>
  </div>
</ng-container>

<ng-template #tplSolo>
  <div class="sw-lang-menu" *ngIf="active">
    <div class="sw-lang-menu__selected" mat-ripple [mat-menu-trigger-for]="languageMenu">
      <img *ngIf="active.image" class="sw-lang-menu__prefix" [src]="active.image" alt>
      <div *ngIf="active.title" class="sw-lang-menu__text">{{ active.title | translate }}</div>
      <mat-icon class="sw-lang-menu__suffix">arrow_drop_down</mat-icon>
    </div>

    <mat-menu #languageMenu="matMenu" xPosition="before">
      <button mat-menu-item *ngFor="let row of items" (click)="select(row)" class="sw-lang-menu__item">
        <img *ngIf="row.image" class="sw-lang-menu__icon" [src]="row.image" alt width="16" height="11">
        <span *ngIf="active.title">{{ row.title | translate }}</span>
      </button>
    </mat-menu>
  </div>
</ng-template>

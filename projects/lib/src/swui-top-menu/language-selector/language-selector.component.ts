import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Inject,
  Input,
  Output
} from '@angular/core';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';
import { SwHubMessageModuleConfig } from '../../services/sw-hub-init/sw-hub-init.model';

export interface LangItem {
  id: string;
  dialect?: string | string[];
  title?: string;
  image?: string;
}

@Component({
    selector: 'lib-swui-language-selector',
    templateUrl: './language-selector.component.html',
    styleUrls: ['./language-selector.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class LanguageSelectorComponent {
  @Input() inMenu = false;
  @Output() valueChanges = new EventEmitter<string>();

  readonly items: LangItem[];

  active?: LangItem;
  isOpen = false;

  constructor( @Inject(SWUI_HUB_MESSAGE_CONFIG) config: SwHubMessageModuleConfig,
               translate: TranslateService,
               private readonly elementRef: ElementRef,
               private readonly cdr: ChangeDetectorRef ) {
    this.items = config.langs || [];
    this.active = this.items.find(( { id } ) => id === translate.currentLang);
    translate.onLangChange.subscribe(( { lang }: LangChangeEvent ) => {
      this.active = this.items.find(( { id } ) => id === lang);
      this.cdr.detectChanges();
    });
  }

  @HostListener('document:click', ['$event.target'])
  onClick( target: HTMLElement ) {
    const inside = this.elementRef.nativeElement.contains(target);
    if (!inside && this.isOpen) {
      this.isOpen = false;
    }
  }

  select( item: LangItem | undefined ): void {
    if (item !== undefined) {
      this.active = item;
      this.valueChanges.emit(item.id);
    }
  }

  prevent( event: Event ) {
    event.preventDefault();
    event.stopPropagation();
  }

  triggerDropdown( event: Event ) {
    event.preventDefault();
    this.isOpen = !this.isOpen;
  }
}

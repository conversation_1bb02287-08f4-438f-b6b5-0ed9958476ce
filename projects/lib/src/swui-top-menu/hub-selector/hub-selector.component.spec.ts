import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { HubSelectorComponent } from './hub-selector.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatRippleModule } from '@angular/material/core';
import { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';
import { SwHubConfigService } from '../../services/sw-hub-config/sw-hub-config.service';
import { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';


describe('HubSelectorComponent', () => {
  let component: HubSelectorComponent;
  let fixture: ComponentFixture<HubSelectorComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      schemas: [NO_ERRORS_SCHEMA],
      declarations: [HubSelectorComponent],
      imports: [
        TranslateModule.forRoot(),
        MatRippleModule,
        MatMenuModule,
        MatIconModule,
      ],
      providers: [
        { provide: SWUI_HUB_MESSAGE_CONFIG, useValue: {} },
        { provide: SwHubConfigService, useValue: {} },
        {
          provide: SwHubAuthService, useValue: {
            isLogged() {
              return true;
            }
          }
        },
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(HubSelectorComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

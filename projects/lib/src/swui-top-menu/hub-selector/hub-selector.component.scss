.hubs-mobile {
  height: 100%;

  &__toggle {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 16px;
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
  }

  &__link {
    display: block;
    text-align: left;
  }
}

.hubs-desktop {
  display: flex;
  height: 100%;

  &__item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 150px;
    list-style: none;
    font-size: 14px;
    font-weight: 400;
    color: #A9B0C0;
    text-decoration: none;
    text-align: center;
    border-left: 1px solid #484B69;
    cursor: pointer;

    &:first-child {
      border-color: transparent;
    }

    &:hover {
      color: #fff;
    }

    &.active {
      color: #fff;
      background-color: #1E2033;
      border-color: transparent;

      &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        display: block;
        height: 6px;
        width: 100%;
        background-color: #A9B0C0;
      }

      &.hub-casino {
        &:after {
          background-color: #4F82D7;
        }
      }

      &.hub-analytics {
        &:after {
          background-color: #9C2B1A;
        }
      }

      &.hub-engagement {
        &:after {
          background-color: #A6CF38;
        }
      }

      &.hub-studio {
        &:after {
          background-color: #A6CF38;
        }
      }

      & + .hubs-desktop__item {
        border-color: transparent;
      }
    }
  }
}

.hubs {
  display: flex;
  align-items: center;
  height: 100%;

  &__mobile {
    display: none;
  }

  @media(max-width: 767px) {
    &__mobile {
      display: block;
    }
    &__desktop {
      display: none;
    }
  }
}

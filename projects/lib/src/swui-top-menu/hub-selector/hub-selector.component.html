<div class="hubs">

  <div class="hubs__desktop hubs-desktop">
    <a
      class="hubs-desktop__item"
      *ngFor="let item of hubs"
      mat-ripple
      [class.active]="activeHub?.id === item.id"
      [ngClass]="item.cssClass"
      [href]="item.url">
      {{ item.name | translate }}
    </a>
  </div>

  <div class="hubs__mobile hubs-mobile">
    <div [matMenuTriggerFor]="menu" class="hubs-mobile__toggle" mat-ripple>
      {{activeHub?.name | translate}}
      <mat-icon class="hubs-mobile__chevron">arrow_drop_down</mat-icon>
    </div>
    <mat-menu #menu="matMenu" xPosition="before">
      <a
        *ngFor="let item of hubs"
        class="hubs-mobile__link"
        mat-menu-item
        [ngClass]="item.cssClass"
        [href]="item.url">
        <span>{{ item.name | translate }}</span>
      </a>
    </mat-menu>
  </div>

</div>

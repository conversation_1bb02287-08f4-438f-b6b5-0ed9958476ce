import { moduleMetadata, storiesOf } from '@storybook/angular';
import { I18nModule } from '../../i18n.module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { SwuiTopMenuModule } from '../swui-top-menu.module';
import { SWUI_HUB_MESSAGE_CONFIG } from '../../services/sw-hub-init/sw-hub-init.token';
import { action } from 'storybook/actions';
import { SwHubConfigService } from '../../services/sw-hub-config/sw-hub-config.service';
import { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';
import { SwHubInitService } from '../../services/sw-hub-init/sw-hub-init.service';
import { AppSettings } from '../../services/settings/app-settings';
import { SettingsService } from '../../services/settings/settings.service';
import { Injectable } from '@angular/core';
import { SwHubEntityService } from '../../services/sw-hub-entity/sw-hub-entity.service';
import { SwHubEntityDataSource } from '../../services/sw-hub-entity/sw-hub-entity-data-source';
import { of } from 'rxjs';
import { APP_BASE_HREF } from '@angular/common';
import { RouteNoopModule } from '../../swui-schema-grid/_stories/route-noop.module';

@Injectable()
class InitService {

  constructor( private readonly settings: SettingsService, private readonly entityService: SwHubEntityService ) {
  }

  sendLocale( lang: string ) {
    console.log('sendLocale', lang);
  }

  sendAppSettings( settings: AppSettings ) {
    console.log('sendAppSettings', settings);
    this.settings.use(settings);
  }

  sendEntityId( entityId: string ) {
    this.entityService.use(entityId);
  }
}

const EN = require('./locale.json');

const LOCALE_LIST = [
  {
    id: 'en',
    dialect: 'en',
    title: 'LANGUAGE.English',
  },
  {
    id: 'zh',
    dialect: 'zh-cn',
    title: 'LANGUAGE.Chinese',
  },
];

const HUBS = {
  casino: {
    name: 'HUBS.casino',
    cssClass: 'hub-casino',
    url: 'http://hub-casino'
  },
  analytics: {
    name: 'HUBS.analytics',
    cssClass: 'hub-analytics',
    url: 'http://hub-analytics'
  },
  engagement: {
    name: 'HUBS.engagement',
    cssClass: 'hub-engagement',
    url: 'http://hub-engagement'
  },
  studio: {
    name: 'HUBS.studio',
    cssClass: 'hub-studio',
    url: 'http://hub-studio'
  }
};

const BRIEF = require('./brief.json');
const ENTITY = require('./short-structure.json');

const template = '<lib-swui-top-menu (sidebarChanges)="change($event)" [showSearch]="true"></lib-swui-top-menu>';

storiesOf('Top menu', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        I18nModule.forRoot({ translations: { en: EN } }),
        SwuiTopMenuModule,
        RouteNoopModule,
      ],
      providers: [
        SettingsService,
        SwHubEntityService,
        { provide: APP_BASE_HREF, useValue: '/iframe.html' },
        { provide: SWUI_HUB_MESSAGE_CONFIG, useValue: { name: 'casino', langs: LOCALE_LIST } },
        { provide: SwHubConfigService, useValue: { hubs: HUBS } },
        {
          provide: SwHubAuthService, useValue: {
            isLogged() {
              return true;
            },
            username: 'username',
            allowedTo() {
              return true;
            },
            isTwoFactor: true
          }
        },
        { provide: SwHubInitService, useClass: InitService },
        {
          provide: SwHubEntityDataSource, useValue: {
            getBrief() {
              return of(BRIEF);
            },

            getEntity() {
              return of(ENTITY);
            }
          }
        },
      ]
    })
  )
  .add('usage', () => ({
    template,
    props: {
      change: action('change')
    }
  }));

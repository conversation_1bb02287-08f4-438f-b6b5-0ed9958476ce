{"id": "W4RkGRen", "type": "entity", "name": "MASTER", "status": "normal", "key": "aaa11200-19f1-48c1-a78c-3a3d56095f38", "isTest": false, "dynamicDomainId": "NlqPw53Z", "environment": "gs1", "staticDomainId": "v5R2nRkE", "merchantTypes": ["MEOW", "stars_mock", "relax_mock", "pariplay", "mrch", "mrch_json", "stars", "test", "testType", "seamless", "everymatrix", "everymatrix_mock", "pariplay_mock", "ipm", "<PERSON><PERSON><PERSON>", "gvc", "mrg<PERSON>_mock", "relax", "gvc_http", "pop_moorgate", "pop", "pop_asia", "pop_eu", "betvictor_asia", "sbtech", "seamless_ipm", "seamless_mrch", "gan", "betvictor", "sisal", "dev_stars", "betvictor_mock"], "settings": {"smtp": {"host": "smtp.gmail.com", "port": 465, "auth": {"user": "swsmanage", "pass": "Csf-UNz-Gy2-RGX"}}, "urlParams": {"history": 1, "history_url": "http://gc.gaming.skywindgroup.com/gamehistory/4.38.0/index.html", "phantom_version_host": "http://client-api.test.phantomental.com/client_link", "phantom_link": "%2Ftest%2Fui%2Fmain.js"}, "templates": {"passwordRecovery": {"from": "Skywind <<EMAIL>>", "subject": "Skywind Password Reset", "html": "\n{{username}},\n<p>\nTo reset your password, please use the following link: <a href=\"http://localhost:6080/?token={{token}}\">click to reset\n</a>.\nIt will expire in 15 minutes — after that you'll need to request a new one.\n</p>\n<p>\nIf you didn't request this change, please let us know by replying to this email.\n</p>\n", "options": {"resetBaseUrl": "http://localhost:6080"}}, "changeEmail": {"from": "Skywind <<EMAIL>>", "subject": "Skywind Change Email Confirmation", "html": "\n{{username}},\n<p>\nTo confirm your email, please use the following link: <a href=\"http://localhost:6080/?token={{token}}\">click to confirm\n</a>.\nIt will expire in 15 minutes — after that you'll need to request a new one.\n</p>\n<p>\nIf you didn't request this change, please let us know by replying to this email.\n</p>\n"}}, "ipWhitelist": [], "restrictions": {}, "boIpWhitelist": ["10.4.*.*", "127.0.0.1"], "passwordPattern": "(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)^.{8,}$", "useSiteAuthorization": false, "isAccountBlockingEnabled": false, "isPasswordChangeEnabled": false, "twoFactorAuthSettings": {"isAuthEnabled": false, "authOptions": ["sms", "email", "google"], "mailTemplate": {"from": "Skywind", "subject": "Login code", "html": "Hello{{username}}, here is your auth code: <b>{{authCode}}</b>"}, "smsTemplates": {"defaultEnTemplateForBO": "{{username}}, here is your sms <b>{{authCode}}</b> auth code"}}, "maintenanceUrl": "https://flc.cdnsky.net/maintenance/", "responsibleGaming": {"enabled": false}, "virtualCurrencyRate": {"BNS": {"EUR": 0.001, "USD": 0.001, "GBP": 0.001, "AUD": 0.001, "AZN": 0.001, "BGN": 0.001, "BND": 0.001, "CAD": 0.001, "CHF": 0.001, "GEL": 0.001, "NZD": 0.001, "SGD": 0.001, "BMD": 0.001, "BRL": 0.005, "ILS": 0.005, "MYR": 0.005, "PEN": 0.005, "PLN": 0.005, "RON": 0.005, "TRY": 0.005, "GHS": 0.005, "ARS": 0.01, "CNY": 0.01, "DKK": 0.01, "HKD": 0.01, "HRK": 0.01, "MAD": 0.01, "MOP": 0.01, "NOK": 0.01, "SEK": 0.01, "VEF": 0.01, "ZAR": 0.01, "ZMW": 0.01, "CZK": 0.05, "DOP": 0.05, "HNL": 0.05, "INR": 0.05, "KGS": 0.05, "MDL": 0.05, "NIO": 0.05, "PHP": 0.05, "RUB": 0.05, "THB": 0.05, "TWD": 0.05, "UAH": 0.05, "UYU": 0.05, "VES": 0.05, "ISK": 0.1, "JPY": 0.1, "RSD": 0.1, "KES": 0.1, "CLP": 0.5, "HUF": 0.5, "KZT": 0.5, "XOF": 0.5, "CRC": 0.5, "KRW": 1, "COP": 1, "MNT": 1, "TZS": 1, "MMK": 1, "PYG": 5, "IDR": 10, "VND": 25, "IDS": 0.01, "VNS": 0.025, "RUP": 0.01, "VDO": 0.025, "MXN": 0.03}}, "validationSettings": {"playerCodeLength": {"min": 3, "max": 50}}, "storePlayerInfo": false, "turbo": false, "enablePhantomFeatures": true, "clientFeatures": {}, "isPasswordForceChangeEnabled": false, "passwordForceChangePeriod": 90, "newLimitsEnabled": false}}
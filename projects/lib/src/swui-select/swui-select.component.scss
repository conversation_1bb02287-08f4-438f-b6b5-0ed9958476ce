.swui-select {
  position: relative;
  padding-right: 1.5em;

  &:after {
    content: '';
    display: block;
    position: absolute;
    top: 2px;
    right: 0;
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid;
    margin: 0 4px;
    color: #757575;
  }

  &__value {
    padding-right: 18px;
    text-overflow: ellipsis;
  }

  &__search {
    position: relative;
    display: block;
    width: 100%;
    margin-bottom: 0 !important;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      display: block;
      width: 100%;
      height: 1px;
      background-color: rgba(0, 0, 0, .12);
    }
  }

  &__input {
    display: block;
    width: 100%;
    height: 46px;
    padding: 0 .75em;
    margin: 0;
    border: unset;
    font-size: 1em;
    color: rgba(0, 0, 0, 0.87);

    &:focus {
      outline: none !important;
    }
  }

  &__dropdown {
    &.search-enabled {
      max-height: 210px;
      overflow: auto;

      &.large {
        max-height: 240px;
      }
    }
  }

  &__other {
    position: relative;
    bottom: 1px;
    opacity: 0.75;
    font-size: 0.75em;
  }

  &__toggle {
    display: block;
    height: 3em;
    padding: 0 16px;
    font-size: inherit;
    line-height: 3em;
  }

  &__viewport {
    height: 100%;
    max-height: 210px;
  }
}

.selected {
  color: #1468cf;
}

.selected-single {
  color: #1468cf;
  background: #e0e0e0;
}

.disabled-option {
  pointer-events: none;
  opacity: .5;
}

.inline {
  display: inline;
}

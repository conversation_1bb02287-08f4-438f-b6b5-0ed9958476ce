import { ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatPseudoCheckboxModule, MatRippleModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';

import { SwuiSelectComponent } from './swui-select.component';


export const SELECT_MODULES = [
  FormsModule,
  ReactiveFormsModule,
  MatSelectModule,
  MatInputModule,
  MatIconModule,
  MatCheckboxModule,
  MatRippleModule,
  ScrollingModule,
  MatMenuModule,
];

@NgModule({
  declarations: [SwuiSelectComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ...SELECT_MODULES,
    MatMenuModule,
    MatPseudoCheckboxModule
  ],
  exports: [SwuiSelectComponent],
})
export class SwuiSelectModule {
}

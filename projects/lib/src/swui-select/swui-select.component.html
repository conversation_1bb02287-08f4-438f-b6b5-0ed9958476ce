<div class="swui-select">
  <input matInput
         readonly
         class="swui-select__value"
         type="text"
         autocomplete="off"
         #trigger="matMenuTrigger"
         [mat-menu-trigger-for]="matMenu"
         [formControl]="triggerInputControl"
         [placeholder]="placeholder"
         [ngClass]="{'swui-select__other': multiple && selectControl.value?.length > 1}"
         (menuOpened)="onOpened()"
         (menuClosed)="onClosed()"/>

  <mat-menu #matMenu="matMenu" class="swui-select__menu">
    <label *ngIf="showSearch"
           class="swui-select__search">
      <input #search
             class="swui-select__input"
             type="text"
             placeholder="{{searchPlaceholder | translate}}"
             [formControl]="searchControl"
             (blur)="search.focus()"
             (click)="$event.stopPropagation()">
    </label>

    <ng-template matMenuContent>
      <ng-container *ngIf="multiple; else single">
        <div *ngIf="!disableAllOption"
             mat-menu-item
             (click)="toggleAll($event)">
          <mat-checkbox [checked]="allChecked" (click)="toggleAll($event)" class="inline">
            <div class="inline">{{ 'ALL.all' | translate }}</div>
          </mat-checkbox>
        </div>
        <cdk-virtual-scroll-viewport
          [itemSize]="itemHeight"
          minBufferPx="480"
          maxBufferPx="960"
          [style.height.px]="viewportHeight">
          <div *cdkVirtualFor="let option of options"
               mat-menu-item
               [ngClass]="{'selected': option.state?.checked, 'disabled-option': option.disabled}"
               (click)="onSelectMultiple($event, option)">
            <mat-checkbox [checked]="option.state?.checked"
                          (click)="onSelectMultiple($event, option)"
                          class="inline">
              <div class="inline">{{ option.text | translate: option?.data }}</div>
            </mat-checkbox>
          </div>
        </cdk-virtual-scroll-viewport>
      </ng-container>

      <ng-template #single>
        <cdk-virtual-scroll-viewport
          [itemSize]="itemHeight"
          minBufferPx="480"
          maxBufferPx="960"
          [style.height.px]="viewportHeight">
          <div *ngIf="!disableEmptyOption"
               mat-menu-item
               (click)="onSelect(null)">
            {{ emptyOptionPlaceholder | translate }}
          </div>

          <div mat-menu-item
               *cdkVirtualFor="let option of options"
               [ngClass]="{
               'selected-single': option.state?.checked,
               'disabled-option': option.disabled
               }"
               (click)="onSelect(option)">
            {{ option.text | translate: option?.data }}
          </div>
        </cdk-virtual-scroll-viewport>
      </ng-template>
    </ng-template>
  </mat-menu>
</div>

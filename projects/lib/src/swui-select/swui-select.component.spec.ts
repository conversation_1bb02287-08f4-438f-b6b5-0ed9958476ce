import { CommonModule } from '@angular/common';
import { Component, DebugElement, ViewChild } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UntypedFormControl } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';

import { SwuiSelectComponent } from './swui-select.component';
import { SwuiSelectOption } from './swui-select.interface';
import { SELECT_MODULES } from './swui-select.module';

function createFakeEvent( type: string ) {
  const event = document.createEvent('Event');
  event.initEvent(type, true, true);
  return event;
}

function dispatchFakeEvent( node: Node | Window, type: string ) {
  node.dispatchEvent(createFakeEvent(type));
}

@Component({
    template: `
    <mat-form-field>
      <lib-swui-select multiple [data]="data" [formControl]="control"></lib-swui-select>
    </mat-form-field>
  `,
    standalone: false
})
class MultiSelectComponent {
  data: any[] = [
    { id: '1', text: 'Solo Option1' },
    { id: '2', text: 'Test Option2' },
    { id: '3', text: 'Option3', disabled: true },
    { id: '4', text: 'Test Option4' },
    { id: '5', text: 'Option5' }
  ];
  control = new UntypedFormControl();
  @ViewChild(SwuiSelectComponent, { static: true }) select: SwuiSelectComponent | undefined;
}

describe('SwuiSelectComponent', () => {
  let component: SwuiSelectComponent;
  let fixture: ComponentFixture<SwuiSelectComponent>;
  let testOptions: SwuiSelectOption[] = [];
  let testValue: string;
  let selectControl: UntypedFormControl;
  let triggerInputControl: UntypedFormControl;
  let host: DebugElement;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        CommonModule,
        NoopAnimationsModule,
        TranslateModule.forRoot(),
        ...SELECT_MODULES
      ],
      declarations: [SwuiSelectComponent, MultiSelectComponent]
    });
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiSelectComponent);
    component = fixture.componentInstance;
    host = fixture.debugElement;
    testOptions = [
      { id: '1', text: 'Solo Option1' },
      { id: '2', text: 'Test Option2' },
      { id: '3', text: 'Option3', disabled: true },
      { id: '4', text: 'Test Option4' },
      { id: '5', text: 'Option5' },
    ];
    testValue = '2';
    component.data = testOptions;
    selectControl = component.selectControl;
    triggerInputControl = component.triggerInputControl;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set value', () => {
    component.value = testValue;
    expect(component.value).toBe(testValue);
    expect(selectControl.value).toEqual(testOptions.filter(opt => opt.id === testValue));
  });

  it('should set required', () => {
    component.required = true;
    expect(component.required).toBeTruthy();
  });

  it('should set disabled', () => {
    component.disabled = true;
    expect(component.disabled).toBeTruthy();
    expect(triggerInputControl.disabled).toBeTruthy();

    component.disabled = false;
    expect(component.disabled).toBeFalsy();
    expect(triggerInputControl.disabled).toBeFalsy();
  });

  it('should set empty', () => {
    expect(component.empty).toBeTruthy();

    component.value = testValue;
    expect(component.empty).toBeFalsy();
  });

  it('should set placeholder', () => {
    component.placeholder = 'test';
    expect(component.placeholder).toBe('test');
  });

  it('should get error state', () => {
    expect(component.errorState).toBeFalsy();
  });

  it('should set state changes', () => {
    const nextSpy = spyOn(component.stateChanges, 'next');
    component.required = true;
    expect(nextSpy).toHaveBeenCalled();
  });

  it('should controlType to be defined', () => {
    expect(component.controlType).toBe('lib-swui-select');
  });

  it('should set host id', () => {
    expect(host.nativeElement.getAttribute('id')).toBeDefined();
  });

  it('should shouldLabelFloat', () => {
    expect(component.shouldLabelFloat).toBeFalsy();
    component.value = testValue;
    expect(component.shouldLabelFloat).toBeTruthy();
  });

  it('should filter', () => {
    component.showSearch = true;
    component.ngOnInit();

    component.searchControl.setValue(testValue);
    expect(component.options).toEqual([testOptions[1]]);

    component.searchControl.setValue('99');
    expect(component.options).toEqual([]);
  });

  it('should call onChange onInit when selected value changed', () => {
    spyOn(component, 'onChange');
    component.ngOnInit();
    selectControl.setValue(testOptions);
    expect(component.onChange).toHaveBeenCalled();
  });

  it('should set onChange in registerOnChange', () => {
    component.ngOnInit();
    let test = false;
    const fn = () => {
      test = true;
    };
    component.registerOnChange(fn);
    selectControl.setValue(testOptions);
    expect(test).toBe(true);
  });

  it('should setDisabledState', () => {
    component.setDisabledState(true);
    expect(component.disabled).toBeTruthy();
    expect(triggerInputControl.disabled).toBeTruthy();

    component.setDisabledState(false);
    expect(component.disabled).toBeFalsy();
    expect(triggerInputControl.disabled).toBeFalsy();
  });

  it('should write value', () => {
    component.writeValue(testValue);
    expect(component.value).toEqual(testValue);
  });

  it('should setDescribedByIds', () => {
    const testIds = ['test1', 'test2'];
    component.setDescribedByIds(testIds);
    expect(component.describedBy).toBe(testIds.join(' '));
  });

  it('should complete state change on destroy', () => {
    const completeSpy = spyOn(component.stateChanges, 'complete');
    component.ngOnDestroy();
    expect(completeSpy).toHaveBeenCalled();
  });

  it('should call onTouched on focus', () => {
    spyOn(component, 'onTouched');
    dispatchFakeEvent(host.nativeElement, 'focus');
    dispatchFakeEvent(host.nativeElement, 'blur');
    expect(component.onTouched).toHaveBeenCalled();
  });

  it('should not set valueAccessor if form control', () => {
    (fixture.componentInstance as any).ngControl = new UntypedFormControl(testValue);
    expect(component.ngControl.valueAccessor).toBeUndefined();
  });

  it('should set disableEmptyOption', () => {
    expect(component.disableEmptyOption).toBe(false);
  });

  it('should set emptyOptionPlaceholder', () => {
    expect(component.emptyOptionPlaceholder).toBe('None');
  });

  it('should set showSearch', () => {
    expect(component.showSearch).toBe(false);
  });

  it('should set searchPlaceholder', () => {
    expect(component.searchPlaceholder).toBe('Search');
  });

  it('should set multiple', () => {
    fixture.destroy();
    const multiFixture = TestBed.createComponent(MultiSelectComponent);
    const instance = multiFixture.componentInstance;
    multiFixture.detectChanges();
    expect(instance.select).toBeDefined();
    if (instance.select) {
      expect(instance.select.multiple).toBeTruthy();
    }
  });

  it('should set multiple value', () => {
    fixture.destroy();
    const multiFixture = TestBed.createComponent(MultiSelectComponent);
    const instance = multiFixture.componentInstance;
    multiFixture.detectChanges();
    expect(instance.select).toBeDefined();
    if (instance.select) {
      instance.select.writeValue(['1', '2']);
      expect(instance.select.value).toEqual(['1', '2']);
    }
  });

  it('should toggleAll', () => {
    fixture.destroy();
    const multiFixture = TestBed.createComponent(MultiSelectComponent);
    const instance = multiFixture.componentInstance;
    multiFixture.detectChanges();
    const select = instance.select;
    expect(select).toBeDefined();
    if (select) {
      select.toggleAll();
      expect(select.value).toEqual(['1', '2', '4', '5']);
      select.toggleAll();
      expect(select.value).toEqual([]);
    }
  });

  it('should set disableAllOption', () => {
    component.disableAllOption = true;
    expect(component.disableAllOption).toBeTruthy();
  });

});

import { Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, FormGroupDirective, NgControl } from '@angular/forms';
import { FocusMonitor } from '@angular/cdk/a11y';
import moment from 'moment';
import { SwuiDateTimepickerConfig } from './swui-datetimepicker.interface';
import { SwuiTimepickerInterface } from '../swui-timepicker/swui-timepicker.interface';
import { MatMenuTrigger } from '@angular/material/menu';
import { MatFormFieldControl } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';
import { ErrorStateMatcher } from '@angular/material/core';


type DateTimePickerValue = moment.Moment | string | undefined;

interface DateTimePickerForm {
  date: moment.Moment | null;
  time: SwuiTimepickerInterface | null;
}

function transformValue( value: DateTimePickerValue | null ): DateTimePickerForm {
  if (value !== null) {
    const date = moment.isMoment(value) ? value : moment.parseZone(value);
    if (date.isValid()) {
      return {
        date: date,
        time: {
          hour: date.hours(),
          minute: date.minutes(),
          second: date.seconds(),
        }
      };
    }
  }
  return {
    date: null,
    time: null
  };
}

const CONTROL_NAME = 'lib-swui-datetimepicker';
let nextUniqueId = 0;

@Component({
    selector: 'lib-swui-datetimepicker',
    templateUrl: './swui-datetimepicker.component.html',
    styleUrls: ['./swui-datetimepicker.component.scss'],
    providers: [{ provide: MatFormFieldControl, useExisting: SwuiDatetimepickerComponent }],
    standalone: false
})
export class SwuiDatetimepickerComponent extends SwuiMatFormFieldControl<DateTimePickerValue> {
  @ViewChild(MatMenuTrigger) dropdownTrigger?: MatMenuTrigger;

  @Input() minDate?: moment.Moment | string;
  @Input() maxDate?: moment.Moment | string;

  @Input()
  get value(): DateTimePickerValue {
    return this._value;
  }

  set value( value: DateTimePickerValue ) {
    this._value = value;
    this.writeValue(value);
    this.stateChanges.next(undefined);
  }

  @Input()
  set config( value: SwuiDateTimepickerConfig ) {
    this._config = value;
    if (this.form.contains('time')) {
      const timeGroup = this.form.get('time') as UntypedFormGroup;
      if (this._config.disableTimepicker) {
        timeGroup.disable();
      } else {
        timeGroup.enable();
      }
    }
  }

  get config(): SwuiDateTimepickerConfig {
    return this._config;
  }

  get empty() {
    return this.formattedDate === '';
  }

  readonly form: UntypedFormGroup;
  dateSource: moment.Moment | null = null;

  readonly controlType = CONTROL_NAME;

  @ViewChild('input') input?: MatInput;
  @ViewChild('date', { read: MatMenuTrigger }) dateRef?: MatMenuTrigger;

  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;

  @HostBinding('class.floating')
  get shouldLabelFloat() {
    return this.focused || !this.empty;
  }

  private _value?: DateTimePickerValue;
  private _config: SwuiDateTimepickerConfig;

  constructor( fm: FocusMonitor,
               elRef: ElementRef<HTMLElement>,
               @Optional() @Self() ngControl: NgControl,
               @Optional() parentFormGroup: FormGroupDirective,
               errorStateMatcher: ErrorStateMatcher ) {
    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);
    this._config = {};
    this.form = new UntypedFormGroup({
      date: new UntypedFormControl(),
      time: new UntypedFormControl()
    });
  }

  writeValue( value: DateTimePickerValue | null | undefined ) {
    if (typeof value === 'undefined') {
      return;
    }
    const data = transformValue(value);
    this.form.setValue(data, { emitEvent: false });
    this.dateSource = this.transformFrom();
  }

  onContainerClick( event: Event ) {
    if (this.dateRef && (event.target as Element).tagName.toLowerCase() !== 'input' && !this.disabled) {
      this.elRef.nativeElement.focus();
      this.dateRef.openMenu();
      this.focused = true;
    }
  }

  onClose() {
    this.focused = false;
    this.onTouched();
  }

  onClick() {
    if (!this.disabled) {
      this.focused = true;
    }
  }

  preventClose( event: MouseEvent ) {
    event.preventDefault();
    event.stopPropagation();
  }

  onSubmit( event: MouseEvent ) {
    event.preventDefault();
    if (this.dropdownTrigger) {
      this.dropdownTrigger.closeMenu();
    }
    this.dateSource = this.transformFrom() || this.today;
    this.onChange(this.dateSource);
  }

  onCancel( event: MouseEvent ) {
    event.preventDefault();
    if (this.dropdownTrigger) {
      this.dropdownTrigger.closeMenu();
    }
  }

  get today(): moment.Moment {
    const now = this.config && this.config.timeZone ? moment.tz(this.config.timeZone) : moment.utc();
    return now.startOf('day');
  }

  get formattedDate(): string {
    if (!this.dateSource) {
      return '';
    }

    const date = this.config.timeZone
      ? this.dateSource.tz(this.config.timeZone)
      : this.dateSource;
    return date ? date.format(this.format) : '';
  }

  protected onDisabledState( disabled: boolean ) {
    if (disabled) {
      this.form.disable();
    } else {
      this.form.enable();
    }
  }

  protected isErrorState(): boolean {
    if (this.input) {
      return this.input.errorState;
    }
    return false;
  }

  private get format(): string {
    const timeFormat = this.config && this.config.timeFormat ? this.config.timeFormat : 'HH:mm:ss';
    const dateFormat = this.config && this.config.dateFormat ? this.config.dateFormat : 'DD.MM.YYYY';
    return this.config.disableTimepicker ? dateFormat : dateFormat + ' ' + timeFormat;
  }

  private transformFrom(): moment.Moment | null {
    const val: DateTimePickerForm | undefined = this.form.getRawValue();
    if (typeof val === 'undefined') {
      return null;
    }
    const { date, time } = val;
    if (moment.isMoment(date) && time) {
      date.set(time);
    } else if (time && this.dateSource) {
      return this.dateSource.clone().set(time);
    }
    return date;
  }
}


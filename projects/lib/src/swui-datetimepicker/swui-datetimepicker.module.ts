import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

import { SwuiDatetimepickerComponent } from './swui-datetimepicker.component';
import { SwuiCalendarModule } from '../swui-calendar/swui-calendar.module';
import { SwuiTimepickerModule } from '../swui-timepicker/swui-timepicker.module';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';

export const DATETIMEPICKER_MODULES = [
  MatDividerModule,
  MatButtonModule,
  MatInputModule,
  MatMenuModule,
  ReactiveFormsModule,
  SwuiCalendarModule,
  SwuiTimepickerModule,
];

/**
 * @deprecated use lib-swui-date-picker
 */

@NgModule({
  declarations: [
    SwuiDatetimepickerComponent
  ],
  exports: [
    SwuiDatetimepickerComponent
  ],
  imports: [
    CommonModule,
    ...DATETIMEPICKER_MODULES,
  ]
})
export class SwuiDatetimepickerModule {
}

<input
  #input
  matInput
  type="text"
  [value]="formattedDate"
  [placeholder]="placeholder"
  readonly="readonly"
  #date="matMenuTrigger"
  (click)="onClick()"
  [disabled]="disabled"
  [ngClass]="{ 'swui-datetimepicker-disabled': disabled }"
  [matMenuTriggerFor]="swuiDateTime">

<mat-menu #swuiDateTime="matMenu" xPosition="before" (closed)="onClose()">
  <form [formGroup]="form" class="swui-datetimepicker"
        [class.timepicker-disabled]="config?.disableTimepicker" (click)="preventClose($event)">
    <div class="swui-datetimepicker__calendar">
      <lib-swui-calendar formControlName="date" [minDate]="minDate" [maxDate]="maxDate" [timeZone]="config?.timeZone">
      </lib-swui-calendar>
    </div>
    <div class="swui-datetimepicker__time" *ngIf="!config?.disableTimepicker">
      <lib-swui-timepicker formControlName="time" [timeDisableLevel]="config?.timeDisableLevel"></lib-swui-timepicker>
    </div>
    <div class="swui-datetimepicker__footer">
      <button mat-button class="swui-datetimepicker__btn" (click)="onCancel($event)">Cancel</button>
      <button mat-flat-button color="primary" class="swui-datetimepicker__btn" (click)="onSubmit($event)">Apply</button>
    </div>
  </form>
</mat-menu>

import { SwuiTimepickerTimeDisableLevel } from '../swui-timepicker/swui-timepicker.interface';
import { CustomPeriod } from '../swui-date-range/swui-date-range.model';
import { unitOfTime } from 'moment';

export interface SwuiDateTimepickerConfig {
  dateFormat?: string;
  disableTimepicker?: boolean;
  timeDisableLevel?: SwuiTimepickerTimeDisableLevel;
  timeFormat?: string;
  timeZone?: string;
  customPeriods?: CustomPeriod[];
  maxPeriod?: unitOfTime.Base;
  minDate?: string;
  maxDate?: string;
}

import { Component, Inject, Input, Optional } from '@angular/core';
import { AbstractControl } from '@angular/forms';
import { SWUI_CONTROL_MESSAGES } from './swui-control-messages.token';
import { SwuiIsControlInvalidService } from '../swui-is-control-invalid/swui-is-control-invalid.service';

export interface SwuiControlMessages {
  [key: string]: string;
}

@Component({
    selector: 'lib-swui-control-messages',
    template: `
    <span *ngIf="errorMessage">{{ errorMessage | translate: params }}</span>
  `,
    standalone: false
})
export class SwuiControlMessagesComponent {
  @Input() control?: AbstractControl;
  /**
   * @deprecated use SwuiIsControlInvalidService
   */
  @Input() force = false;

  @Input('messages') set setMessages( value: SwuiControlMessages | undefined ) {
    if (value) {
      this.messages = { ...(this.config || {}), ...value };
    }
  }

  params?: Object;
  private messages: SwuiControlMessages;

  constructor( @Optional() @Inject(SWUI_CONTROL_MESSAGES) private readonly config: SwuiControlMessages,
               @Optional() private readonly service: SwuiIsControlInvalidService ) {
    this.messages = config || {};
  }

  get errorMessage(): string | undefined {
    const control = this.control;
    if (control) {
      if (this.force && !control.touched) {
        control.markAsTouched();
      }
      const [key, values] = this.error(control);
      this.params = values;
      return key && this.messages[key];
    }
    return undefined;
  }

  private error( control: AbstractControl ): [string | undefined, any] {
    if (this.isControlInvalid(control)) {
      return Object.entries(control.errors || {}).shift() || [undefined, undefined];
    }
    return [undefined, undefined];
  }

  private isControlInvalid( control: AbstractControl ): boolean {
    if (this.service) {
      return this.service.isControlInvalid(control);
    } else {
      return control.touched && control.invalid;
    }
  }
}

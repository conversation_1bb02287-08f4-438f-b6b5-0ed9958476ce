import { moduleMetadata, storiesOf } from '@storybook/angular';

import { SwuiControlStoriesModule } from './stories/swui-control-stories/swui-control-stories.module';
import { SwuiControlStoriesComponent } from './stories/swui-control-stories/swui-control-stories.component';

storiesOf('Forms/ControlMessages', module)
  .addDecorator(moduleMetadata({
    imports: [
      SwuiControlStoriesModule,
    ],
  }))
  .add('Example', () => ({
    component: SwuiControlStoriesComponent,
  }));


  /*.add('Error', () => {
    const transform = (validatorName: string, validatorValue: any) => of(`${validatorName}-${validatorValue}`);
    const control = new FormControl(null, [Validators.required]);
    control.markAllAsTouched();
    return ({
      component: SwuiControlMessagesComponent,
      props: {
        control,
        transform
      },
    });
  })
  .add('Force error', () => {
    const transform = (validatorName: string, validatorValue: any) => of(`${validatorName}-${validatorValue}`);
    const control = new FormControl(null, [Validators.required]);
    return ({
      component: SwuiControlMessagesComponent,
      props: {
        control,
        transform,
        force: true
      },
    });
  });*/

<form [formGroup]="form">
  <div class="form-group">
    <div>
      <mat-form-field appearance="outline">
        <mat-label>testControl</mat-label>
        <input type="text" matInput formControlName="testControl">
        <mat-error>
          <lib-swui-control-messages [control]="testControl"
                                     [messages]="messageErrors">
          </lib-swui-control-messages>
        </mat-error>
      </mat-form-field>
    </div>
    <div>
      <mat-form-field appearance="outline">
        <mat-label>testControlEmpty</mat-label>
        <input type="text" matInput formControlName="testControlEmpty">
        <mat-error>
          <lib-swui-control-messages [force]="false"
                                     [control]="testControlEmpty"
                                     [messages]="messageErrors">
          </lib-swui-control-messages>
        </mat-error>
      </mat-form-field>
    </div>
    <div>
      <mat-form-field appearance="outline">
        <mat-label>testControlForced</mat-label>
        <input type="text" matInput formControlName="testControlForced">
        <mat-error>
          <lib-swui-control-messages [force]="true"
                                     [control]="testControlForced"
                                     [messages]="messageErrors">
          </lib-swui-control-messages>
        </mat-error>
      </mat-form-field>
    </div>
  </div>
</form>

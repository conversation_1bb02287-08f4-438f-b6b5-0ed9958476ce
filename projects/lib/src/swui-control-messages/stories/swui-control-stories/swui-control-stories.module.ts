import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

import { SwuiControlStoriesComponent } from './swui-control-stories.component';
import { SwuiControlMessagesModule } from '../../swui-control-messages.module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';


@NgModule({
  imports: [
    BrowserAnimationsModule,
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    SwuiControlMessagesModule,
    MatInputModule,
    TranslateModule.forRoot(),
  ],
  declarations: [
    SwuiControlStoriesComponent
  ],
})
export class SwuiControlStoriesModule {
}

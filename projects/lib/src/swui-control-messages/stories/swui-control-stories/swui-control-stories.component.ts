import { Component } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';

export interface ErrorMessage {
  [key: string]: any;
}

@Component({
    selector: 'lib-swui-control-stories',
    templateUrl: './swui-control-stories.component.html',
    standalone: false
})
export class SwuiControlStoriesComponent {
  form: UntypedFormGroup;

  messageErrors: ErrorMessage = {
    required: 'field is required',
    maxlength: `Maximum length is ${3}`,
  };

  get testControl(): UntypedFormControl {
    return this.form.get('testControl') as UntypedFormControl;
  }

  get testControlEmpty(): UntypedFormControl {
    return this.form.get('testControlEmpty') as UntypedFormControl;
  }

  get testControlForced(): UntypedFormControl {
    return this.form.get('testControlForced') as UntypedFormControl;
  }

  constructor(private fb: UntypedFormBuilder ) {
    this.form = this.fb.group({
      testControl: ['0', Validators.compose([
        Validators.required,
        Validators.maxLength(3)
      ])],
      testControlEmpty: [null, Validators.compose([
        Validators.required,
        Validators.maxLength(3)
      ])],
      testControlForced: [null, Validators.compose([
        Validators.required,
        Validators.maxLength(3)
      ])],
    });
  }
}

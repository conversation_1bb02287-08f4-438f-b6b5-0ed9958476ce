import { ModuleWithProviders, NgModule } from '@angular/core';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { Observable, of } from 'rxjs';

interface Translations {
  [lang: string]: any;
}

export interface I18nModuleConfig {
  useDefaultLang?: boolean;
  translations: Translations;
}

export class I18nLoader implements TranslateLoader {
  constructor( private translations: Translations ) {
  }

  getTranslation( lang: string ): Observable<any> {
    return of(this.translations[lang]);
  }
}

export function i18nLoaderFactory(config: I18nModuleConfig) {
  return () => new I18nLoader(config.translations);
}

@NgModule({
  imports: [
    TranslateModule.forRoot(),
  ],
  exports: [
    TranslateModule
  ]
})
export class I18nModule {

  constructor( translate: TranslateService ) {
    translate.setDefaultLang('en');
    translate.use('en');
  }

  static forRoot( config: I18nModuleConfig = { translations: { en: {} } } ): ModuleWithProviders<I18nModule> {
    return {
      ngModule: I18nModule,
      providers: [
        { provide: TranslateLoader, useFactory: i18nLoaderFactory(config) }
      ]
    };
  }
}

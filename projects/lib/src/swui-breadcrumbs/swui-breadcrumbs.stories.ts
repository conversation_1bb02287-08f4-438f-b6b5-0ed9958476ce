import { APP_BASE_HREF } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { moduleMetadata, storiesOf } from '@storybook/angular';
import { RouterModule } from '@angular/router';

import { SwuiBreadcrumbsComponent } from './swui-breadcrumbs.component';
import { MODULES } from './swui-breadcrumbs.module';
import { MatMenuModule } from '@angular/material/menu';
import { MatInputModule } from '@angular/material/input';

storiesOf('Breadcrumbs', module)
  .addDecorator(
    moduleMetadata({
      declarations: [
        SwuiBreadcrumbsComponent,
      ],
      imports: [
        BrowserAnimationsModule,
        MatInputModule,
        MatMenuModule,
        ...MODULES,
        RouterModule.forRoot([
          {
            path: 'iframe.html',
            redirectTo: '',
            pathMatch: 'full'
          },
          {
            path: 'vertebrates',
            component: SwuiBreadcrumbsComponent,
            data: {
              breadcrumb: 'vertebrates',
            },
            children: [
              {
                path: 'fish',
                component: SwuiBreadcrumbsComponent,
                data: {
                  breadcrumb: 'fishes'
                },
                children: [
                  {
                    path: ':id',
                    component: SwuiBreadcrumbsComponent,
                    data: {
                      breadcrumb: ''
                    }
                  },
                ]
              },
              {
                path: 'mammals',
                component: SwuiBreadcrumbsComponent,
                data: {
                  breadcrumb: 'mammals',
                },
              }
            ]
          },
          {
            path: 'invertebrates',
            component: SwuiBreadcrumbsComponent,
            data: {
              breadcrumb: 'invertebrates'
            }
          }
        ])
      ],
      providers: [{ provide: APP_BASE_HREF, useValue: '/' }]
    })
  )
  .add('Breadcrumbs for pre-defined menu with routes', () => ({
    component: SwuiBreadcrumbsComponent,
    template: `
<lib-swui-breadcrumbs></lib-swui-breadcrumbs>

<button mat-button [matMenuTriggerFor]="animals">Animal index</button>

<mat-menu #animals="matMenu">
  <button mat-menu-item routerLink="/vertebrates" [matMenuTriggerFor]="vertebrates">Vertebrates</button>
  <button mat-menu-item routerLink="/invertebrates">Invertebrates</button>
</mat-menu>

<mat-menu #vertebrates="matMenu">
  <button mat-menu-item routerLink="/vertebrates/fish" [matMenuTriggerFor]="fish">Fishes</button>
  <button mat-menu-item routerLink="/vertebrates/mammals">Mammals</button>
</mat-menu>

<mat-menu #fish="matMenu">
  <button mat-menu-item routerLink="/vertebrates/fish/oilfish">Baikal oilfish</button>
</mat-menu>`,
  }));

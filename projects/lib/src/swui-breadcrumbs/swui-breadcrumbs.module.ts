import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SwuiBreadcrumbsComponent } from './swui-breadcrumbs.component';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatButtonModule } from '@angular/material/button';

export const MODULES = [
  MatButtonModule,
  MatIconModule,
  MatListModule,
];


@NgModule({
  declarations: [
    SwuiBreadcrumbsComponent,
  ],
  exports: [
    SwuiBreadcrumbsComponent,
  ],
  imports: [
    CommonModule,
    ...MODULES,
    RouterModule,
  ]
})
export class SwuiBreadcrumbsModule {
}

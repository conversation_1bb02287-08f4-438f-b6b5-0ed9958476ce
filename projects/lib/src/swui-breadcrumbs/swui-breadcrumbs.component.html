<mat-nav-list class="breadcrumbs">
  <ng-container *ngFor="let breadcrumb of breadcrumbs; let i = index">
    <a mat-list-item class="breadcrumb-item"
       *ngIf="breadcrumbs.length - 1 !== i; else tplNoLink"
       routerLink="{{breadcrumb.url}}">
      <mat-icon class="separator">chevron_right</mat-icon>
      <div matLine>{{ breadcrumb.label }}</div>
    </a>

    <ng-template #tplNoLink>
      <mat-list-item class="disabled">
        {{ breadcrumb.label }}
      </mat-list-item>
    </ng-template>
  </ng-container>
</mat-nav-list>

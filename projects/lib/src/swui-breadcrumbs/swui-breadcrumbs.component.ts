import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { distinctUntilChanged, filter } from 'rxjs/operators';

interface Breadcrumb {
  label: string;
  url: string;
}

@Component({
    selector: 'lib-swui-breadcrumbs',
    templateUrl: './swui-breadcrumbs.component.html',
    styleUrls: ['./swui-breadcrumbs.component.scss'],
    encapsulation: ViewEncapsulation.None,
    standalone: false
})
export class SwuiBreadcrumbsComponent implements OnInit {

  public breadcrumbs: Breadcrumb[];

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
  ) {
    this.breadcrumbs = this.buildBreadcrumbs(this.activatedRoute.root);
  }

  ngOnInit() {
    this.initBreadcrumbs();
  }

  initBreadcrumbs() {
    this.router.events.pipe(
      filter(( event ) => event instanceof NavigationEnd),
      distinctUntilChanged(),
    ).subscribe(() => {
      this.breadcrumbs = this.buildBreadcrumbs(this.activatedRoute.root);
    });
  }

  buildBreadcrumbs( route: ActivatedRoute, url: string = '', breadcrumbs: Breadcrumb[] = [] ): Breadcrumb[] {
    let label = route.routeConfig && route.routeConfig.data ? route.routeConfig.data.breadcrumb : '';
    let path = route.routeConfig && route.routeConfig.data ? route.routeConfig.path : '';

    if (path) {
      const lastRoutePart = path.split('/').pop();
      if (lastRoutePart) {
        const isDynamicRoute = lastRoutePart.startsWith(':');
        if (isDynamicRoute && !!route.snapshot) {
          const paramName = lastRoutePart.split(':')[1];
          path = path.replace(lastRoutePart, route.snapshot.params[paramName]);
          label = route.snapshot.params[paramName];
        }
      }
    }

    const nextUrl = path ? `${url}/${path}` : url;

    const breadcrumb: Breadcrumb = {
      label: label,
      url: nextUrl,
    };

    const newBreadcrumbs = breadcrumb.label ? [...breadcrumbs, breadcrumb] : [...breadcrumbs];

    if (route.firstChild) {
      return this.buildBreadcrumbs(route.firstChild, nextUrl, newBreadcrumbs);
    }

    return newBreadcrumbs;
  }
}

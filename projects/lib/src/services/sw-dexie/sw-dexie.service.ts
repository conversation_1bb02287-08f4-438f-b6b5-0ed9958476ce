import { Inject, Injectable, InjectionToken } from '@angular/core';
import <PERSON><PERSON> from 'dexie';
import { ReplaySubject } from 'rxjs';
import { take } from 'rxjs/operators';
import { SwHubAuthService } from '../sw-hub-auth/sw-hub-auth.service';
import {
  IDexieAppSettings, IDexieColumn, IDexieComponentConfig, IDexieFilter, IDexieFilterState, IDexieRunSettings,
  IDexieUser, settingsNames,
  UserSettingOptions
} from './dexie-types';

export const DEXI_CONFIG = new InjectionToken<string>('SwDexieServiceConfig');

@Injectable()
export class SwDexieService extends Dexie {
  users: Dexie.Table<IDexieUser, number> | undefined;
  columns: Dexie.Table<IDexieColumn, number> | undefined;
  appSettings: Dexie.Table<IDexieAppSettings, number> | undefined;
  filters: Dexie.Table<IDexieFilter, number> | undefined;
  collapsedState: Dexie.Table<IDexieComponentConfig, number> | undefined;
  usersNotificatedAboutNewMenu: Dexie.Table<IDexieUser, number> | undefined;
  filterStates: Dexie.Table<IDexieFilterState, number> | undefined;
  runSettings: Dexie.Table<IDexieRunSettings, number> | undefined;
  user: IDexieUser | undefined;

  _ready = new ReplaySubject<any>(1);

  get ready(): Promise<any> {
    return !this.authService ? Promise.resolve({}) : new Promise(( resolve ) => {
      this._ready.pipe(
        take(1)
      ).subscribe(() => {
        resolve({});
      });
    });
  }

  constructor( private authService: SwHubAuthService,
               @Inject(DEXI_CONFIG) hubName: string
  ) {
    super(hubName);
    try {
      this.version(1).stores({
        filters: '++id,name,scope,data',
        settings: '++,name,params',
      });

      this.version(2).stores({
        users: '++id, &[username+entityKey]',
        columns: '++id, userId, schemaType, params',
        appSettings: '++id, userId, params',
        filters: '++id, [userId+scope], name, data',
        loginKeys: '++id, active, key, label',
      });

      this.version(3).stores({
        collapsedState: '++id, userId, componentId, data, scope, &[userId+componentId+scope]',
      });

      this.version(4).stores({
        columns: '++id, [userId+schemaType], params',
      });

      this.version(5).stores({
        usersNotificatedAboutNewMenu: '++id, &[username+entityKey]',
      });

      this.version(6).stores({
        filterStates: '++id, &[username+entityKey+componentName], state',
      });

      this.version(7).stores({
        lastVisitedPages: '++id, &[username+entityKey], url',
      });

      this.version(8).stores({
        runSettings: '++id, &[username+entityKey+path], params',
      });
    } catch (e) {
      console.error('SwDexieService constructor error ', e);
    }
    this._subscribeUser();
  }

  getSetting( opts: UserSettingOptions ): Promise<any> {
    let result;
    switch (opts.name) {
      case 'filter':
        result = this.getFilterState(opts?.component);
        break;
      default:
        result = Promise.resolve();
    }
    return result;
  }

  putSetting( opts: UserSettingOptions, value: any ) {
    let result;
    switch (opts.name) {
      case settingsNames.filter:
        if (opts.component) {
          result = this.putFilterState(opts.component, value);
        }
        break;
      default:
        result = Promise.resolve();
    }
    return result;
  }

  getColumn( schemaType: string ): Promise<IDexieColumn | undefined> {
    return this.ready.then(() => {
      return this.columns?.get({ userId: this.user?.id, schemaType });
    });
  }

  saveColumn( schemaType: string, params: any ): Promise<number | undefined> {
    return this.ready
      .then(() => this.getColumn(schemaType))
      .then(( column ) => {
        if (column && column.id) {
          return this.columns?.update(column.id, { userId: this.user?.id, schemaType, params });
        } else {
          return this.columns?.add({ userId: this.user?.id || 0, schemaType, params });
        }
      });
  }

  getAppSettings(): Promise<IDexieAppSettings | undefined> {
    return this.ready.then(() => {
      return this.appSettings?.get({ userId: this.user?.id });
    });
  }

  saveAppSettings( params: any ): Promise<IDexieAppSettings | number | undefined> {
    return this.ready.then(() => {
      return this.getAppSettings().then(( appSettings ) => {
        if (appSettings && appSettings.id) {
          return this.appSettings?.update(appSettings?.id, { userId: this.user?.id, params });
        } else {
          return this.appSettings?.add({ userId: this.user?.id || 0, params });
        }
      });
    });
  }

  getComponentState( componentId: string, scope: string ): any {
    return this.ready.then(() => {
      return this.collapsedState?.get({ userId: this.user?.id, componentId, scope });
    });
  }

  updateComponentState( componentId: string, scope: string, data: any ): void {
    let search = { userId: this.user?.id || 0, componentId, scope };
    this.collapsedState?.get(search, res => {
      res = Object.assign(res || search, { data });
      this.collapsedState?.put(res);
    });
  }

  setUserToNotificatedList(): Promise<number | undefined> {
    return this.ready.then(() => {
      const { username, entityKey } = this.authService;
      const newUser: IDexieUser = {
        username: username,
        entityKey: entityKey,
      };

      return this.usersNotificatedAboutNewMenu?.add(newUser);
    });
  }

  isNotUserInNotificatedList(): Promise<boolean | undefined> {
    return this.ready.then(() => {
      const findOptions = {
        username: this.authService.username,
        entityKey: this.authService.entityKey,
      };

      return this.usersNotificatedAboutNewMenu?.get(findOptions)
        .then(user => !user);
    });
  }

  getFilterState( componentName: string | undefined ): Promise<any> {
    return this.ready.then(() => {
      const findOptions = {
        username: this.authService.username,
        entityKey: this.authService.entityKey,
        componentName,
      };
      return this.filterStates?.get(findOptions)
        .then(filterState => {
          return filterState ? JSON.parse(filterState.state) : {};
        });
    });
  }

  putFilterState( componentName: string, state: Record<string, any> ): Promise<number | undefined> {
    return this.ready.then(() => {
      const findOptions = {
        username: this.authService.username,
        entityKey: this.authService.entityKey,
        componentName,
      };

      const newState = JSON.stringify(state);
      const createOptions: IDexieFilterState = { ...findOptions, state: newState };

      return this.filterStates?.get(findOptions)
        .then(row => {
          if (row && row.id) {
            return this.filterStates?.update(row.id, { state: newState });
          }
          if (!!createOptions) {
            return this.filterStates?.add(createOptions);
          }
        });
    });
  }

  getRunSettings( path: string | undefined ): Promise<any> {
    return this.ready.then(() => {
      const findOptions = {
        username: this.authService.username,
        entityKey: this.authService.entityKey,
        path,
      };
      return this.runSettings?.get(findOptions)
        .then(settings => {
          return settings ? JSON.parse(settings.params) : {};
        });
    });
  }

  putRunSettings( path: string, params: Record<string, any> ): Promise<number | undefined> {
    return this.ready.then(() => {
      const findOptions = {
        username: this.authService.username,
        entityKey: this.authService.entityKey,
        path,
      };

      const newParams = JSON.stringify(params);
      const createOptions: IDexieRunSettings = { ...findOptions, params: newParams };

      return this.runSettings?.get(findOptions)
        .then(row => {
          if (row && row.id) {
            return this.runSettings?.update(row.id, { params: newParams });
          }
          if (!!createOptions) {
            return this.runSettings?.add(createOptions);
          }
        });
    });
  }

  private _subscribeUser(): void {
    this.authService.logged.asObservable().subscribe(() => {
      if (this.authService.isLogged()) {
        let activeKey = this.authService.entityKey;
        if (activeKey && this.authService.username) {
          this._getOrCreateUser(this.authService.username, activeKey);
        }
      }
    });
  }

  private _getOrCreateUser( username: string, entityKey: string ): Promise<IDexieUser | undefined> {
    let userObj = { username, entityKey };

    // @ts-ignore
    return this.users?.get(userObj)
      .then(this._saveUser.bind(this, userObj))
      .catch(console.error.bind(console));
  }

  private _saveUser( userObj: any, user: any ): void {
    if (user) {
      this.user = user;
      this._ready.next(undefined);
    } else {
      this.users?.add(Object.assign({}, userObj))
        .then(() => {
          this.users?.get(userObj).then(this._saveUser.bind(this, userObj)).catch(console.error.bind(console));
        })
        .catch(console.error.bind(console));
    }
  }
}

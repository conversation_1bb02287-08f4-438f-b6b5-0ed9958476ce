import { Injectable } from '@angular/core';
import { from, Observable } from 'rxjs';
import { map, mapTo } from 'rxjs/operators';
import { ColumnsManagementDataProvider } from '../../swui-schema-grid/columns-management/columns-management.model';
import { IDexieColumn } from './dexie-types';
import { SwDexieService } from './sw-dexie.service';

@Injectable()
export class SwDexieColumnsDataProviderService implements ColumnsManagementDataProvider {

  constructor(
    private dexieService: SwDexieService,
  ) {
  }

  getColumns( gridId: string ): Observable<any> {
    return new Observable(observer => {
      from(this.dexieService.getColumn(gridId))
        .pipe(
          map(( column: IDexieColumn | undefined ) => {
            let mapped = {};
            if (typeof column !== 'undefined' && 'params' in column) {
              mapped = column.params;
            }
            return mapped;
          })
        ).subscribe(observer);
    });
  }

  saveColumns( gridId: string, data: any ): Observable<any> {
    return new Observable(observer => {
      from(this.dexieService.saveColumn(gridId, data))
        .pipe(mapTo(data))
        .subscribe(observer);
    });
  }
}

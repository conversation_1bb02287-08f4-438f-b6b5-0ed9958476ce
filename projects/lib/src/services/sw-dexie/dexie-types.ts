export interface IDexieUser {
  id?: number;
  username: string | undefined;
  entityKey: string | undefined;
}

export interface IDexieColumn {
  id?: number;
  userId: number;
  schemaType: string;
  params: any;
}

export interface IDexieAppSettings {
  id?: number;
  userId: number;
  params: any;
}

export interface IDexieFilter {
  id?: number;
  userId: number;
  name: string;
  scope: string;
  data: any;
}

export interface IDexieComponentConfig {
  id?: number;
  userId: number;
  componentId: string;
  scope: string;
  data: any;
}

export interface IDexieFilterState {
  id?: number;
  username: string | undefined;
  entityKey: string | undefined;
  componentName: string | undefined;
  state: any;
}

export interface IDexieRunSettings {
  id?: number;
  username: string | undefined;
  entityKey: string | undefined;
  path: string | undefined;
  params: any;
}

export interface IDexieLastVisitedPage {
  id?: number;
  username: string;
  entityKey: string;
  url: string;
}


export interface UserSettingName {
  filter: string;
}

export const settingsNames: UserSettingName = {
  filter: 'filter'
};

export interface UserSettingOptions {
  name: string;
  component?: string;
}

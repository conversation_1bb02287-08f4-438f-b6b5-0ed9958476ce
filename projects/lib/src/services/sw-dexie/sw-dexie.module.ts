import { ModuleWithProviders, NgModule } from '@angular/core';
import { DEXI_CONFIG, SwDexieService } from './sw-dexie.service';

@NgModule()
export class SwDexieModule {
  static forRoot( hubName: string ): ModuleWithProviders<SwDexieModule> {
    return {
      ngModule: SwDexieModule,
      providers: [
        { provide: DEXI_CONFIG, useValue: hubName },
        SwDexieService,
      ]
    };
  }
}

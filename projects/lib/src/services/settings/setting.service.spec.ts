import { DEFAULT_SETTINGS, SettingsService } from './settings.service';
import { TestBed } from '@angular/core/testing';
import { AppSettings } from './app-settings';
import moment from 'moment';

describe( 'SwuiSettingsService', () => {
  const test_settings: AppSettings = {
    pageSize: 10,
    currencyFormat: window.navigator.language,
    dateFormat: 'DD.MM.YYYY',
    timeFormat: 'HH:mm:ss',
    timezoneName: moment.tz.guess()
  };
  let service: SettingsService;

  beforeEach( () => {
    TestBed.configureTestingModule( {
      providers: [
        SettingsService
      ]
    });
    service = TestBed.inject( SettingsService );
  });

  it( 'it should be created', () => {
    expect( service ).toBeTruthy();
  });

  it( 'it should return default settings value', () => {
    expect( service.resolve() ).toBe( DEFAULT_SETTINGS );
    expect( service.appSettings ).toBe( DEFAULT_SETTINGS );
  });

  it( 'it should return new settings value from getter', () => {
    service.use( test_settings );
    expect( service.resolve() ).toBe( test_settings );
    expect( service.appSettings ).toBe( test_settings );
  });

  it( 'it should return default settings value from observable', () => {
    service.appSettings$.subscribe( ( value: AppSettings ) => expect( value ).toBe( DEFAULT_SETTINGS ));
  });

  it( 'it should return test settings value from observable', () => {
    service.use( test_settings );
    service.appSettings$.subscribe( ( value: AppSettings ) => expect( value ).toBe( test_settings ));
  });
});

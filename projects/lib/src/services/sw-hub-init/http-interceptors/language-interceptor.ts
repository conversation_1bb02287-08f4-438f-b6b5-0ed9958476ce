import { Inject, Injectable } from '@angular/core';
import { <PERSON>tt<PERSON><PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { SWUI_HUB_MESSAGE_CONFIG } from '../sw-hub-init.token';
import { SwHubMessageModuleConfig } from '../sw-hub-init.model';


/** Pass untouched request through to the next request handler. */
@Injectable()
export class LanguageInterceptor implements HttpInterceptor {
  private readonly langs: { [key: string]: string };

  constructor( private readonly translate: TranslateService, @Inject(SWUI_HUB_MESSAGE_CONFIG) config: SwHubMessageModuleConfig ) {
    this.langs = config.langs.reduce(( result, { id, dialect } ) => ({
      ...result,
      [id]: dialect
    }), {});
  }

  intercept( req: HttpRequest<any>, next: <PERSON>ttp<PERSON><PERSON><PERSON> ): Observable<HttpEvent<any>> {
    const lang = this.langs[this.translate.currentLang] || this.translate.currentLang;
    return next.handle(req.clone(lang ? {
      setHeaders: {
        'Accept-Language': lang
      }
    } : {}));
  }
}

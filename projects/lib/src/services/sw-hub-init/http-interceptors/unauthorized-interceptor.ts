import { Inject, Injectable } from '@angular/core';
import { <PERSON>ttp<PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, <PERSON>ttpHead<PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { SwHubInitService } from '../sw-hub-init.service';
import { SWUI_HUB_MESSAGE_CONFIG } from '../sw-hub-init.token';
import { SwHubMessageModuleConfig } from '../sw-hub-init.model';
import { DOCUMENT } from '@angular/common';

function isJson( headers: HttpHeaders ): boolean {
  if (!headers || !headers.has('content-type')) {
    return false;
  }
  return (headers.get('content-type') || '').startsWith('application/json');
}

@Injectable()
export class UnauthorizedInterceptor implements HttpInterceptor {
  private readonly location?: Location;

  constructor( @Inject(SWUI_HUB_MESSAGE_CONFIG) private readonly config: SwHubMessageModuleConfig,
               @Inject(DOCUMENT) doc: any,
               private readonly hubService: SwHubInitService ) {
    this.location = (doc as Document)?.location;
  }

  intercept( req: HttpRequest<any>, next: HttpHandler ): Observable<HttpEvent<any>> {
    if (this.isBlacklistedRoute(req)) {
      return next.handle(req);
    }
    return next.handle(req).pipe(
      catchError(response => {
        if (response.status === 401 && isJson(response.headers) && response.error && response.error.code !== 203) {
          this.hubService.sendTokenExpired({ error: response.error });
        }
        return throwError(response);
      })
    );
  }

  private isBlacklistedRoute( request: HttpRequest<any> ): boolean {
    const requestedUrl = new URL(request.url, this.location?.origin);
    return (this.config.auth?.blacklistedRoutes || []).findIndex(route => {
      if (typeof route === 'string') {
        const { hostname, pathname } = new URL(route, this.location?.origin);
        return hostname === requestedUrl.hostname && pathname === requestedUrl.pathname;
      }
      return route.test(request.url);
    }) > -1;
  }
}

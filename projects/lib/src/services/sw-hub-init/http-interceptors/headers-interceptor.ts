import { <PERSON>ttp<PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

/** Pass untouched request through to the next request handler. */
@Injectable()
export class HeadersInterceptor implements HttpInterceptor {

  intercept( req: HttpRequest<any>, next: HttpHandler ): Observable<HttpEvent<any>> {
    let headers: { [key: string]: string } = {
      initiatorServiceName: 'Backoffice'
    };
    if (!req.headers.has('Content-Type')) {
      headers = { ...headers, 'Content-Type': 'application/json' };
    }
    return next.handle(req.clone({
      setHeaders: headers
    }));
  }
}

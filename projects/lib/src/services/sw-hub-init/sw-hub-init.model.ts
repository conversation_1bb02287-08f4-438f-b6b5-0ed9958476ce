import { AppSettings } from '../settings/app-settings';
import { SwHubAuthModuleConfig } from '../sw-hub-auth/sw-hub-auth.module';

interface LocationExceptions {
  url: string;
  replaceTo: string;
}

export interface SwHubMessageModuleConfig {
  name: string;
  path?: string;
  langs: {
    id: string;
    dialect?: string | string[];
    title?: string;
    image?: string;
  }[];
  defaultLang?: string;
  logo?: string;
  logoSymbols?: string;
  auth?: SwHubAuthModuleConfig;
  lastLocationExceptions?: LocationExceptions[];
  lastUnknownLocationExceptions?: string[];
}

export interface BridgeLoadedMessageBody {
  accessToken?: string | null;
  grantedPermissions?: string | null;
  lang?: string | null;
  settings?: AppSettings | null;
  navigation?: NavigationHistoryBody | null;
  entityId?: string | null;
  twoFactor: boolean;
}

export interface OpenHubMessageBody {
  targetHub: string;
  location: string; // should starts with "/"
}

export interface TokenExpiredMessageBody {
  error: {
    code: number;
    message: string;
  };
}

export interface NavigationHistoryBody {
  hub: string;
  url: string;
}

export interface HubMessage {
  target: string;
  initiator: string;
  type: string;
  body: any;
}

export const TYPES = {
  BRIDGE_LOADED: 'bridge_loaded',
  LOGIN: 'login',
  LOGOUT: 'logout',
  HUB_LOADED: 'hub_loaded',
  OPEN_HUB: 'open_hub',
  TOKEN: 'token',
  TOKEN_EXPIRED: 'token_expired',
  LOCALE_CHANGED: 'locale_changed',
  APP_SETTINGS_CHANGED: 'app_settings_changed',
  ENTITY_ID_CHANGED: 'entity_id_changed',
  SIDEBAR_TOGGLE: 'sidebar_toggle',
  SIDEBAR_COLLAPSED: 'sidebar_collapsed',
  LOCATION_CHANGED: 'location_changed', // from child to base
  USER_ACTIVITY: 'user_activity',
  PAGE_TITLE: 'page_title',
  UNKNOWN_LOCATION: 'unknown_location'
};

export function isHubMessage( event: MessageEvent ): boolean {
  return typeof event.data === 'object' &&
    'target' in event.data &&
    'initiator' in event.data &&
    'type' in event.data &&
    'body' in event.data;
}


import { TestBed } from '@angular/core/testing';
import { CommonModule } from '@angular/common';
import { JwtModule } from '@auth0/angular-jwt';
import { SwHubInitService } from './sw-hub-init.service';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import { SwHubAuthModule } from '../sw-hub-auth/sw-hub-auth.module';
import { SettingsService } from '../settings/settings.service';
import { SwuiSidebarModule } from '../../swui-sidebar/swui-sidebar.module';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { SwHubConfigService } from '../sw-hub-config/sw-hub-config.service';
import { SWUI_HUB_MESSAGE_CONFIG } from './sw-hub-init.token';
import { SwHubEntityService } from '../sw-hub-entity/sw-hub-entity.service';
import { SwuiSidebarService } from '../../swui-sidebar/swui-sidebar.service';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

export function tokenGetter() {
  return '';
}

describe('SwHubInitService', () => {
  let service: SwHubInitService;
  beforeEach(() => {
    TestBed.configureTestingModule({
    imports: [CommonModule,
        RouterTestingModule,
        TranslateModule.forRoot(),
        JwtModule.forRoot({
            config: {
                tokenGetter: tokenGetter,
            }
        }),
        SwHubAuthModule,
        SwuiSidebarModule],
    providers: [
        SwHubInitService,
        SwHubConfigService,
        SettingsService,
        SwHubEntityService,
        SwuiSidebarService,
        { provide: SWUI_HUB_MESSAGE_CONFIG, useValue: { langs: [] } },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting()
    ]
});
    service = TestBed.inject(SwHubInitService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});

import { Injectable } from '@angular/core';
import { ReplaySubject } from 'rxjs';
import { JwtHelperService } from '@auth0/angular-jwt';
import { PermissionService } from './permission.service';

@Injectable()
export class SwHubAuthService {
  logged = new ReplaySubject<void>();
  accessToken: string | undefined;
  entityKey: string | undefined;
  username: string | undefined;
  isSuperAdmin = false;
  isTwoFactor = false;

  private expirationDate: number | undefined;
  private permissionService: PermissionService | undefined;
  private readonly jwt = new JwtHelperService();

  isLogged(): boolean {
    if (this.expirationDate) {
      return this.expirationDate > new Date().getTime();
    }
    return this.accessToken !== undefined;
  }

  tokenGetter() {
    return () => this.accessToken;
  }

  setToken( accessToken?: string | null, grantedPermissions?: string | null, twoFactor?: boolean): void {
    this.logout();
    if (accessToken) {
      const token = this.decode(accessToken);
      const permissions = grantedPermissions ? this.decode(grantedPermissions).permissions : null;
      if (token) {
        this.isTwoFactor = !!twoFactor;
        this.accessToken = accessToken;
        this.entityKey = `${token.entityId}`;
        this.username = token.username;
        this.isSuperAdmin = token.isSuperAdmin || false;
        this.permissionService = new PermissionService(permissions || token.grantedPermissions || []);

        if (token.hasOwnProperty('exp')) {
          const date = new Date(0);
          date.setUTCSeconds(token.exp);
          this.expirationDate = date.getTime();
          console.log(`Token expired at ${date}`);
        } else {
          this.expirationDate = undefined;
        }
      }
    }
    this.logged.next(undefined);
  }

  logout(): void {
    this.accessToken = undefined;
    this.entityKey = undefined;
    this.username = undefined;
    this.isSuperAdmin = false;
    this.permissionService = undefined;
    this.expirationDate = undefined;
  }

  getGrantedPermissions(): string[] {
    return this.permissionService ? this.permissionService.permissions : [];
  }

  allowedTo( permissions: string[] ): boolean {
    return this.permissionService ? this.permissionService.allowedTo(permissions) : false;
  }

  areGranted( permissions: string[] ): boolean {
    return this.permissionService ? this.permissionService.areGranted(permissions) : false;
  }

  private decode( token: string ): any | null {
    try {
      return this.jwt.decodeToken(token);
    } catch (e) {
      console.error(e);
    }
    return null;
  }
}

export const PERMISSIONS_NAMES = Object.freeze({
  KEYENTITY_REPORT: 'keyentity:report',
  KEYENTITY_REPORT_CURRENCY: 'keyentity:report:currency',
  KEYENTITY_REPORT_WALLET_CURRENCY: 'keyentity:report:wallet-currency',
  K<PERSON><PERSON>ENTITY_REPORT_PLAYERS: 'keyentity:report:players',
  KEYENTITY_REPORT_PROMO: 'keyentity:report:promo',
  REPORT: 'report',
  REPORT_CURRENCY: 'report:currency',
  KEYENTITY_CASHIER: 'keyentity:cashier',
  REPORT_WALLET_CURRENCY: 'report:wallet-currency',
  REPORT_PLAYERS: 'report:players',
  REPORT_WITHOUT_LIMIT: 'report-without-limit',
  REPORT_PROMO: 'report:promo',
  PLAYER: 'player',
  PLAYER_RESET_CHANGE_NICKNAME: 'player:reset-change-nickname-attempts',
  ENTITY_PLAYER: 'entity:player',
  <PERSON><PERSON><PERSON>ENTITY_PLAYER: 'keyentity:player',
  <PERSON><PERSON><PERSON>EN<PERSON>TY_PLAYER_DEPOSIT: 'keyentity:player:deposit',
  KEYENTITY_PLAYER_WITHDRAWAL: 'keyentity:player:withdrawal',
  PLAYER_PROMOTION: 'player:promotion',
  KEYENTITY_PLAYER_PROMOTION: 'keyentity:player:promotion',
  PLAYER_VIEW: 'player:view',
  ENTITY_PLAYER_VIEW: 'entity:player:view',
  KEYENTITY_PLAYER_VIEW: 'keyentity:player:view',
  KEYENTITY_PLAYER_CREATE: 'keyentity:player:create',
  USER: 'user',
  USER_EDIT: 'user:edit',
  USER_CREATE: 'user:create',
  USER_CHANGE_TYPE: 'user:change-type',
  ENTITY_USER: 'entity:user',
  KEYENTITY_USER: 'keyentity:user',
  KEYENTITY_USER_CREATE: 'keyentity:user:create',
  KEYENTITY_USER_CHANGE_TYPE: 'keyentity:user:change-type',
  KEYENTITY_USER_EDIT: 'keyentity:user:edit',
  USER_VIEW: 'user:view',
  ENTITY_USER_VIEW: 'entity:user:view',
  KEYENTITY_USER_VIEW: 'keyentity:user:view',
  LOBBY: 'lobby',
  LOBBY_CREATE: 'lobby:create',
  LOBBY_VIEW: 'lobby:view',
  LOBBY_EDIT: 'lobby:edit',
  LOBBY_DELETE: 'lobby:delete',
  KEYENTITY_LOBBY: 'keyentity:lobby',
  KEYENTITY_LOBBY_CREATE: 'keyentity:lobby:create',
  KEYENTITY_LOBBY_VIEW: 'keyentity:lobby:view',
  KEYENTITY_LOBBY_EDIT: 'keyentity:lobby:edit',
  KEYENTITY_LOBBY_DELETE: 'keyentity:lobby:delete',
  KEYENTITY_MERCHANT_VIEW: 'keyentity:merchant:view',
  KEYENTITY_INTEGRATION_VIEW: 'keyentity:integration:view',
  TERMINAL: 'keyentity:terminal',
  TERMINAL_VIEW: 'keyentity:terminal:view',
  ENTITY_GAME: 'entity:game',
  ENTITY_GAME_URL: 'entity:game:url',
  ENTITY_INFO: 'entity:info',
  ENTITY_GAME_UNFINISHED: 'entity:game:unfinished',
  ENTITY_GAME_HISTORY: 'entity:game:history',
  ENTITY_GAME_ADD_GAME_CASCADE: 'entity:game:add-game-cascade',
  ENTITY_GAME_CHANGE_STATE: 'entity:game:change-state',
  ENTITY_GAME_CHANGE_STATE_DISABLED: 'entity:game:change-state:disabled',
  ENTITY_GAME_CHANGE_STATE_ENABLED: 'entity:game:change-state:enabled',
  KEYENTITY_GOS_GAME_HISTORY: 'keyentity:gos:game:history',
  ENTITY_GOS_GAME_HISTORY: 'entity:gos:game:history',
  KEYENTITY_EXTERNAL_GAME_PROVIDER_HISTORY: 'keyentity:external-game-provider:history',
  KEYENTITY_EXTERNAL_GAME_PROVIDER_GAMECLOSE_FORCEFINISH: 'keyentity:external-game-provider:gameclose:forcefinish',
  ENTITY_EXTERNAL_GAME_PROVIDER_GAMECLOSE_FORCEFINISH: 'entity:external-game-provider:gameclose:forcefinish',
  ENTITY_EXTERNAL_GAME_PROVIDER_HISTORY: 'entity:external-game-provider:history',
  KEYENTITY_EXTERNAL_GAME_PROVIDER_AVAILABILITY: 'keyentity:external-game-provider:availability',
  ENTITY_EXTERNAL_GAME_PROVIDER_AVAILABILITY: 'entity:external-game-provider:availability',
  DISABLE_ENTITY_GAME_HISTORY_BALANCES: 'disable:entity:game-history:balances',
  DISABLE_KEYENTITY_GAME_HISTORY_BALANCES: 'disable:keyentity:game-history:balances',
  KEYENTITY_GAME: 'keyentity:game',
  KEYENTITY_GAME_HISTORY: 'keyentity:game:history',
  KEYENTITY_GAME_CHANGE_STATE: 'keyentity:game:change-state',
  KEYENTITY_GAME_CHANGE_STATE_ENABLED: 'keyentity:game:change-state:enabled',
  KEYENTITY_GAME_CHANGE_STATE_DISABLED: 'keyentity:game:change-state:disabled',
  KEYENTITY_GAMECATEGORY: 'keyentity:gamecategory',
  KEYENTITY_GAMECATEGORY_VIEW: 'keyentity:gamecategory:view',
  KEYENTITY_GAME_UNFINISHED: 'keyentity:game:unfinished',
  KEYENTITY_GAMEPROVIDER_GAME: 'keyentity:gameprovider:game',
  KEYENTITY_GAMEPROVIDER_GAME_CREATE: 'keyentity:gameprovider:game:create',
  KEYENTITY_GAMEPROVIDER_GAME_EDIT: 'keyentity:gameprovider:game:edit',
  KEYENTITY_MASTER_GAMECATEGORY_VIEW: 'keyentity:master:gamecategory:view',
  KEYENTITY_MASTER_GAMECATEGORY: 'keyentity:master:gamecategory',
  GRANTED_ALL: 'granted:all',
  GRANTED_MOCK: 'granted:mock',
  DENIED_ALL: 'denied:all',
  ENTITY: 'entity',
  ENTITY_VIEW: 'entity:view',
  ENTITY_CREATE: 'entity:create',
  ENTITY_EDIT: 'entity:edit',
  ENTITY_BALANCE: 'entity:balance',
  ENTITY_CHANGESTATE: 'entity:change-state',
  ENTITY_CHANGESTATE_TEST: 'entity:change-state-test',
  ENTITY_GAMECLOSE_FORCEFINISH: 'entity:gameclose:forcefinish',
  KEYENTITY_GAMECLOSE_FORCEFINISH: 'keyentity:gameclose:forcefinish',
  ENTITY_GAMECLOSE_REVERT: 'entity:gameclose:revert',
  KEYENTITY_GAMECLOSE_REVERT: 'keyentity:gameclose:revert',
  ENTITY_GAMECLOSE_RETRY: 'entity:gameclose:retry',
  KEYENTITY_GAMECLOSE_RETRY: 'keyentity:gameclose:retry',
  ENTITY_GAMECLOSE_TRANSFER_OUT: 'entity:gameclose:transfer-out',
  KEYENTITY_GAMECLOSE_TRANSFER_OUT: 'keyentity:gameclose:transfer-out',
  AGENT: 'agent',
  AGENT_VIEW: 'agent:view',
  KEYENTITY_AGENT: 'keyentity:agent',
  KEYENTITY_AGENT_VIEW: 'keyentity:agent:view',
  AUDIT: 'audit',
  KEYENTITY_AUDIT: 'keyentity:audit',
  PAYMENT: 'payment',
  PAYMENT_VIEW: 'payment:view',
  PROMOTION: 'promotion',
  PROMOTION_VIEW: 'promotion:view',
  PROMOTION_CREATE: 'promotion:create',
  PROMOTION_EDIT: 'promotion:edit',
  PROMOTION_DELETE: 'promotion:delete',
  PROMOTION_BONUSCOIN: 'promotion:bonuscoin',
  PROMOTION_REBATE: 'promotion:rebate',
  KEYENTITY_PROMOTION: 'keyentity:promotion',
  KEYENTITY_PROMOTION_VIEW: 'keyentity:promotion:view',
  KEYENTITY_PROMOTION_CREATE: 'keyentity:promotion:create',
  KEYENTITY_PROMOTION_EDIT: 'keyentity:promotion:edit',
  KEYENTITY_PROMOTION_DELETE: 'keyentity:promotion:delete',
  KEYENTITY_PROMOTION_BONUSCOIN: 'keyentity:promotion:bonuscoin',
  KEYENTITY_PROMOTION_BONUSCOIN_VIEW: 'keyentity:promotion:bonuscoin:view',
  KEYENTITY_PROMOTION_BONUSCOIN_CREATE: 'keyentity:promotion:bonuscoin:create',
  KEYENTITY_PROMOTION_BONUSCOIN_EDIT: 'keyentity:promotion:bonuscoin:edit',
  KEYENTITY_PROMOTION_BONUSCOIN_DELETE: 'keyentity:promotion:bonuscoin:delete',
  KEYENTITY_PROMOTION_REBATE: 'keyentity:promotion:rebate',
  KEYENTITY_PROMOTION_REBATE_VIEW: 'keyentity:promotion:rebate:view',
  KEYENTITY_PROMOTION_REBATE_CREATE: 'keyentity:promotion:rebate:create',
  KEYENTITY_PROMOTION_REBATE_EDIT: 'keyentity:promotion:rebate:edit',
  KEYENTITY_PROMOTION_REBATE_DELETE: 'keyentity:promotion:rebate:delete',
  KEYENTITY_PROMOTION_VIRTUALMONEY: 'keyentity:promotion:virtualmoney',
  KEYENTITY_PROMOTION_VIRTUALMONEY_VIEW: 'keyentity:promotion:virtualmoney:view',
  KEYENTITY_PROMOTION_VIRTUALMONEY_CREATE: 'keyentity:promotion:virtualmoney:create',
  KEYENTITY_PROMOTION_VIRTUALMONEY_EDIT: 'keyentity:promotion:virtualmoney:edit',
  KEYENTITY_PROMOTION_VIRTUALMONEY_DELETE: 'keyentity:promotion:virtualmoney:delete',
  KEYENTITY_PROMOTION_FREEBET: 'keyentity:promotion:freebet',
  KEYENTITY_PROMOTION_FREEBET_VIEW: 'keyentity:promotion:freebet:view',
  KEYENTITY_PROMOTION_FREEBET_CREATE: 'keyentity:promotion:freebet:create',
  KEYENTITY_PROMOTION_FREEBET_EDIT: 'keyentity:promotion:freebet:edit',
  KEYENTITY_PROMOTION_FREEBET_DELETE: 'keyentity:promotion:freebet:delete',
  KEYENTITY_PROMOTION_SKYWIND: 'keyentity:promotion:skywind', // Manage skywind promotions
  KEYENTITY_PROMOTION_SKYWIND_CREATE: 'keyentity:promotion:skywind:create', // Create skywind promotions
  KEYENTITY_PROMOTION_SKYWIND_EDIT: 'keyentity:promotion:skywind:edit', // Update skywind promotions
  KEYENTITY_PROMOTION_OWNER: 'keyentity:promotion:owner', // Update owner of promotion
  KEYENTITY_PAYMENT: 'keyentity:payment',
  KEYENTITY_PAYMENT_VIEW: 'keyentity:payment:view',
  ROLE: 'role',
  ROLE_CREATE: 'role:create',
  ROLE_EDIT: 'role:edit',
  ROLE_VIEW: 'role:view',
  ROLE_DELETE: 'role:delete',
  DOMAIN: 'domain',
  FINANCE: 'finance',
  FINANCE_VIEW: 'finance:view',
  FINANCE_CREDIT: 'finance:credit',
  FINANCE_DEBIT: 'finance:debit',
  COUNTRY_ADD: 'country:add',
  BI_REPORTS_VIEW: 'bi:reports:view',
  KEYENTITY_BI_REPORTS_VIEW: 'keyentity:bi:reports:view',
  FORCE_RESET_PASSWORD: 'user-extra:force-reset-password',
  KEYENTITY_FORCE_RESET_PASSWORD: 'keyentity:user-extra:force-reset-password',
  FORCE_SET_EMAIL: 'user-extra:email:force-set',
  KEYENTITY_FORCE_SET_EMAIL: 'keyentity:user-extra:email:force-set',
  USER_DELETE: 'user-extra:delete',
  KEYENTITY_USER_DELETE: 'keyentity:user-extra:delete',
  KEYENTITY_RESPONSIBLEGAMING_PLAYER: 'keyentity:responsiblegaming:player',
  KEYENTITY_RESPONSIBLEGAMING_PLAYER_VIEW: 'keyentity:responsiblegaming:player:view',
  KEYENTITY_RESPONSIBLEGAMING_PLAYER_EDIT: 'keyentity:responsiblegaming:player:edit',
  RESPONSIBLEGAMING_PLAYER: 'responsiblegaming:player',
  RESPONSIBLEGAMING_PLAYER_VIEW: 'responsiblegaming:player:view',
  RESPONSIBLEGAMING_PLAYER_EDIT: 'responsiblegaming:player:edit',
  USER_UNBLOCK_CHANGE_PASSWORD: 'user-extra:change-password-unlock',
  KEYENTITY_USER_UNBLOCK_CHANGE_PASSWORD: 'keyentity:user-extra:change-password-unlock',
  USER_UNBLOCK_LOGIN: 'user-extra:login-unlock',
  KEYENTITY_USER_UNBLOCK_LOGIN: 'keyentity:user-extra:login-unlock',
  KEYENTITY_JURISDICTION: 'keyentity:jurisdiction',
  KEYENTITY_JURISDICTION_VIEW: 'keyentity:jurisdiction:view',
  KEYENTITY_ENTITYDOMAIN: 'keyentity:entitydomain',
  KEYENTITY_ENTITYDOMAIN_DYNAMIC: 'keyentity:entitydomain:dynamic',
  KEYENTITY_ENTITYDOMAIN_DYNAMIC_VIEW: 'keyentity:entitydomain:dynamic:view',
  ENTITY_ENTITYDOMAIN_DYNAMIC: 'entity:entitydomain:dynamic',
  ENTITY_ENTITYDOMAIN_DYNAMIC_VIEW: 'entity:entitydomain:dynamic:view',
  KEYENTITY_ENTITYDOMAIN_STATIC: 'keyentity:entitydomain:static',
  KEYENTITY_ENTITYDOMAIN_STATIC_VIEW: 'keyentity:entitydomain:static:view',
  ENTITY_ENTITYDOMAIN_STATIC: 'entity:entitydomain:static',
  ENTITY_ENTITYDOMAIN_STATIC_VIEW: 'entity:entitydomain:static:view',
  KEYENTITY_ENTITYDOMAIN_BULKOPERATION: 'keyentity:entitydomain:bulk-operation',
  KEYENTITY_BULKOPERATION: 'keyentity:bulk-operation',
  KEYENTITY_BI_REPORT_DOMAINS: 'keyentity:bi-reports-domains',
  KEYENTITY_BI_REPORT_DOMAINS_VIEW: 'keyentity:bi-reports-domains:view',
  KEYENTITY_BI_REPORT_DOMAINS_EDIT: 'keyentity:bi-reports-domains:edit',
  KEYENTITY_BI_REPORT_DOMAINS_SELECT: 'keyentity:bi-reports-domains:select',
  KEYENTITY_ROLE_CREATE: 'keyentity:role:create',
  KEYENTITY_ROLE_EDIT: 'keyentity:role:edit',
  KEYENTITY_ROLE_VIEW: 'keyentity:role:view',
  KEYENTITY_ROLE_DELETE: 'keyentity:role:delete',
  JURISDICTION: 'jurisdiction',
  JURISDICTION_VIEW: 'jurisdiction:view',
  SRT: 'srt',
  SRT_CHALLENGE: 'srt:challenge',
  SRT_TOURNAMENT: 'srt:tournament',
  GAME_SERVER: 'gs:settings',
  GAME_SERVER_VIEW: 'gs:settings:view',
  GAME_SERVER_CREATE: 'gs:settings:create',
  GAME_SERVER_EDIT: 'gs:settings:edit',
  GAME_SERVER_REMOVE: 'gs:settings:remove',
  GAME_GROUP_CREATE: 'gamegroup:create',
  KEYENTITY_GAME_GROUP_CREATE: 'keyentity:gamegroup:create',
  GAME_GROUP: 'gamegroup',
  GAME_GROUP_VIEW: 'gamegroup:view',
  KEYENTITY_GAME_GROUP: 'keyentity:gamegroup',
  KEYENTITY_GAME_GROUP_VIEW: 'keyentity:gamegroup:view',
  GAME_GROUP_EDIT: 'gamegroup:edit',
  KEYENTITY_GAME_GROUP_EDIT: 'keyentity:gamegroup:edit',
  GAME_GROUP_DELETE: 'gamegroup:delete',
  KEYENTITY_GAME_GROUP_DELETE: 'keyentity:gamegroup:delete',
  HUB_CASINO: 'hub:casino',
  HUB_ANALYTICS: 'hub:analytics',
  HUB_ENGAGEMENT: 'hub:engagement',
  HUB_ENGAGEMENT_TOURNAMENTS: 'hub:engagement:tournaments',
  HUB_ENGAGEMENT_PRIZE_DROPS: 'hub:engagement:prize-drops',
  HUB_ENGAGEMENT_MUST_WIN_JACKPOTS: 'hub:engagement:must-win-jackpots',
  HUB_STUDIO: 'hub:studio',
  KEYENTITY_BI_REPORT_PLAYER_SHOW_HIDE_COLUMN_DEBITS_CREDITS:
    'keyentity:bi:report:player-show-hide-column:debits-credits',
  BI_REPORT_PLAYER_SHOW_HIDE_COLUMN_DEBITS_CREDITS: 'bi:report:player-show-hide-column:debits-credits',
  GAME_LIMITS: 'gamelimits',
  KEYENTITY_GAME_LIMITS: 'keyentity:gamelimits',
  ENTITY_GAMECLOSE_FINALIZE: 'entity:gameclose:finalize',
  KEYENTITY_GAMECLOSE_FINALIZE: 'keyentity:gameclose:finalize',
  KEYENTITY_USER_CHANGE_PASSWORD: 'keyentity:user:change-password',
  GAMERTP: 'gamertp',
  GAMERTP_VIEW: 'gamertp:view',
  KEYENTITY_GAMERTP: 'keyentity:gamertp',
  KEYENTITY_GAMERTP_VIEW: 'keyentity:gamertp:view',
  KEYENTITY_GAMELABEL: 'keyentity:gamelabel',
  KEYENTITY_GAMELABEL_VIEW: 'keyentity:gamelabel:view',
  KEYENTITY_ENTITYLABELS: 'keyentity:entitylabels',
  KEYENTITY_ENTITYLABELS_VIEW: 'keyentity:entitylabels:view',
  KEYENTITY_ENTITYLABELS_CREATE: 'keyentity:entitylabels:create',
  ENTITYLABELS: 'entitylabels',
  ENTITYLABELS_VIEW: 'entitylabels:view',
  ENTITYLABELS_CREATE: 'entitylabels:create',
  JACKPOT: 'jackpot',
  JACKPOT_INSTANCE: 'jackpot:instance',
  JACKPOT_INSTANCE_VIEW: 'jackpot:instance:view',
  ENTITY_LIVEGAME: 'entity:live-game',
  ENTITY_LIVEGAME_ADD: 'entity:live-game:add-live-game',
  ENTITY_LIVEGAME_REMOVE: 'entity:live-game:remove-live-game',
  ENTITY_LIVEGAME_CHANGE_STATE: 'entity:live-game:change-state',
  ENTITY_LIVEGAME_CHANGE_STATE_ENABLED: 'entity:live-game:change-state:enabled',
  ENTITY_LIVEGAME_CHANGE_STATE_DISABLED: 'entity:live-game:change-state:disabled',
  KEYENTITY_LIVEGAME: 'keyentity:live-game',
  KEYENTITY_LIVEGAME_CHANGE_STATE: 'keyentity:live-game:change-state',
  KEYENTITY_LIVEGAME_CHANGE_STATE_ENABLED: 'keyentity:live-game:change-state:enabled',
  KEYENTITY_LIVEGAME_CHANGE_STATE_DISABLED: 'keyentity:live-game:change-state:disabled',
  FLAT_REPORTS: 'flat-reports',
  FLAT_REPORTS_VIEW: 'flat-reports:view',
  KEYENTITY_FLAT_REPORTS: 'keyentity:flat-reports',
  KEYENTITY_FLAT_REPORTS_VIEW: 'keyentity:flat-reports:view',
  ID_DECODE: 'id:decode',
  ID_ENCODE: 'id:encode',
  RESTRICTED_COUNTRIES_SOLUTION: 'restricted-countries-solution',
  JP_CONFIG_REPORT: 'jp-config-report',
  DEPLOYMENT: 'deployment',
  COUNTRY_REMOVE: 'country:remove',
});

export const PERMISSIONS_LIST = Object.freeze({
  GAME_STORE: [
    PERMISSIONS_NAMES.GRANTED_MOCK,
  ],
  REPORT: [
    PERMISSIONS_NAMES.KEYENTITY_REPORT,
    PERMISSIONS_NAMES.KEYENTITY_REPORT_CURRENCY,
    PERMISSIONS_NAMES.KEYENTITY_REPORT_WALLET_CURRENCY,
    PERMISSIONS_NAMES.KEYENTITY_REPORT_PLAYERS,
    PERMISSIONS_NAMES.REPORT,
    PERMISSIONS_NAMES.REPORT_CURRENCY,
    PERMISSIONS_NAMES.REPORT_WALLET_CURRENCY,
    PERMISSIONS_NAMES.REPORT_PLAYERS,
  ],
  PLAYER: [
    PERMISSIONS_NAMES.PLAYER,
    PERMISSIONS_NAMES.ENTITY_PLAYER,
    PERMISSIONS_NAMES.KEYENTITY_PLAYER,
  ],
  PLAYER_VIEW: [
    PERMISSIONS_NAMES.PLAYER_VIEW,
    PERMISSIONS_NAMES.ENTITY_PLAYER_VIEW,
    PERMISSIONS_NAMES.KEYENTITY_PLAYER_VIEW,
  ],
  USER: [
    PERMISSIONS_NAMES.USER,
    PERMISSIONS_NAMES.ENTITY_USER,
    PERMISSIONS_NAMES.KEYENTITY_USER,
  ],
  USER_VIEW: [
    PERMISSIONS_NAMES.USER_VIEW,
    PERMISSIONS_NAMES.ENTITY_USER_VIEW,
    PERMISSIONS_NAMES.KEYENTITY_USER_VIEW,
  ],
  LOBBY: [
    PERMISSIONS_NAMES.LOBBY,
    PERMISSIONS_NAMES.LOBBY_VIEW,
    PERMISSIONS_NAMES.KEYENTITY_LOBBY,
    PERMISSIONS_NAMES.KEYENTITY_LOBBY_VIEW,
  ],
  TERMINAL: [
    PERMISSIONS_NAMES.TERMINAL,
    PERMISSIONS_NAMES.TERMINAL_VIEW,
  ],
  REPORT_CURRENCY: [
    PERMISSIONS_NAMES.REPORT_CURRENCY,
    PERMISSIONS_NAMES.REPORT_WALLET_CURRENCY,
    PERMISSIONS_NAMES.KEYENTITY_REPORT_CURRENCY,
    PERMISSIONS_NAMES.KEYENTITY_REPORT_WALLET_CURRENCY,
  ],
  REPORT_PLAYERS: [
    PERMISSIONS_NAMES.REPORT_PLAYERS,
    PERMISSIONS_NAMES.KEYENTITY_REPORT_PLAYERS,
  ],
  REPORT_FINANCE: [
    PERMISSIONS_NAMES.FINANCE_VIEW,
  ],
  GAME: [
    PERMISSIONS_NAMES.ENTITY_GAME,
    PERMISSIONS_NAMES.KEYENTITY_GAME,
  ],
  GAME_CREATE: [
    PERMISSIONS_NAMES.KEYENTITY_GAMEPROVIDER_GAME,
    PERMISSIONS_NAMES.KEYENTITY_GAMEPROVIDER_GAME_CREATE
  ],
  GAME_EDIT: [
    PERMISSIONS_NAMES.KEYENTITY_GAMEPROVIDER_GAME,
    PERMISSIONS_NAMES.KEYENTITY_GAMEPROVIDER_GAME_EDIT
  ],
  GAMES_CASCADE_ADD: [
    PERMISSIONS_NAMES.ENTITY_GAME,
    PERMISSIONS_NAMES.ENTITY_GAME_ADD_GAME_CASCADE,
  ],
  GAME_HISTORY: [
    PERMISSIONS_NAMES.ENTITY_GAME_HISTORY,
    PERMISSIONS_NAMES.KEYENTITY_GAME_HISTORY,
  ],
  GOS_GAME_HISTORY: [
    PERMISSIONS_NAMES.ENTITY_GOS_GAME_HISTORY,
    PERMISSIONS_NAMES.KEYENTITY_GOS_GAME_HISTORY,
  ],
  EXTERNAL_GAME_PROVIDER_HISTORY: [
    PERMISSIONS_NAMES.ENTITY_EXTERNAL_GAME_PROVIDER_HISTORY,
    PERMISSIONS_NAMES.KEYENTITY_EXTERNAL_GAME_PROVIDER_HISTORY,
  ],
  EXTERNAL_GAME_PROVIDER_AVAILABILITY: [
    PERMISSIONS_NAMES.ENTITY_EXTERNAL_GAME_PROVIDER_AVAILABILITY,
    PERMISSIONS_NAMES.KEYENTITY_EXTERNAL_GAME_PROVIDER_AVAILABILITY,
  ],
  EXTERNAL_GAME_PROVIDER_AVAILABILITY_ENTITY: [
    PERMISSIONS_NAMES.ENTITY_EXTERNAL_GAME_PROVIDER_AVAILABILITY,
  ],
  EXTERNAL_GAME_PROVIDER_AVAILABILITY_KEYENTITY: [
    PERMISSIONS_NAMES.KEYENTITY_EXTERNAL_GAME_PROVIDER_AVAILABILITY,
  ],
  GAMECATEGORY: [
    PERMISSIONS_NAMES.KEYENTITY_GAMECATEGORY,
    PERMISSIONS_NAMES.KEYENTITY_GAMECATEGORY_VIEW,
  ],
  GAMELABEL: [
    PERMISSIONS_NAMES.KEYENTITY_GAMELABEL,
    PERMISSIONS_NAMES.KEYENTITY_GAMELABEL_VIEW
  ],
  ENTITY: [
    PERMISSIONS_NAMES.ENTITY,
    PERMISSIONS_NAMES.ENTITY_VIEW,
  ],
  AGENT: [
    PERMISSIONS_NAMES.AGENT,
    PERMISSIONS_NAMES.AGENT_VIEW,
    PERMISSIONS_NAMES.KEYENTITY_AGENT,
    PERMISSIONS_NAMES.KEYENTITY_AGENT_VIEW,
  ],
  AUDIT: [
    PERMISSIONS_NAMES.AUDIT,
    PERMISSIONS_NAMES.KEYENTITY_AUDIT,
  ],
  PAYMENT: [
    PERMISSIONS_NAMES.PAYMENT,
    PERMISSIONS_NAMES.PAYMENT_VIEW,
    PERMISSIONS_NAMES.KEYENTITY_PAYMENT,
    PERMISSIONS_NAMES.KEYENTITY_PAYMENT_VIEW,
  ],
  PROMOTION: [
    PERMISSIONS_NAMES.PROMOTION,
    PERMISSIONS_NAMES.KEYENTITY_PROMOTION,
  ],
  PROMOTION_VIEW: [
    PERMISSIONS_NAMES.PROMOTION_VIEW,
    PERMISSIONS_NAMES.KEYENTITY_PROMOTION_VIEW,
  ],
  PROMOTION_CREATE: [
    PERMISSIONS_NAMES.PROMOTION_CREATE,
    PERMISSIONS_NAMES.KEYENTITY_PROMOTION_CREATE,
  ],
  PROMOTION_EDIT: [
    PERMISSIONS_NAMES.PROMOTION_EDIT,
    PERMISSIONS_NAMES.KEYENTITY_PROMOTION_EDIT,
  ],
  PROMOTION_BONUSCOIN: [
    PERMISSIONS_NAMES.PROMOTION_BONUSCOIN,
    PERMISSIONS_NAMES.KEYENTITY_PROMOTION_BONUSCOIN,
  ],
  PROMOTION_REBATE: [
    PERMISSIONS_NAMES.PROMOTION_REBATE,
    PERMISSIONS_NAMES.KEYENTITY_PROMOTION_REBATE,
  ],
  PLAYER_PROMOTION: [
    PERMISSIONS_NAMES.KEYENTITY_PLAYER_PROMOTION,
    PERMISSIONS_NAMES.PLAYER_PROMOTION,
  ],
  ROLE: [
    PERMISSIONS_NAMES.KEYENTITY_ROLE_VIEW,
    PERMISSIONS_NAMES.KEYENTITY_ROLE_EDIT,
    PERMISSIONS_NAMES.KEYENTITY_ROLE_CREATE,
    PERMISSIONS_NAMES.KEYENTITY_ROLE_DELETE,
  ],
  DOMAIN: [
    PERMISSIONS_NAMES.DOMAIN,
  ],
  DENIED_ALL: [
    PERMISSIONS_NAMES.DENIED_ALL
  ],
  GRANTED_ALL: [
    PERMISSIONS_NAMES.GRANTED_ALL
  ],
  GRANTED_MOCK: [
    PERMISSIONS_NAMES.GRANTED_MOCK
  ],
  TABLEAU_VIEW: [
    PERMISSIONS_NAMES.BI_REPORTS_VIEW,
    PERMISSIONS_NAMES.KEYENTITY_BI_REPORTS_VIEW,
  ],
  RESPONSIBLEGAMING: [
    PERMISSIONS_NAMES.KEYENTITY_RESPONSIBLEGAMING_PLAYER,
    PERMISSIONS_NAMES.KEYENTITY_RESPONSIBLEGAMING_PLAYER_VIEW,
    PERMISSIONS_NAMES.KEYENTITY_RESPONSIBLEGAMING_PLAYER_EDIT,
    PERMISSIONS_NAMES.RESPONSIBLEGAMING_PLAYER,
    PERMISSIONS_NAMES.RESPONSIBLEGAMING_PLAYER_VIEW,
    PERMISSIONS_NAMES.RESPONSIBLEGAMING_PLAYER_EDIT,
  ],
  SRT: [
    PERMISSIONS_NAMES.SRT_CHALLENGE,
    PERMISSIONS_NAMES.SRT_TOURNAMENT,
  ],
  GAME_SERVER: [
    PERMISSIONS_NAMES.GAME_SERVER_VIEW,
    PERMISSIONS_NAMES.GAME_SERVER_CREATE,
    PERMISSIONS_NAMES.GAME_SERVER_EDIT,
    PERMISSIONS_NAMES.GAME_SERVER_REMOVE,
  ],
  ENTITY_SETTINGS: [
    PERMISSIONS_NAMES.ENTITY,
    PERMISSIONS_NAMES.ENTITY_EDIT,
  ],
  GAME_GROUP_VIEW: [
    PERMISSIONS_NAMES.GAME_GROUP,
    PERMISSIONS_NAMES.GAME_GROUP_VIEW,
    PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP,
    PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP_VIEW,
  ],
  GAME_GROUP_CREATE: [
    PERMISSIONS_NAMES.GAME_GROUP,
    PERMISSIONS_NAMES.GAME_GROUP_CREATE,
    PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP,
    PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP_CREATE,
  ],
  GAME_GROUP_EDIT: [
    PERMISSIONS_NAMES.GAME_GROUP,
    PERMISSIONS_NAMES.GAME_GROUP_EDIT,
    PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP,
    PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP_EDIT,
  ],
  GAME_GROUP_DELETE: [
    PERMISSIONS_NAMES.GAME_GROUP,
    PERMISSIONS_NAMES.GAME_GROUP_DELETE,
    PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP,
    PERMISSIONS_NAMES.KEYENTITY_GAME_GROUP_DELETE,
  ],
  GAME_CHANGE_STATE: [
    PERMISSIONS_NAMES.ENTITY_GAME_CHANGE_STATE,
  ],
  GAME_CHANGE_STATE_DISABLED: [
    PERMISSIONS_NAMES.KEYENTITY_GAME,
    PERMISSIONS_NAMES.KEYENTITY_GAME_CHANGE_STATE,
    PERMISSIONS_NAMES.KEYENTITY_GAME_CHANGE_STATE_DISABLED
  ],
  GAME_CHANGE_STATE_ENABLED: [
    PERMISSIONS_NAMES.KEYENTITY_GAME,
    PERMISSIONS_NAMES.KEYENTITY_GAME_CHANGE_STATE,
    PERMISSIONS_NAMES.KEYENTITY_GAME_CHANGE_STATE_ENABLED
  ],
  GAMERTP: [
    PERMISSIONS_NAMES.GAMERTP,
    PERMISSIONS_NAMES.GAMERTP_VIEW,
    PERMISSIONS_NAMES.KEYENTITY_GAMERTP,
    PERMISSIONS_NAMES.KEYENTITY_GAMERTP_VIEW,
  ],
  ENTITY_LABELS: [
    PERMISSIONS_NAMES.KEYENTITY_ENTITYLABELS,
    PERMISSIONS_NAMES.KEYENTITY_ENTITYLABELS_VIEW,
    PERMISSIONS_NAMES.KEYENTITY_ENTITYLABELS_CREATE,
    PERMISSIONS_NAMES.ENTITYLABELS,
    PERMISSIONS_NAMES.ENTITYLABELS_VIEW,
    PERMISSIONS_NAMES.ENTITYLABELS_CREATE,
  ],
  JACKPOT_VIEW: [
    PERMISSIONS_NAMES.JACKPOT,
    PERMISSIONS_NAMES.JACKPOT_INSTANCE,
    PERMISSIONS_NAMES.JACKPOT_INSTANCE_VIEW,
  ],
  FLAT_REPORTS: [
    PERMISSIONS_NAMES.FLAT_REPORTS,
    PERMISSIONS_NAMES.FLAT_REPORTS_VIEW,
    PERMISSIONS_NAMES.KEYENTITY_FLAT_REPORTS,
    PERMISSIONS_NAMES.KEYENTITY_FLAT_REPORTS_VIEW,
  ],
  ENTITY_BULK_ACTIONS: [
    PERMISSIONS_NAMES.KEYENTITY_ENTITYDOMAIN_BULKOPERATION,
    PERMISSIONS_NAMES.KEYENTITY_BULKOPERATION
  ]
});

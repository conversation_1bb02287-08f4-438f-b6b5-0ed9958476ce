import { createServiceFactory, SpectatorService } from '@ngneat/spectator';
import { SwHubAuthService } from './sw-hub-auth.service';
import * as CryptoJS from 'crypto-js';


function jwt( payload: { [key: string]: any } ): string {
  function base64url( source: any ): string {
    let value = CryptoJS.enc.Base64.stringify(source);
    value = value.replace(/=+$/, '');
    value = value.replace(/\+/g, '-');
    value = value.replace(/\//g, '_');
    return value;
  }

  const header = base64url(CryptoJS.enc.Utf8.parse(JSON.stringify({
    alg: 'HS256',
    typ: 'JWT'
  })));
  const data = base64url(CryptoJS.enc.Utf8.parse(JSON.stringify(payload)));
  const token = `${header}.${data}`;
  const signature = base64url(CryptoJS.HmacSHA256(token, ''));
  return `${token}.${signature}`;
}

describe('SwHubAuthService', () => {
  const grantedPermissions = [
    'disable:entity:game-history:balances',
    'disable:keyentity:game-history:balances',
    'keyentity:gamecategory:change-ordering',
    'keyentity:gamecategory:view',
    'user:create'
  ];
  const token = jwt({
    userId: '141491',
    entityId: '126080',
    username: 'SUPERADMIN',
    grantedPermissions,
    exp: Math.floor(Date.now() / 1000) + 60
  });
  const expiredToken = jwt({
    exp: Math.floor(Date.now() / 1000) - 60
  });
  console.log(token);
  console.log(expiredToken);
  let spectator: SpectatorService<SwHubAuthService>;
  const createService = createServiceFactory({
    service: SwHubAuthService
  });

  beforeEach(() => spectator = createService());

  it('should be created', () => {
    expect(spectator.service).toBeTruthy();
  });

  it('isLogged should return false', () => {
    expect(spectator.service.isLogged()).toEqual(false);
  });

  it('isLogged should return true', () => {
    spectator.service.setToken(token);
    expect(spectator.service.isLogged()).toEqual(true);
  });

  it('isLogged should return false from expired token', () => {
    spectator.service.setToken(expiredToken);
    expect(spectator.service.isLogged()).toEqual(false);
  });

  it('tokenGetter should return token', () => {
    spectator.service.setToken(token);
    expect(spectator.service.tokenGetter()()).toEqual(token);
  });

  it('should setToken', () => {
    spectator.service.setToken(token);

    expect(spectator.service.accessToken).toEqual(token);
    expect(spectator.service.username).toEqual('SUPERADMIN');
    expect(spectator.service.isSuperAdmin).toEqual(false);
  });

  it('should not setToken', () => {
    spectator.service.setToken(undefined);

    expect(spectator.service.accessToken).toBeUndefined();
    expect(spectator.service.entityKey).toBeUndefined();
    expect(spectator.service.username).toBeUndefined();
    expect(spectator.service.isSuperAdmin).toEqual(false);
  });

  it('should logout', () => {
    spectator.service.logout();

    expect(spectator.service.accessToken).toBeUndefined();
    expect(spectator.service.entityKey).toBeUndefined();
    expect(spectator.service.username).toBeUndefined();
    expect(spectator.service.isSuperAdmin).toEqual(false);
  });

  it('getGrantedPermissions return grantedPermissions', () => {
    spectator.service.setToken(token);
    expect(spectator.service.getGrantedPermissions()).toEqual(grantedPermissions);
  });

  it('getGrantedPermissions return []', () => {
    expect(spectator.service.getGrantedPermissions()).toEqual([]);
  });

  it('allowedTo should return false', () => {
    expect(spectator.service.allowedTo([])).toEqual(false);
  });

  it('allowedTo should return true', () => {
    spectator.service.setToken(token);
    expect(spectator.service.allowedTo(grantedPermissions)).toEqual(true);
  });

  it('areGranted should return true', () => {
    spectator.service.setToken(token);
    expect(spectator.service.areGranted(['granted:all'])).toEqual(true);
  });

  it('areGranted should return false', () => {
    expect(spectator.service.areGranted(['granted:all'])).toEqual(false);
  });

  it('decode should called from setToken', () => {
    spyOn<any>(spectator.service, 'decode');
    spectator.service.setToken(token);
    expect(spectator.service['decode']).toHaveBeenCalledTimes(1);
  });
});

export class PermissionService {
  private readonly permissionTree: any;

  constructor( readonly permissions: string[] ) {
    this.permissionTree = this.permissions.reduce(( tree, item ) => ({
      ...tree,
      ...this.insertNode(tree, item.split(':'))
    }), {});
  }

  allowedTo( permissions: string[] ): boolean {
    if (permissions.length === 0) {
      return false;
    }
    return permissions.some(permission => this.checkPermission(this.permissionTree, permission.split(':')));
  }

  areGranted( permissions: string[] ): boolean {
    if (permissions.indexOf('granted:all') > -1) {
      return true;
    }
    if (permissions.indexOf('denied:all') > -1) {
      return false;
    }
    const shortPermissions: string[] = [];
    permissions.forEach(permission => {
      const match = permission.match(/(?:entity:|keyentity:)*(\w|-)+/);
      if (Array.isArray(match) && match.length) {
        shortPermissions.push(match[0]);
      }
    });
    if (shortPermissions.some(permission => this.permissions.indexOf(permission) > -1)) {
      return true;
    }
    return permissions.some(permission => this.permissions.indexOf(permission) > -1);
  }

  private checkPermission( tree: any, parts: string[] ): boolean {
    const part = parts.shift();
    const node = part ? tree[part] || {} : {};
    if (!('__self' in node) && parts.length) {
      return this.checkPermission(node, parts);
    }
    return '__self' in node;
  }

  private insertNode( tree: any, parts: string[] ): any {
    const part = parts.shift();
    if (!part) {
      return {};
    }
    if (parts.length) {
      return { ...tree, [part]: this.insertNode(part in tree ? tree[part] : {}, parts) };
    }
    return { ...tree, [part]: { ...tree[part], __self: true } };
  }
}

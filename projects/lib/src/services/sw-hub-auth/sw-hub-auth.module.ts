import { CommonModule } from '@angular/common';
import { ModuleWithProviders, NgModule } from '@angular/core';
import { JWT_OPTIONS, JwtHelperService, JwtInterceptor, JwtModule } from '@auth0/angular-jwt';
import { SwHubAuthService } from './sw-hub-auth.service';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { SwHubAuthGuard } from './sw-hub-auth.guard';
import { SWUI_HUB_AUTH_CONFIG } from './sw-hub-auth.token';

export interface SwHubAuthModuleConfig {
  whitelistedDomains?: Array<string | RegExp>;
  blacklistedRoutes?: Array<string | RegExp>;
}

export function jwtOptionsFactory( config: SwHubAuthModuleConfig, auth: SwHubAuthService ) {
  return {
    tokenGetter: auth.tokenGetter(),
    headerName: 'X-ACCESS-TOKEN',
    authScheme: '',
    whitelistedDomains: config.whitelistedDomains,
    blacklistedRoutes: config.blacklistedRoutes
  };
}

@NgModule({
  imports: [
    CommonModule,
    JwtModule,
  ],
  providers: [
    SwHubAuthService,
  ]
})
export class SwHubAuthModule {

  static forRoot( config: SwHubAuthModuleConfig ): ModuleWithProviders<SwHubAuthModule> {
    return {
      ngModule: SwHubAuthModule,
      providers: [
        SwHubAuthService,
        JwtHelperService,
        SwHubAuthGuard,
        {
          provide: SWUI_HUB_AUTH_CONFIG,
          useValue: config
        },
        {
          provide: HTTP_INTERCEPTORS,
          useClass: JwtInterceptor,
          multi: true
        },
        {
          provide: JWT_OPTIONS,
          useFactory: jwtOptionsFactory,
          deps: [SWUI_HUB_AUTH_CONFIG, SwHubAuthService]
        }
      ]
    };
  }
}

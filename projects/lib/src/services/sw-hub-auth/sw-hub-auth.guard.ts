import { Inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot, UrlTree } from '@angular/router';
import { Observable } from 'rxjs';
import { map, take } from 'rxjs/operators';
import { SwHubMessageModuleConfig } from '../sw-hub-init/sw-hub-init.model';
import { SwHubInitService } from '../sw-hub-init/sw-hub-init.service';
import { SWUI_HUB_MESSAGE_CONFIG } from '../sw-hub-init/sw-hub-init.token';
import { SwHubAuthService } from './sw-hub-auth.service';
import { SwHubConfigService } from '../sw-hub-config/sw-hub-config.service';


@Injectable()
export class SwHubAuthGuard  {

  constructor( private readonly auth: SwHubAuthService,
               private readonly configService: SwHubConfigService,
               private readonly router: Router,
               private readonly hubService: SwHubInitService,
               @Inject(SWUI_HUB_MESSAGE_CONFIG) private readonly config: SwHubMessageModuleConfig
  ) {
  }

  canActivate(): Observable<boolean> | boolean {
    if (this.auth.isLogged()) {
      return true;
    }
    return this.auth.logged.pipe(map(() => true), take(1));
  }

  canActivateChild( { data }: ActivatedRouteSnapshot, { url }: RouterStateSnapshot ): UrlTree | boolean {
    const { permissions, permissionsList, isSuperAdminOnly } = data;

    if (!this.auth.isLogged()) {
      if (this.configService.loginUrl) {
        if (!(this.config?.lastUnknownLocationExceptions || []).includes(url)) {
          this.hubService.sendUnknownUserLocation(url);
        }
        setTimeout(() => {
          location.href = this.configService.loginUrl as string;
        }, 100);
        return false;
      }
      console.error('Login url notfound');
      return this.router.parseUrl('/pages/404');
    }

    if (isSuperAdminOnly && !this.auth.isSuperAdmin) {
      return this.router.parseUrl('/pages/404');
    }

    if (url === '/pages/404') {
      return true;
    }

    if (permissionsList && Array.isArray(permissionsList)) {
      return permissionsList.every(permissionsItem => Array.isArray(permissionsItem) && this.auth.areGranted(permissionsItem));
    }

    if (this.auth.areGranted(Array.isArray(permissions) ? permissions : [])) {
      return true;
    }

    return this.router.parseUrl('/pages/404');
  }
}

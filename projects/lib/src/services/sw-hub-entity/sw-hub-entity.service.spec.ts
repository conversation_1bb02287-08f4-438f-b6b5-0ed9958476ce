import { TestBed } from '@angular/core/testing';
import { CommonModule } from '@angular/common';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { ActivatedRoute } from '@angular/router';
import { SwHubEntityService } from './sw-hub-entity.service';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';


describe('SwHubEntityService', () => {
  let service: SwHubEntityService;
  beforeEach(() => {
    TestBed.configureTestingModule({
    imports: [CommonModule],
    providers: [
        SwHubEntityService,
        {
            provide: ActivatedRoute,
            useValue: {
                snapshot: {
                    queryParams: {}
                }
            }
        },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting()
    ]
});
    service = TestBed.inject(SwHubEntityService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});

import { Inject, Injectable, Optional } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { BehaviorSubject, combineLatest, Observable, of } from 'rxjs';
import { map, take } from 'rxjs/operators';
import { SwHubEntityDataSource } from './sw-hub-entity-data-source';
import { SwHubBriefEntity, SwHubEntityItem, SwHubShortEntity } from './sw-hub-entity.model';

export interface ExtendedEntity extends SwHubEntityItem {
  child: any;
  parentId?: string;
  children: string[];
  level: number;
}

function findEntity( entity: SwHubShortEntity, id: string ): SwHubShortEntity | null {
  if (entity.id === id) {
    return entity;
  }
  if (Array.isArray(entity.child)) {
    let found: SwHubShortEntity | null = null;
    entity.child.every(item => {
      found = findEntity(item, id);
      return !found;
    });
    return found;
  }
  return null;
}

@Injectable()
export class SwHubEntityService {
  readonly brief$: Observable<SwHubBriefEntity | null>;
  readonly items$: Observable<SwHubEntityItem[]>;
  readonly itemSelected$: Observable<SwHubEntityItem | null>;
  readonly entitySelected$: Observable<SwHubShortEntity | null>;

  entities: ExtendedEntity[] = [];
  foundedEntities: ExtendedEntity[] = [];
  entitiesObject: Record<string, any> | undefined;
  expandedEntities = new Map<string, boolean>();

  private readonly id$ = new BehaviorSubject<string | null>(null);
  private readonly top$: Observable<SwHubShortEntity | null>;

  constructor( private readonly activatedRoute: ActivatedRoute,
               @Optional() @Inject(SwHubEntityDataSource) service: SwHubEntityDataSource | null
  ) {
    this.brief$ = service ? service.getBrief() : of(null);
    this.top$ = service ? service.getEntity() : of(null);

    this.items$ = this.top$.pipe(
      map(entity => {
        this.entities = this.convertStructure(entity);

        this.entitiesObject = this.entities.reduce(( res: Record<string, ExtendedEntity>, item ) => {
          res[item.id] = item;

          return res;
        }, {});

        return this.entities;
      })
    );

    this.itemSelected$ = combineLatest([this.id$, this.items$]).pipe(
      map(( [id, items] ) => {
        if (items.length === 0) {
          return null;
        }
        if (!id) {
          return items[0] || null;
        }
        return items.find(item => item.id === id) || items[0] || null;
      })
    );
    this.entitySelected$ = combineLatest([this.top$, this.itemSelected$]).pipe(
      map(( [entity, selected] ) => {
        if (selected && entity) {
          return findEntity(entity, selected.id);
        }
        return null;
      })
    );
  }

  convertStructure( structure: SwHubShortEntity | null ) {
    if (!structure) {
      return [];
    }

    const entities: ExtendedEntity[] = [];
    this.convertItem(structure, entities, 0);

    return entities;
  }

  convertItem( item: SwHubShortEntity, result: any[], level: number, parent?: { id: any; } | undefined ) {
    const children = item.child ? [...item.child] : [];
    const childrenIds = children.map(( { id } ) => id);

    const currItem: ExtendedEntity = { ...item, child: [], level, parentId: parent?.id, children: childrenIds };
    currItem.parentId = parent?.id;
    result.push(currItem);
    if (children) {
      children.forEach(( child: any ) => this.convertItem(child, result, level + 1, currItem));
    }
  }

  useByPath( entityId: string ) {
    this.items$
      .pipe(
        take(1),
        map(items => {
          const { path } = this.activatedRoute.snapshot.queryParams;

          if (!path) {
            return entityId;
          }

          if (!items) {
            return null;
          }

          const entity = items.find(item => item.path === path);

          return entity?.id || null;
        })
      )
      .subscribe(id => {
        this.id$.next(id);
      });
  }

  use( entityId: string, force?: boolean ) {
    this.entitySelected$
      .pipe(take(1))
      .subscribe(entity => {
        if ((!entity && !force) || (force && entity?.id !== entityId)) {
          this.id$.next(entityId);
        }
      });
  }
}

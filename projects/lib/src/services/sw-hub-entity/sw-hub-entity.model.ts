export interface SwHubBriefEntity {
  id?: string;
  path?: string;
  name: string;
  title?: string;
  type: 'entity' | 'brand' | 'merchant';
}

export interface SwHubEntity {
  id: string;
  path?: string;
  name?: string;
  title?: string;
  type?: 'entity' | 'brand' | 'merchant';
}

export interface SwHubShortEntity extends SwHubEntity {
  child?: SwHubShortEntity[];
}

export interface SwHubEntityItem extends SwHubEntity {
  text?: string;
}

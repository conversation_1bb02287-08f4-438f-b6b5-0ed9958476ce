import { createHttpFactory, HttpMethod, SpectatorHttp } from '@ngneat/spectator';
import { HubConfigHubs, HubUrls, SwHubConfigService } from './sw-hub-config.service';
import { PERMISSIONS_NAMES } from '../sw-hub-auth/permissions';

const apiConfig = '/api/config';

describe('SwHubConfigService', () => {
  const createHttp = createHttpFactory(SwHubConfigService);
  const testResponse = {
    bridge: 'test_bridge',
    logo: {
      main: 'test_main_logo',
      symbols: 'test_symbols_logo'
    }
  };

  let spectator: SpectatorHttp<SwHubConfigService>;
  let response: HubUrls;

  afterEach(() => spectator.controller.verify());
  beforeEach(() => spectator = createHttp());

  it('should be created', () => {
    expect(spectator.service).toBeTruthy();
  });

  it('fetch should return empty object when config is empty', async ( done ) => {
    response = {
      bridge: undefined,
      loginUrl: undefined,
      logoutUrl: undefined
    };
    spectator.service.fetch().then(() => done());

    const request = spectator.controller.expectOne(apiConfig, HttpMethod.GET);
    request.flush({});

    expect(spectator.service.bridge).toBeUndefined();
    expect(spectator.service.loginUrl).toBeUndefined();
    expect(spectator.service.logoutUrl).toBeUndefined();
  });

  it('fetch should return object when config is not empty', async ( done ) => {
    response = {
      bridge: 'test_bridge',
      loginUrl: 'test_loginUrl',
      logoutUrl: 'test_logoutUrl'
    };

    spectator.service.fetch().then(() => done());

    const request = spectator.controller.expectOne(apiConfig, HttpMethod.GET);
    request.flush(response);

    expect(spectator.service.bridge).toEqual('test_bridge');
    expect(spectator.service.loginUrl).toEqual('test_loginUrl');
    expect(spectator.service.logoutUrl).toEqual('test_logoutUrl');
  });

  it('fetch should return casino hub', async ( done ) => {
    const responseHubs: HubConfigHubs = {
      casino: {
        url: 'test_url',
        name: 'HUBS.casino',
        cssClass: 'hub-casino',
        permission: PERMISSIONS_NAMES.HUB_CASINO
      }
    };
    response = {
      bridge: 'test_bridge',
      hubs: {
        casino: 'test_url',
        analytics: undefined,
        engagement: undefined,
        studio: undefined
      },
      loginUrl: 'test_loginUrl',
      logoutUrl: 'test_logoutUrl'
    };

    spectator.service.fetch().then(() => done());

    const request = spectator.controller.expectOne(apiConfig, HttpMethod.GET);
    request.flush(response);

    expect(spectator.service.hubs).toEqual(responseHubs);
  });

  it('fetch should return all hubs', async ( done ) => {
    const responseHubs: HubConfigHubs = {
      casino: {
        url: 'test_casino_url',
        name: 'HUBS.casino',
        cssClass: 'hub-casino',
        permission: PERMISSIONS_NAMES.HUB_CASINO
      },
      engagement: {
        url: 'test_engagement_url',
        name: 'HUBS.engagement',
        cssClass: 'hub-engagement',
        permission: PERMISSIONS_NAMES.HUB_ENGAGEMENT
      },
      analytics: {
        url: 'test_analytics_url',
        name: 'HUBS.analytics',
        cssClass: 'hub-analytics',
        permission: PERMISSIONS_NAMES.HUB_ANALYTICS
      },
      studio: {
        url: 'test_studio_url',
        name: 'HUBS.studio',
        cssClass: 'hub-studio',
        permission: PERMISSIONS_NAMES.HUB_STUDIO
      },
    };
    response = {
      bridge: 'test_bridge',
      hubs: {
        casino: 'test_casino_url',
        analytics: 'test_analytics_url',
        engagement: 'test_engagement_url',
        studio: 'test_studio_url'
      },
      loginUrl: 'test_loginUrl',
      logoutUrl: 'test_logoutUrl'
    };

    spectator.service.fetch().then(() => done());

    const request = spectator.controller.expectOne(apiConfig, HttpMethod.GET);
    request.flush(response);

    expect(spectator.service.hubs).toEqual(responseHubs);
  });

  it('fetch should return undefined if catch error', async ( done ) => {
    const errResponse = { status: 400, statusText: 'Bad Request' };

    spectator.service.fetch().then(() => done());

    const request = spectator.controller.expectOne(apiConfig, HttpMethod.GET);
    request.flush(errResponse);

    expect(spectator.service.bridge).toBeUndefined();
    expect(spectator.service.loginUrl).toBeUndefined();
    expect(spectator.service.logoutUrl).toBeUndefined();
  });

  it('fetch should return empty logo when config is empty', async ( done ) => {
    spectator.service.fetch().then(() => done());

    const request = spectator.controller.expectOne(apiConfig, HttpMethod.GET);
    request.flush({});

    expect(spectator.service.logo).toBeUndefined();
  });

  it('fetch should return undefined for non-matching domain', async ( done ) => {
    spectator.service.fetch().then(() => done());

    const request = spectator.controller.expectOne(apiConfig, HttpMethod.GET);
    request.flush(testResponse);

    expect(spectator.service.logo).toEqual(testResponse.logo);
  });
});

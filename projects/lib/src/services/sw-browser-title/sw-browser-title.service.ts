import { Injectable } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { Subject } from 'rxjs';
import { filter, map, takeUntil } from 'rxjs/operators';
import { SwHubConfigService } from '../sw-hub-config/sw-hub-config.service';


@Injectable()
export class SwBrowserTitleService {

  private readonly destroyed$ = new Subject<void>();

  constructor( private hubConfigService: SwHubConfigService,
               private activatedRoute: ActivatedRoute,
               private readonly router: Router,
               private titleService: Title ) {
  }

  setupTitles(hubName?: string, additionalTitle?: string, fixedUrl?: string): void {
    this.router.events.pipe(
      filter(event => {
        if (fixedUrl && event instanceof NavigationEnd) {
          return event?.urlAfterRedirects.includes(fixedUrl);
        }
        return event instanceof NavigationEnd;
      }),
      map(() => {
        let child = this.activatedRoute.firstChild;
        while (child) {
          if (child.firstChild) {
            child = child.firstChild;
          } else if (child.snapshot.data &&    child.snapshot.data['title']) {
            return child.snapshot.data['title'];
          } else {
            return '';
          }
        }
        return '';
      }),
      takeUntil(this.destroyed$)
    ).subscribe((title: string) => {
        const envName = this.hubConfigService.envName?.toLocaleUpperCase();
        const location = this.hubConfigService.locationName?.toLocaleUpperCase();
        const processedHubName = hubName ? `${hubName} | ` : '';
      this.titleService.setTitle(`UBO ${location || ''} ${envName || ''} | ${processedHubName} ${title} ${additionalTitle || ''}`);
    });
  }
}

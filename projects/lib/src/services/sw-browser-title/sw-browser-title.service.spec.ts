import { TestBed } from '@angular/core/testing';

import { SwBrowserTitleService } from './sw-browser-title.service';
import { SwHubConfigService } from '../sw-hub-config/sw-hub-config.service';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

describe('BrowserTitleService', () => {
  let service: SwBrowserTitleService;

  beforeEach(() => {
    TestBed.configureTestingModule({
    imports: [RouterTestingModule],
    providers: [
        SwBrowserTitleService,
        SwHubConfigService,
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting()
    ]
});
    service = TestBed.inject(SwBrowserTitleService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});

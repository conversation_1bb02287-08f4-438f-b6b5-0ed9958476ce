$path: './roboto/';

@font-face {
  font-family: 'Roboto';
  src: url($path + 'Roboto-Bold.woff2') format('woff2'),
  url($path + 'Roboto-Bold.woff') format('woff');
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Roboto';
  src: url($path + 'Roboto-Light.woff2') format('woff2'),
  url($path + 'Roboto-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'Roboto';
  src: url($path + 'Roboto-Regular.woff2') format('woff2'),
  url($path + 'Roboto-Regular.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Roboto';
  src: url($path + 'Roboto-Medium.woff2') format('woff2'),
  url($path + 'Roboto-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
}


$path: './';

@font-face {
  font-family: 'icomoon';
  src:url($path + 'icomoon/icomoon.eot');
  src:url($path + 'icomoon/icomoon.eot') format('embedded-opentype'),
  url($path + 'icomoon/icomoon.woff') format('woff'),
  url($path + 'icomoon/icomoon.ttf') format('truetype'),
  url($path + 'icomoon/icomoon.svg') format('svg');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 400;
  src: url($path + 'material/MaterialIcons-Regular.eot'); /* For IE6-8 */
  src: local('Material Icons'),
  local('MaterialIcons-Regular'),
  url($path + 'material/MaterialIcons-Regular.woff2') format('woff2'),
  url($path + 'material/MaterialIcons-Regular.woff') format('woff'),
  url($path + 'material/MaterialIcons-Regular.svg') format('svg'),
  url($path + 'material/MaterialIcons-Regular.ttf') format('truetype');
}

@font-face {
  font-family: 'Material Icons Outline';
  font-style: normal;
  font-weight: 400;
  src: local('Material Icons Outline'),
  local('MaterialIcons-Outline'),
  url($path + 'material/MaterialIcons-Outline.woff') format('woff'),
  url($path + 'material/MaterialIcons-Outline.svg') format('svg'),
  url($path + 'material/MaterialIcons-Outline.ttf') format('truetype');
}

@font-face {
  font-family: 'sw';
  src: url($path + 'sw-fonts/sw-fonts.eot');
  src: url($path + 'sw-fonts/sw-fonts.eot') format('embedded-opentype'),
  url($path + 'sw-fonts/sw-fonts.ttf') format('truetype'),
  url($path + 'sw-fonts/sw-fonts.woff') format('woff'),
  url($path + 'sw-fonts/sw-fonts.svg') format('svg');
  font-weight: normal;
  font-style: normal;
}

@import 'roboto-local';
@import './_icons.scss';

[class^="icon-sw-"], [class*=" icon-sw-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'sw' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-sw-arrow-back:before {content: "\e907"}
.icon-sw-view-normal:before {content: "\e903"}
.icon-sw-view-full:before {content: "\e905"}
.icon-sw-view-expanded:before {content: "\e906"}
.icon-sw-info:before {content: "\e904"}
.icon-sw-bi:before {content: "\e900"}
.icon-sw-galaxy:before {content: "\e901"}
.icon-sw-pencil-square:before {content: "\e902"}
.icon-sw-filter:before {content: "\e908"}
.icon-sw-money:before {content: "\e909"}
.icon-sw-people:before {content: "\e90a"}
.icon-sw-plus-square:before {content: "\e90e"}
.icon-sw-minus-square:before {content: "\e90d"}

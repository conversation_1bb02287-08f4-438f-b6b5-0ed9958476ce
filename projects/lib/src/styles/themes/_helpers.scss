@mixin attr-x($attr, $attr-count: 10, $attr-steps: 10, $unit: '%') {
  $attr-list: null;
  @for $i from 1 through $attr-count {
    $attr-value: $attr-steps * $i;

    .#{$attr}#{$attr-value} {
      #{$attr}: #{$attr-value}#{$unit};
    }

    $attr-list: append($attr-list, unquote(".#{$attr}-#{$attr-value}"), comma);
  }

  #{$attr-list} {
    //append style to all classes
  }
}
// USE: @include attr-x('padding', 20, 4, 'px');
// USE: add class .padding16

@mixin table-sticky($height) {
  .table-sticky {
    height: $height;

    &__header {
      display: flex;
      align-items: center;
      height: 56px;
      padding: 0 24px;
      border-bottom: 1px solid rgba(0, 0, 0, .12);
      box-sizing: border-box;
    }

    &__body {
      height: calc(100% - 56px);
      overflow: auto;
    }

    &__search {
      width: calc(100% - 110px);
      max-width: 330px;
      margin-left: auto;

      &--full {
        width: 100%;
        max-width: 100%;
        margin-left: 0;
      }
    }

    table {
      width: 100%;
      background: transparent;

      th {
        background-color: #fff;

        &:first-child {
          border-top-left-radius: 4px;
        }

        &:last-child {
          border-top-right-radius: 4px;
        }
      }
    }
  }
}

@mixin mat-tabs-custom() {
  .mat-tab-header {
    .mat-tab-label {
      min-width: auto;
      padding: 0 20px;
      opacity: 1;
      font-weight: 300;

      &.mat-tab-label-active {
        font-weight: 500;
      }
    }
  }
}

.sw-chip {
  position: relative;
  display: inline-flex !important;
  align-items: center;
  justify-content: center;
  min-height: 26px;
  min-width: 75px;
  height: 1px;
  padding: 7px 10px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1 !important;
  color: #2A2C44;
  white-space: nowrap;
  overflow: hidden;
  background-color: #D9DBDF;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  border-radius: 4px;
  cursor: default;
 // user-select: none;
  &-disabled {
    opacity: 0.26;
  }
  &-green {
    color: #196746 !important;
    background-color: #CFF6CC !important;
  }
  &-green-dark {
    color: #dfffdd !important;
    background-color: #4caf50 !important;
  }
  &-blue {
    color: #1E46A5 !important;
    background-color: #E0EBFF !important;
  }
  &-blue-dark {
    color: #E0EBFF !important;
    background-color: #1468cf !important;
  }
  &-orange {
    color: #B14B00 !important;
    background-color: #FFEFD1 !important;
  }
  &-orange-dark {
    color: #FFEFD1 !important;
    background-color: #f56e0c !important;
  }
  &-red {
    color: #b33027 !important;
    background: #f5b6b1 !important;
  }
  &-red-dark {
    color: #FFE6EA !important;
    background: #D51832!important;
  }
  &-purple {
    color: #9c27b0 !important;
    background: #e7c8ec !important;
  }
  &-brown {
    background: #e0d1cb !important;
    color: #795548 !important;
  }
}

.sw-grid-table {
  th.mat-header-cell {
    &.right {
      text-align: right;
      .mat-sort-header-container {
        justify-content: flex-end;
      }
    }
    &.left {
      text-align: left;
      .mat-sort-header-container {
        justify-content: flex-start;
      }
    }
    &.center {
      text-align: center;
      .mat-sort-header-container {
        justify-content: center;
      }
    }
  }

  td.mat-cell {
    &.right {
      text-align: right;
    }
    &.left {
      text-align: left;
    }
    &.center {
      text-align: center;
    }
  }
}

$mat-button-padding: 0 1.14285714286em !default;
$mat-button-margin: 0 !default;
$mat-button-min-width: 100px !default;
$mat-button-line-height: 44px !default;
$mat-button-border-radius: 4px !default;

$mat-icon-button-size: 44px !default;
$mat-icon-button-border-radius: 50% !default;
$mat-icon-button-line-height: 24px !default;

@mixin mat-button-base {
  margin: $mat-button-margin;
  min-width: $mat-button-min-width;
  line-height: $mat-button-line-height;
  padding: $mat-button-padding;
  border-radius: $mat-button-border-radius;
  font-size: 15px;
  letter-spacing: 0.0892857em;
  //letter-spacing: 0.02857em;
  text-transform: uppercase;
}

@mixin mat-raised-button {
  @include mat-button-base;
}

.mat-drawer-container {
  background-color: #eaedf1;
}

button, a, div, span {
  &.mat-button,
  &.mat-stroked-button,
  &.mat-flat-button {
    &:not(.mat-icon-button) {
      &:not(.mat-dialog-close) {
        .mat-icon {
          position: relative;
          top: -0.05em;
          font-size: 1.42857142857em;
          line-height: 1;
          width: auto;
          height: auto;
          overflow: hidden;
          display: inline-flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }

  &.mat-button,
  &.mat-icon-button,
  &.mat-stroked-button,
  &.mat-flat-button {
    @include mat-button-base;

    &.mat-button-md {
      font-size: 14px;
      line-height: 36px;
    }

    &.mat-button-sm {
      font-size: 13px;
      line-height: 30px;
    }

    &.mat-button-xs {
      font-size: 12px;
      line-height: 24px;
    }

    &.mat-button-initial {
      text-transform: initial;
      letter-spacing: 0;
    }
  }
}


button, a, div, span {
  &.mat-raised-button {
    @include mat-raised-button;
  }
}

button, a, div, span {
  &.mat-icon-button {
    padding: 0;

    // Reset the min-width from the button base.
    min-width: 0;

    width: $mat-icon-button-size;
    height: $mat-icon-button-size;

    flex-shrink: 0;
    line-height: $mat-icon-button-size;
    border-radius: $mat-icon-button-border-radius;

    i, .mat-icon {
      line-height: $mat-icon-button-line-height;
    }
  }
}

//mat-card
$mat-card-padding: 32px !default;
div.mat-card {
  padding: $mat-card-padding;

  &:not([class*=mat-elevation-z]) {
    box-shadow: none;
  }
}

//mat-tab
.mat-tab-header {
  margin-bottom: 20px;
  padding: 0;
  border-bottom: 1px solid rgba(0, 0, 0, .12);

  .mat-tab-label,
  .mat-tab-link {
    padding: 0 20px;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
    letter-spacing: 1.25px;
    text-align: center;
    text-transform: uppercase;
    color: #616161;
    min-width: auto;
    opacity: 1;

    &.mat-tab-label-active {
      color: #1468cf;
    }
  }

  .mat-ink-bar {
    height: 2px;
    color: #1468cf;
  }
}

// Form-field
.no-field-padding {
  &.mat-form-field-appearance-outline {
    .mat-form-field-wrapper {
      padding-bottom: 0;
    }

    .mat-form-field-infix {
      padding: 1em 0 1em 0;
      border: 0;
      height: 44px;
      box-sizing: border-box;
    }
  }

  mat-icon {
    position: relative;
    top: 2px;
  }
}

// Mat-menu
.sw-mat-menu {
  &__item {
    font-size: 14px;
    height: 32px !important;
    line-height: 32px !important;
    color: #5591cd;
    font-weight: 400;

    &.disabled {
      color: rgba(0, 0, 0, 0.38);

      .sw-mat-menu__icon {
        color: rgba(0, 0, 0, 0.38) !important;
      }
    }
  }

  &__icon {
    position: relative;
    top: -1px;
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
    line-height: 16px !important;
    color: #5591cd !important;
    margin-right: 8px !important;
  }
}

.mat-menu-panel {
  overflow: unset !important;
  min-height: auto !important;

  .mat-menu-content {
    position: relative;
    overflow: unset;
    padding: 0 !important;


    &:before {
      position: absolute;
      font-size: 30px;
      line-height: 1;
      color: #fff;
      transform: scaleY(0.5);
    }
  }

  &.mat-menu-before {
    .mat-menu-content {
      &:before {
        right: 7px;
      }
    }
  }

  &.mat-menu-after {
    .mat-menu-content {
      &:before {
        left: 7px;
      }
    }
  }

  &.mat-menu-below {
    margin-top: 12px;

    &.mat-menu-after,
    &.mat-menu-before {
      .mat-menu-content {
        &:before {
          content: '\25b2';
          top: -18px;
          text-shadow: 0 -5px 7px rgba(0, 0, 0, 0.06);
        }
      }
    }
  }

  &.mat-menu-above {
    margin-bottom: 12px;

    &.mat-menu-after,
    &.mat-menu-before {
      .mat-menu-content {
        &:before {
          content: '\25bc';
          bottom: -18px;
          text-shadow: 0 5px 7px rgba(0, 0, 0, 0.06);
        }
      }
    }
  }
}

.entity-picker-menu {
  box-shadow: none !important;
  background: transparent !important;

  .mat-menu-content {
    &:before {
      color: #F6F6F6
    }
  }
}

// sw-multiselect
.swui-multiselect-menu,
.swui-date-picker-menu {
  &.mat-menu-before {
    margin-right: -12px;
  }

  &.mat-menu-after {
    margin-left: -12px;
  }

  &.mat-menu-above {
    margin-bottom: 30px;
  }

  &.mat-menu-below {
    margin-top: 24px;
  }
}

.swui-multiselect-menu {
  width: 204px;
}


// sw-grid
$color-grid-data: #2A2C44;
.sw-grid {
  &__table {
    .mat-header-cell {
      white-space: nowrap;

      &:last-child {
        .mat-sort-header-container {
          padding-right: 8px;
          border-top-right-radius: 4px;
        }
      }

      &:first-child {
        .mat-sort-header-container {
          padding-left: 24px;
          border-top-left-radius: 4px;
        }
      }

      &.left {
        .mat-sort-header-container {
          justify-content: flex-start;
        }
      }

      &.right {
        .mat-sort-header-container {
          justify-content: flex-end;
        }
      }

      &.center {
        .mat-sort-header-container {
          justify-content: center;
        }
      }

      .mat-sort-header-container {
        height: 100%;
        background: #fff;
        padding: 0 8px;
      }

      .mat-sort-header-button {
        white-space: nowrap;
      }

      .mat-sort-header-arrow {
        display: flex;
        align-items: center;
        justify-content: center;

        &:after {
          content: '';
          display: block;
          width: 0;
          height: 0;
          border-style: solid;
          border-width: 0 5px 7px 5px;
          border-color: transparent transparent $color-grid-data transparent;
        }

        .mat-sort-header-indicator,
        .mat-sort-header-stem {
          display: none;
        }
      }

      &.start-from-desc {
        .mat-sort-header-arrow {
          &:after {
            border-width: 7px 5px 0 5px;
            border-color: $color-grid-data transparent transparent transparent;
          }
        }
      }

      &.start-from-asc {
        .mat-sort-header-arrow {
          &:after {
            border-width: 0 5px 7px 5px;
            border-color: transparent transparent $color-grid-data transparent;
          }
        }
      }

      &[aria-sort="descending"] {
        .mat-sort-header-arrow {
          &:after {
            border-width: 7px 5px 0 5px;
            border-color: $color-grid-data transparent transparent transparent;
          }
        }
      }

      &[aria-sort="ascending"] {
        .mat-sort-header-arrow {
          &:after {
            border-width: 0 5px 7px 5px;
            border-color: transparent transparent $color-grid-data transparent;
          }
        }
      }
    }
  }

  &__pagination {
    .mat-paginator-container {
      padding: 0;
      justify-content: flex-start;

      .mat-icon-button {
        color: #969FB3;

        &[disabled="true"] {
          opacity: 0.26;
        }
      }
    }
  }

  &__actions {
    button {
      height: 30px;
      line-height: 30px;
      min-width: auto;

      &:not(.mat-icon-button) {
        height: 30px;
        font-size: 12px;
        padding: 0 8px;
      }

      &.mat-icon-button {
        width: 30px;
        color: #939DB1;

        &:not([disabled]) {
          &:hover {
            color: $color-grid-data;
            transition: all 0.15s ease-in-out;
          }
        }

        &[disabled] {
          color: rgba(#939DB1, 0.26) !important;
        }
      }
    }

    input {
      font-size: 14px;
      line-height: 1;
      color: $color-grid-data;
      border: 1px solid rgba(0, 0, 0, 0.12);
      border-radius: 4px;
      background-color: transparent;
      outline: none;
      padding: 0 8px;
    }
  }
}

.sw-bulk {
  &__menu {
    margin-right: -7px;
  }
}

// Tooltip

.mat-tooltip {
  font-size: 13px;
  background: #2a2c44;
}

// Mat chip
.mat-chip {
  white-space: nowrap;
}

// Cdk overlay

.cdk-overlay-dark-backdrop {
  background: rgba(0, 0, 0, 0.72) !important;
}

.side-filter-form-field {
  .mat-form-field-label {
    overflow: visible;
  }
}

// Date range
.swui-date-range {
  max-width: initial !important;

  .mat-tab-label {
    padding: 0 8px !important;
    margin-right: 8px;
    height: 32px;
    font-size: 12px !important;
  }

  .mat-tab-header {
    padding: 0;
  }

  &.mat-menu-below {
    margin-top: 19px;
  }

  &.mat-menu-above {
    margin-bottom: 19px;
  }

  &.mat-menu-after {
    margin-left: -11px;
  }

  &.mat-menu-before {
    margin-right: -11px;
  }
}


// Top filter

.top-filter {
  section {
    margin: 0 16px 16px 0;

  }

  .mat-form-field {
    &.mat-form-field-appearance-outline {
      &.mat-form-field-can-float {
        &.mat-form-field-should-float {
          .mat-form-field-label {
            transform: translateY(-0.98em) scale(0.75);
          }
        }

        .mat-input-server:focus + .mat-form-field-label-wrapper {
          .mat-form-field-label {
            transform: translateY(-0.98em) scale(0.75);
          }
        }
      }

      .mat-form-field-wrapper {
        padding-bottom: 0;
        margin: 0;
      }

      .mat-form-field-infix {
        padding: 0.9em 0 0.6em 0;
        border: 0;
        height: 34px;
        box-sizing: border-box;
      }

      .mat-select-arrow-wrapper {
        transform: translateY(0);
      }

      .mat-form-field-label-wrapper {
        top: -0.1em;
        padding-top: 0.1em;
      }

      .mat-form-field-label {
        margin-top: -0.4em;
      }

      .mat-form-field-outline {
        border-radius: 4px;
        background: #fff;

        .mat-form-field-outline-gap,
        .mat-form-field-outline-start,
        .mat-form-field-outline-end {
          border: none !important;
        }
      }
    }

    .clear-button {
      width: 20px !important;
      height: 20px !important;
      font-size: 10px !important;
      position: relative;
      top: -3px;

      mat-icon {
        position: relative;
        top: 1px;
      }
    }
  }

  lib-control-input {
    margin: 8px 8px 0 0;
    min-width: 225px;

    .sw-boolean {
      height: 30px;
      display: flex;
      align-items: center;
      padding-bottom: 0;
    }
  }

  .control-items {
    display: inline-flex;
    flex-wrap: wrap;

    .swui-select {
      &:after {
        top: 4px;
      }
    }
  }

  lib-input-date-range {
    .time-range {
      height: 16px;
      font-size: 10px;
      letter-spacing: -0.03em;
    }
  }
}

.control-items {
  .mat-form-field {
    width: 100%;
  }
}

.swui-select {
  &__menu {
    max-width: 100% !important;
    min-width: 300px !important;
  }
}

.widget-list-menu {
  max-width: unset !important;
}

@use '@angular/material/core/m2' as mat;
@use '@angular/material/core/core' as core;
@use '@angular/material/core/theming/all-theme' as all-theme;

// Include the common styles for Angular Material. We include this here so that you only
// have to load a single css file for Angular Material in your app.
@include core.core();
@import 'global';

// Define the palettes for your theme using the Material Design palettes available in palette.scss
$primary: mat.define-palette(mat.$green-palette);
$accent: mat.define-palette(mat.$pink-palette, A200, A100, A400);
$warn: mat.define-palette(mat.$red-palette);

// Create the theme object. A theme consists of configurations for individual
// theming systems such as "color" or "typography".
$theme: mat.define-light-theme((
  color: (
    primary: $primary,
    accent: $accent,
    warn: $warn,
  ),
  typography: mat.define-typography-config(),
  density: 0,
));

// Include theme styles for core and each component used in your app.
@include all-theme.all-component-themes($theme);

// Custom theme styles
.hub-theme {
  .color-primary {
    color: mat.get-color-from-palette($primary);
  }
}

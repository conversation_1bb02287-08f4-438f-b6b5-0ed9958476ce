import type { Meta, StoryObj } from '@storybook/angular';

// This file demonstrates global theme configuration
// The CSS resources addon is deprecated in Storybook v9
// Global styles should be configured in .storybook/preview.js instead

const meta: Meta = {
  title: 'Styles/Themes',
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Global theme configuration for the component library.',
      },
    },
  },
};

export default meta;
type Story = StoryObj;

export const ThemeDemo: Story = {
  render: () => ({
    template: `
      <div style="padding: 20px;">
        <h2>Theme Configuration</h2>
        <p>Global styles and themes are configured in .storybook/preview.js</p>
        <p>This replaces the deprecated CSS resources addon.</p>
      </div>
    `,
  }),
};

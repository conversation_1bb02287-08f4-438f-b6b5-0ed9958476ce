import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';

import { DocsComponent } from './docs.component';
import { DocsTypographyComponent } from './docs-typography/docs-typography.component';
import { DocsAdditionalColorsComponent } from './docs-additional-colors/docs-additional-colors.component';
import { DocsChipComponent } from './docs-chip/docs-chip.component';
import { DocsIconsComponent } from './docs-icons/docs-icons.component';
import { DocsButtonsComponent } from './docs-buttons/docs-buttons.component';
import { MatButtonModule } from '@angular/material/button';




@NgModule({
  declarations: [
    DocsComponent,
    DocsTypographyComponent,
    DocsAdditionalColorsComponent,
    DocsChipComponent,
    DocsIconsComponent,
    DocsButtonsComponent,
  ],
  exports: [
    DocsComponent,
    DocsTypographyComponent,
    DocsAdditionalColorsComponent,
    DocsChipComponent,
    DocsIconsComponent,
    DocsButtonsComponent,
  ],
  imports: [
    CommonModule,
    MatListModule,
    MatIconModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
  ]
})
export class DocsModule { }

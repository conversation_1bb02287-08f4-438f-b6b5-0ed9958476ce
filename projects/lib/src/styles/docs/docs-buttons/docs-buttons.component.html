
<div class="docs">
  <mat-card>
    <div class="chips-table">
      <div class="chips-table__column">
        <mat-list>
          <mat-list-item>
            <button mat-button color="primary">
              Default
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-button color="primary">
              <mat-icon>add</mat-icon>
              Default
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-button color="primary">
              Default
              <mat-icon>add</mat-icon>
            </button>
          </mat-list-item>
        </mat-list>
      </div>

      <div class="chips-table__column">
        <mat-list>
          <mat-list-item>
            <button mat-flat-button color="primary">
              Default
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-flat-button color="primary">
              <mat-icon>add</mat-icon>
              Default
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-flat-button color="primary">
              Default
              <mat-icon>add</mat-icon>
            </button>
          </mat-list-item>
        </mat-list>

      </div>

      <div class="chips-table__column">
        <mat-list>
          <mat-list-item>
            <button mat-stroked-button color="primary">
              Default
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-stroked-button color="primary">
              <mat-icon>add</mat-icon>
              Default
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-stroked-button color="primary">
              Default
              <mat-icon>add</mat-icon>
            </button>
          </mat-list-item>
        </mat-list>
      </div>
    </div>

    <div class="chips-table">
      <div class="chips-table__column">
        <mat-list>
          <mat-list-item>
            <button mat-button color="warn" class="mat-button-md">
              mat-button-md
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-button color="warn" class="mat-button-md">
              <mat-icon>add</mat-icon>
              mat-button-md
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-button color="warn" class="mat-button-md">
              mat-button-md
              <mat-icon>add</mat-icon>
            </button>
          </mat-list-item>
        </mat-list>
      </div>

      <div class="chips-table__column">
        <mat-list>
          <mat-list-item>
            <button mat-flat-button color="warn" class="mat-button-md">
              mat-button-md
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-flat-button color="warn" class="mat-button-md">
              <mat-icon>add</mat-icon>
              mat-button-md
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-flat-button color="warn" class="mat-button-md">
              mat-button-md
              <mat-icon>add</mat-icon>
            </button>
          </mat-list-item>
        </mat-list>

      </div>

      <div class="chips-table__column">
        <mat-list>
          <mat-list-item>
            <button mat-stroked-button color="warn" class="mat-button-md">
              mat-button-md
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-stroked-button color="warn" class="mat-button-md">
              <mat-icon>add</mat-icon>
              mat-button-md
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-stroked-button color="warn" class="mat-button-md">
              mat-button-md
              <mat-icon>add</mat-icon>
            </button>
          </mat-list-item>
        </mat-list>
      </div>
    </div>

    <div class="chips-table">
      <div class="chips-table__column">
        <mat-list>
          <mat-list-item>
            <button mat-button color="accent" class="mat-button-sm">
              mat-button-sm
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-button color="accent" class="mat-button-sm">
              <mat-icon>vpn_key</mat-icon>
              mat-button-sm
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-button color="accent" class="mat-button-sm">
              mat-button-sm
              <mat-icon>info</mat-icon>
            </button>
          </mat-list-item>
        </mat-list>
      </div>

      <div class="chips-table__column">
        <mat-list>
          <mat-list-item>
            <button mat-flat-button color="accent" class="mat-button-sm">
              mat-button-sm
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-flat-button color="accent" class="mat-button-sm">
              <mat-icon>add</mat-icon>
              mat-button-sm
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-flat-button color="accent" class="mat-button-sm">
              mat-button-sm
              <mat-icon>add</mat-icon>
            </button>
          </mat-list-item>
        </mat-list>

      </div>

      <div class="chips-table__column">
        <mat-list>
          <mat-list-item>
            <button mat-stroked-button color="accent" class="mat-button-sm">
              mat-button-sm
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-stroked-button color="accent" class="mat-button-sm">
              <mat-icon>add</mat-icon>
              mat-button-sm
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-stroked-button color="accent" class="mat-button-sm">
              mat-button-sm
              <mat-icon>add</mat-icon>
            </button>
          </mat-list-item>
        </mat-list>
      </div>
    </div>

    <div class="chips-table">
      <div class="chips-table__column">
        <mat-list>
          <mat-list-item>
            <button mat-button color="primary" disabled class="mat-button-xs">
              mat-button-xs
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-button color="primary" disabled class="mat-button-xs">
              <mat-icon>add</mat-icon>
              mat-button-xs
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-button color="primary" disabled class="mat-button-xs">
              mat-button-xs
              <mat-icon>add</mat-icon>
            </button>
          </mat-list-item>
        </mat-list>
      </div>

      <div class="chips-table__column">
        <mat-list>
          <mat-list-item>
            <button mat-flat-button color="primary" disabled class="mat-button-xs">
              mat-button-xs
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-flat-button color="primary" disabled class="mat-button-xs">
              <mat-icon>add</mat-icon>
              mat-button-xs
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-flat-button color="primary" disabled class="mat-button-xs">
              mat-button-xs
              <mat-icon>add</mat-icon>
            </button>
          </mat-list-item>
        </mat-list>

      </div>

      <div class="chips-table__column">
        <mat-list>
          <mat-list-item>
            <button mat-stroked-button color="primary" disabled class="mat-button-xs">
              mat-button-xs
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-stroked-button color="primary" disabled class="mat-button-xs">
              <mat-icon>add</mat-icon>
              mat-button-xs
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-stroked-button color="primary" disabled class="mat-button-xs">
              mat-button-xs
              <mat-icon>add</mat-icon>
            </button>
          </mat-list-item>
        </mat-list>
      </div>
    </div>

    <div class="chips-table">
      <div class="chips-table__column">
        <mat-list>
          <mat-list-item>
            <button mat-mini-fab color="primary">
              <mat-icon>add</mat-icon>
            </button>
          </mat-list-item>
          <mat-list-item>
            <button mat-icon-button color="primary">
              <mat-icon>add</mat-icon>
            </button>
          </mat-list-item>
        </mat-list>
      </div>
    </div>
  </mat-card>
</div>






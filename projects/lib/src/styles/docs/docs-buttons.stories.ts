import { moduleMetadata, storiesOf } from '@storybook/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { DocsModule } from './docs.module';
import { DocsButtonsComponent } from './docs-buttons/docs-buttons.component';

storiesOf('DOCS', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        DocsModule,
      ],
    })
  )
  .add('Buttons', () => ({
    component: DocsButtonsComponent,
    props: {
    },
  }));

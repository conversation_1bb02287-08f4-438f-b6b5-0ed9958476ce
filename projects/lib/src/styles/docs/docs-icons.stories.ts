import { moduleMetadata, storiesOf } from '@storybook/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { DocsModule } from './docs.module';
import { DocsIconsComponent } from './docs-icons/docs-icons.component';

storiesOf('DOCS', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        DocsModule,
      ],
    })
  )
  .add('Icons', () => ({
    component: DocsIconsComponent,
    props: {
    },
  }));


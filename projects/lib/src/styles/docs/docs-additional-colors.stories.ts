import { moduleMetadata, storiesOf } from '@storybook/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { DocsModule } from './docs.module';
import { DocsAdditionalColorsComponent } from './docs-additional-colors/docs-additional-colors.component';

storiesOf('DOCS', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        DocsModule,
      ],
    })
  )
  .add('Colors', () => ({
    component: DocsAdditionalColorsComponent,
    props: {
    },
  }));

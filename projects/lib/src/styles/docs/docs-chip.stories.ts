import { moduleMetadata, storiesOf } from '@storybook/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { DocsModule } from './docs.module';
import { DocsChipComponent } from './docs-chip/docs-chip.component';

storiesOf('DOCS', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        DocsModule,
      ],
    })
  )
  .add('Chip', () => ({
    component: DocsChipComponent,
    props: {
    },
  }));

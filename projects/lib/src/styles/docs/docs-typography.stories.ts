import { moduleMetadata, storiesOf } from '@storybook/angular';
import { DocsTypographyComponent } from './docs-typography/docs-typography.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { DocsModule } from './docs.module';

storiesOf('DOCS', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        DocsModule,
      ],
    })
  )
  .add('Typography', () => ({
    component: DocsTypographyComponent,
    props: {
    },
  }));


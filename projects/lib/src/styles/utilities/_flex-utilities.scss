// Flex Layout Utilities
// Replacement for @angular/flex-layout directives

// Layout Direction
.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

// Flex Properties
.flex-1 {
  flex: 1;
}

.flex-auto {
  flex: auto;
}

.flex-none {
  flex: none;
}

// Alignment
.flex-align-start-center {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-align-center-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-align-end-center {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex-align-start-start {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.flex-align-start-end {
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
}

// Responsive Flex (for gt-md breakpoint)
@media (min-width: 960px) {
  .flex-gt-md-50 {
    flex: 0 0 50%;
    max-width: 50%;
  }
}

// Default flex for smaller screens
.flex-100 {
  flex: 0 0 100%;
  max-width: 100%;
}

// Combined utilities for common patterns
.flex-row-start-center {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}

.flex-column-full {
  display: flex;
  flex-direction: column;
  flex: 0 0 100%;
  max-width: 100%;
}

// Responsive column layout
@media (min-width: 960px) {
  .flex-column-gt-md-50 {
    display: flex;
    flex-direction: column;
    flex: 0 0 50%;
    max-width: 50%;
  }
}

import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { NO_ERRORS_SCHEMA } from '@angular/core';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

import { SwuiMenuComponent } from './swui-menu.component';
import { MAT_MODULES } from './swui-menu.module';


describe('MenuComponent', () => {
  let component: SwuiMenuComponent;
  let fixture: ComponentFixture<SwuiMenuComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        ...MAT_MODULES,
        TranslateModule.forRoot(),
        RouterModule.forRoot([])
      ],
      declarations: [SwuiMenuComponent],
      schemas: [NO_ERRORS_SCHEMA],
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiMenuComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

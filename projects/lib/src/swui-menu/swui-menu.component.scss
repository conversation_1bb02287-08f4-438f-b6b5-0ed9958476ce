$active-color: var(--active-color);
$dropdown-bg: #353653;

.sw-main-menu {
  list-style: none;
  padding: 0;
  margin: 0;
  background: transparent;

  &__link {
    display: flex;
    align-items: center;
    width: 100%;
    height: 50px;
    padding: 0 16px;
    box-sizing: border-box;
    text-align: left;
    text-transform: capitalize;
    text-decoration: none;
    color: #333;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.1em;
    cursor: pointer;
    &--active {
      color: $active-color;
    }
    &.parent-active {
      color: $active-color;
    }
    &:hover {
      transition: color 0.15s ease-in-out;
      color: $active-color;
    }
  }
  &__inner {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0 24px 0 38px;
    box-sizing: border-box;
  }
  &__icon {
    position: absolute;
    left: 0;
    top: 50%;
    margin-top: -10px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    font-size: 20px;
  }
  &__chevron {
    position: absolute;
    right: 0;
    top: 50%;
    height: 20px;
    width: 20px;
    margin-top: -10px;
    font-size: 20px;
    opacity: 0;
    transition: none;
  }
  &__text {
    display: block;
    flex-shrink: 0;
    width: 150px;
    letter-spacing: 0.02em;
    overflow: hidden;
  }
  &__dropdown {
    padding: 0 0 8px;
    background-color: rgba(51, 51, 51, 0.2);
    overflow: hidden;
    &--collapsed{
      padding: 0;
      .mat-tree-node {
        height: 0 !important;
        overflow: hidden;
      }
    }
    .mat-tree-node {
      height: 36px;
      min-height: auto;
      transition: height 0.3s ease;
    }
    .sw-main-menu {
      &__link {
        height: 36px;
        padding: 0;
        font-size: 13px;
        line-height: 1.125em;
      }
      &__icon {
        display: none;
      }
      &__inner {
        padding-left: 54px;
      }
      &__node {
        border-bottom: none;
      }
    }
  }
  &__node {
    display: block;
    &:not(.node-expanded) {
      border-bottom: 1px solid rgba(0, 0, 0, .54);
    }
    &:hover {
      .sw-main-menu {
        &__chevron {
          opacity: 1;
          transition: opacity 0.15s ease-in-out;
        }
      }
    }
    &.node-expanded {
      background-color: rgba(0, 0, 0, .12);
      .sw-main-menu {
        &__chevron {
          opacity: 1;
        }
      }
    }
    &.node-active {
      &>.sw-main-menu {
        &__link {
          color: $active-color;
        }
      }
    }
  }
}



:host-context(.sw-sidebar) {
  .sw-main-menu {
    &__link {
      color: #b8bbc5;
      &.parent-active {
        color: $active-color ;
      }
      &--active {
        color: $active-color ;
      }
      &:hover {
        color: $active-color ;
      }
    }
    &__dropdown {
      background-color: $dropdown-bg;
    }
    &__node {
      border-bottom-color: #393d54;
      &.node-expanded {
        background-color: $dropdown-bg;
        &>.sw-main-menu {
          &__link {
            color: $active-color;
          }
        }
      }
      &.node-active {
        &>.sw-main-menu {
          &__link {
            color: $active-color;
          }
        }
      }
    }
  }
}



@mixin nth-children($points...) {
  @each $point in $points {
    &:nth-child(#{$point}) {
      @content;
    }
  }
}

.sw-loading {
  width: 100%;
  padding: 32px 16px;
  &__item {
    height: 20px;
    max-width: 100%;
    margin-bottom: 12px;
    background: #000;
    animation: pulse 1s infinite ease-in-out;
    @include nth-children(1, 5, 9) {
      width: 150px;
    }
    @include nth-children(2, 6, 10) {
      width: 120px;
    }
    @include nth-children(3, 7) {
      width: 180px;
    }
    @include nth-children(4, 8) {
      width: 90px;
    }
  }
}

@keyframes pulse {
  0%   { background-color: rgba(165, 165, 165, 0.1); }
  50%  { background-color: rgba(165, 165, 165, 0.3); }
  100% { background-color: rgba(165, 165, 165, 0.1); }
}


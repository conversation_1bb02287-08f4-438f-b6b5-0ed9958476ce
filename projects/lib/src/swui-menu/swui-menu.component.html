<div class="sw-main-menu-wrapper" #menu>
  <ng-container *ngIf="!loading; else loadingMenu">
    <mat-tree
      [dataSource]="dataSource"
      [treeControl]="treeControl"
      class="sw-main-menu">

      <!-- leafs -->
      <mat-tree-node *matTreeNodeDef="let item" class="sw-main-menu__node">
        <a
          mat-ripple
          [routerLink]="[item.url]"
          (click)="onItemClick(item)"
          routerLinkActive="sw-main-menu__link--active"
          class="sw-main-menu__link">

        <span class="sw-main-menu__inner">
          <mat-icon
            *ngIf="item.icon || item.fontIcon || item.fontSet || item.svgIcon"
            [fontIcon]="item.fontIcon" [svgIcon]="item.svgIcon" [fontSet]="item.fontSet"
            class="sw-main-menu__icon">
            {{ item.icon }}
          </mat-icon>
          <span class="sw-main-menu__text">{{ item.title | translate }}</span>
        </span>

        </a>
      </mat-tree-node>

      <!-- expandable -->
      <mat-nested-tree-node
        *matTreeNodeDef="let item; when: hasChild"
        class="sw-main-menu__node sw-main-menu__node--nested"
        [ngClass]="{
        'node-active': item === currentParent && (!isSidebarCollapsed || isSidebarHovered),
        'node-expanded': treeControl.isExpanded(item)
      }">

        <a
          mat-ripple
          routerLinkActive="sw-main-menu__link--active"
          class="sw-main-menu__link"
          (click)="onParentClick(item)"
          [ngClass]="{ 'parent-active': item === currentParent && isSidebarCollapsed && !isSidebarHovered }"
          [attr.aria-label]="'toggle ' + item.title | translate">
        <span class="sw-main-menu__inner">
          <mat-icon
            *ngIf="item.icon || item.fontIcon || item.fontSet || item.svgIcon"
            [fontIcon]="item.fontIcon" [svgIcon]="item.svgIcon" [fontSet]="item.fontSet" class="sw-main-menu__icon">
            {{ item.icon }}
          </mat-icon>
          <span class="sw-main-menu__text">{{ item.title | translate }}</span>
          <mat-icon class="sw-main-menu__chevron">
            {{treeControl.isExpanded(item) ? 'expand_more' : 'chevron_right'}}
          </mat-icon>
        </span>

        </a>
        <div class="sw-main-menu__dropdown"
             [ngClass]="{'sw-main-menu__dropdown--collapsed': !treeControl.isExpanded(item)}">
          <ng-container matTreeNodeOutlet></ng-container>
        </div>
      </mat-nested-tree-node>
    </mat-tree>
  </ng-container>

  <ng-template #loadingMenu>
    <div class="sw-loading">
      <div class="sw-loading__item" *ngFor="let item of loadingArray(10)"></div>
    </div>
  </ng-template>

</div>

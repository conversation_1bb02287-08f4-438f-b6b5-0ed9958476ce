import { moduleMetadata, storiesOf } from '@storybook/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { APP_BASE_HREF } from '@angular/common';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

import { SwuiMenuModule } from './swui-menu.module';
import { SwuiMenuComponent } from './swui-menu.component';


export const MENU = [
  {
    title: 'Business management',
    icon: 'insert_emoticon',
    url: '/parent1',
    children: [
      {
        title: 'Business structure',
        icon: '',
        url: '/parent1/child1',
      },
      {
        title: 'Entity bulk actions',
        icon: '',
        url: '/parent1/child2',
      },
      {
        title: 'Menu child 3',
        icon: '',
        url: '/parent1/child3',
      }
    ]
  },
  {
    title: 'No children',
    icon: 'account_balance',
    url: '/parent2',
  },
  {
    title: 'Huge amount of childs',
    icon: 'face',
    url: '/parent3',
    children: [
      {
        title: 'Menu child 4',
        icon: 'icon-home4',
        url: '/parent3/child1',
      },
      {
        title: 'Menu child 4',
        icon: 'icon-home4',
        url: '/parent3/child2',
      },
      {
        title: 'Menu child 4',
        icon: 'icon-home4',
        url: '/parent3/child3',
      },
      {
        title: 'Menu child 4',
        icon: 'icon-home4',
        url: '/parent3/child4',
      },
      {
        title: 'Menu child 4',
        icon: 'icon-home4',
        url: '/parent3/child5',
      },
      {
        title: 'Menu child 4',
        icon: 'icon-home4',
        url: '/parent3/child6',
      },
      {
        title: 'Menu child 4',
        icon: 'icon-home4',
        url: '/parent3/child7',
      },
    ]
  },
  {
    title: 'One child',
    icon: 'extension',
    url: '/parent4',
    children: [
      {
        title: 'Business structure',
        icon: '',
        url: '/parent4/child1',
      },
    ]
  },
  {
    title: 'Atatata',
    icon: 'https',
    url: '/parent5',
    children: [
      {
        title: 'Business structure',
        icon: '',
        url: '/parent5/child1',
      },
      {
        title: 'Entity bulk actions',
        icon: '',
        url: '/parent5/child2',
      },
      {
        title: 'Menu child 3',
        icon: '',
        url: '/child3',
      }
    ]
  },
  {
    title: 'Settings',
    icon: 'settings',
    url: '/parent6',
    children: [
      {
        title: 'Business structure',
        icon: '',
        url: '/parent6/child1',
      },
      {
        title: 'Entity bulk actions',
        icon: '',
        url: '/parent6/child2',
      },
      {
        title: 'Menu child 3',
        icon: '',
        url: '/parent6/child3',
      }
    ]
  },
];

storiesOf('Sidebar menu/menu', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        SwuiMenuModule,
        TranslateModule.forRoot(),
        RouterModule.forRoot([
          {
            path: 'iframe.html',
            redirectTo: '',
            pathMatch: 'full'
          },
          {
            path: '',
            children: [
              {
                path: 'parent1',
                component: SwuiMenuComponent,
                children: [
                  {
                    path: 'child1',
                    component: SwuiMenuComponent,
                  },
                  {
                    path: 'child2',
                    component: SwuiMenuComponent,
                  },
                  {
                    path: 'child3',
                    component: SwuiMenuComponent,
                  }
                ]
              },
              {
                path: 'parent2',
                component: SwuiMenuComponent,
              },
              {
                path: 'parent3',
                component: SwuiMenuComponent,
                children: [
                  {
                    path: 'child1',
                    component: SwuiMenuComponent,
                  },
                  {
                    path: 'child2',
                    component: SwuiMenuComponent,
                  },
                  {
                    path: 'child3',
                    component: SwuiMenuComponent,
                  },
                  {
                    path: 'child4',
                    component: SwuiMenuComponent,
                  },
                  {
                    path: 'child5',
                    component: SwuiMenuComponent,
                  },
                  {
                    path: 'child6',
                    component: SwuiMenuComponent,
                  },
                  {
                    path: 'child7',
                    component: SwuiMenuComponent,
                  },
                ]
              },
              {
                path: 'parent4',
                component: SwuiMenuComponent,
                children: [
                  {
                    path: 'child1',
                    component: SwuiMenuComponent,
                  },
                ]
              },
              {
                path: 'parent5',
                component: SwuiMenuComponent,
                children: [
                  {
                    path: 'child1',
                    component: SwuiMenuComponent,
                  },
                  {
                    path: 'child2',
                    component: SwuiMenuComponent,
                  },
                  {
                    path: 'child3',
                    component: SwuiMenuComponent,
                  }
                ]
              },
              {
                path: 'parent6',
                component: SwuiMenuComponent,
                children: [
                  {
                    path: 'child1',
                    component: SwuiMenuComponent,
                  },
                  {
                    path: 'child2',
                    component: SwuiMenuComponent,
                  },
                  {
                    path: 'child3',
                    component: SwuiMenuComponent,
                  }
                ]
              },
            ]
          }
        ])
      ],
      providers: [
        { provide: APP_BASE_HREF, useValue : '/' },
      ]
    })
  )
  .add('default', () => ({
    component: SwuiMenuComponent,
    props: {
      items: MENU,
    }
  }))
  .add('custom color', () => ({
    component: SwuiMenuComponent,
    props: {
      items: MENU,
      activeColor: '#6499db'
    }
  }))
  .add('loading', () => ({
    component: SwuiMenuComponent,
  }));

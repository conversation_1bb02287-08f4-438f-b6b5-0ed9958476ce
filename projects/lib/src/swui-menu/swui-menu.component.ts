import { Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { NestedTreeControl } from '@angular/cdk/tree';
import { MatTreeNestedDataSource } from '@angular/material/tree';
import { Subject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';

import { SwuiMenuItem } from './swui-menu.interface';


@Component({
    selector: 'lib-swui-menu',
    templateUrl: './swui-menu.component.html',
    styleUrls: ['./swui-menu.component.scss'],
    standalone: false
})
export class SwuiMenuComponent implements OnInit, OnDestroy {
  @Input() activeColor?: string;

  @Input()
  get isSidebarCollapsed(): boolean {
    return this._isSidebarCollapsed;
  }

  set isSidebarCollapsed( value: boolean ) {
    this._isSidebarCollapsed = value;
    if (!value && this.currentParent) {
      this.treeControl.expand(this.currentParent);
    } else {
      this.collapseAll();
    }
  }

  @Input()
  get isSidebarHovered(): boolean {
    return this._isSidebarHovered;
  }

  set isSidebarHovered( value: boolean ) {
    this._isSidebarHovered = value;
    if (this.isSidebarCollapsed && !value) {
      this.collapseAll();
    } else if (this.currentParent) {
      this.treeControl.expand(this.currentParent);
    }
  }

  @Input()
  set items( items: SwuiMenuItem[] ) {
    if (!items) {
      return;
    }
    this.loading = false;
    this.dataSource.data = items;
    this.expandParentItem();
  }

  loading = true;
  loadingArray = Array;
  treeControl = new NestedTreeControl<SwuiMenuItem>(item => item.children);
  dataSource = new MatTreeNestedDataSource<SwuiMenuItem>();
  currentParent: SwuiMenuItem | undefined;

  @ViewChild('menu', { static: true }) menuRef: ElementRef | undefined;

  private _isSidebarCollapsed = false;
  private _isSidebarHovered = false;
  private destroy$ = new Subject();

  constructor( private router: Router ) {
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        takeUntil(this.destroy$),
      ).subscribe(( event: any ) => {
      const item = this.dataSource.data.find(( { url } ) => event.url.indexOf(url) !== -1);

      if (item && this.currentParent !== item) {
        if (item.children?.length){
          this.onParentClick(item);
        } else {
          this.onItemClick(item);
        }

        this.expandParentItem();
      }
    });
  }

  ngOnInit(): void {
    if (this.menuRef) {
      this.menuRef.nativeElement.style.setProperty('--active-color', this.activeColor || '#ffffff');
    }
  }

  ngOnDestroy(): void {
    if (this.menuRef) {
      this.menuRef.nativeElement.style.removeProperty('--active-color');
    }

    this.destroy$.next(undefined);
    this.destroy$.complete();
  }

  hasChild( _: number, item: SwuiMenuItem ) {
    return !!item.children && item.children.length > 0;
  }

  collapseAll() {
    this.treeControl.collapseAll();
  }

  onItemClick( item: SwuiMenuItem ) {
    this.currentParent = this.getParentMenuItem(item.url);
    this.collapseOther(this.currentParent);
  }

  onParentClick( item: SwuiMenuItem ) {
    this.treeControl.expand(item);
    this.collapseOther(item);
  }

  private collapseOther( item: SwuiMenuItem | undefined ) {
    this.dataSource.data.forEach(( el: SwuiMenuItem ) => {
      if ((el !== item) && el.children) {
        this.treeControl.collapse(el);
      }
    });
  }

  private getParentMenuItem( url: string ): SwuiMenuItem | undefined {
    return this.dataSource.data.find(( item: SwuiMenuItem ) => {
      if (item.children && item.children.length > 0) {
        return item.children.some(( childItem: SwuiMenuItem ) => url.includes(childItem.url));
      } else {
        return item.url === url;
      }
    });
  }

  private expandParentItem() {
    this.currentParent = this.getParentMenuItem(this.router.url);
    if (this.currentParent && !this.isSidebarCollapsed) {
      this.treeControl.expand(this.currentParent);
    }
  }

}

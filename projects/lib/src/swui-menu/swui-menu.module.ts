import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

import { SwuiMenuComponent } from './swui-menu.component';
import { MatMenuModule } from '@angular/material/menu';
import { MatTreeModule } from '@angular/material/tree';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatRippleModule } from '@angular/material/core';


export const MAT_MODULES = [
  MatListModule,
  MatMenuModule,
  MatTreeModule,
  MatIconModule,
  MatRippleModule,
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    RouterModule,
    ...MAT_MODULES,
  ],
  declarations: [SwuiMenuComponent],
  exports: [SwuiMenuComponent]
})
export class SwuiMenuModule {
}

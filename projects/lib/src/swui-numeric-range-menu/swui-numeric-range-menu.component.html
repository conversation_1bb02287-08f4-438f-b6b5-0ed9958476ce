<input
  #input
  matInput
  #range="matMenuTrigger"
  type="text"
  [formControl]="valueControl"
  readonly="readonly"
  [matMenuTriggerFor]="menu">

<mat-menu #menu="matMenu" class="swui-numeric-range">
  <div class="swui-numeric-range__wrapper" (click)="prevent($event)">
    <div class="swui-numeric-range__header">
      <div class="swui-numeric-range__title">{{title | translate}}</div>
      <div class="swui-numeric-range__clear" (click)="clear($event)">
        {{'COMPONENTS.DATE_RANGE.clear' | translate}}
      </div>
    </div>
    <div class="swui-numeric-range__body">
      <div *ngIf="requiredField" style="color: #f44336">{{ (requiredField | translate) + ' field is required' }}</div>
      <mat-form-field appearance="outline">
        <mat-label for="from">From</mat-label>
        <input id="from" matInput type="number" placeholder="From" [formControl]="fromControl">
        <button mat-button *ngIf="fromControl.value" matSuffix mat-icon-button (click)="fromControl.reset('')">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label for="to">To</mat-label>
        <input id="to" matInput type="number" placeholder="To" [formControl]="toControl">
        <button mat-button *ngIf="toControl.value" matSuffix mat-icon-button (click)="toControl.reset('')">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>
    </div>
    <div class="swui-numeric-range__footer">
      <div class="swui-numeric-range__actions">
        <button
          mat-flat-button
          (click)="cancel($event)"
          class="swui-numeric-range__button swui-numeric-range__button--cancel">
          {{'COMPONENTS.DATE_RANGE.cancel' | translate}}
        </button>
        <button
          mat-flat-button
          color="primary"
          (click)="apply($event)"
          class="swui-numeric-range__button">
          {{'COMPONENTS.DATE_RANGE.apply' | translate}}
        </button>
      </div>
    </div>
  </div>


</mat-menu>

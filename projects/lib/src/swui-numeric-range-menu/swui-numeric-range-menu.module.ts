import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import {
  FormsModule,
  ReactiveFormsModule
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';
import { NumericRangeMenuComponent } from './swui-numeric-range-menu.component';

@NgModule({
  imports: [
    CommonModule,
    MatMenuModule,
    FormsModule,
    ReactiveFormsModule,
    MatInputModule,
    MatFormFieldModule,
    TranslateModule,
    MatIconModule,
    MatButtonModule
  ],
  exports: [NumericRangeMenuComponent],
  declarations: [NumericRangeMenuComponent],
  providers: [],
})
export class SwuiNumericRangeMenuModule {
}

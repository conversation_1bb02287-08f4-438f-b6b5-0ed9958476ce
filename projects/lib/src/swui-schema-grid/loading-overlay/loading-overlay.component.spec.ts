import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SwuiGridLoadingOverlayComponent } from './loading-overlay.component';

describe('SwuiGridLoadingOverlayComponent', () => {
  let component: SwuiGridLoadingOverlayComponent;
  let fixture: ComponentFixture<SwuiGridLoadingOverlayComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [SwuiGridLoadingOverlayComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiGridLoadingOverlayComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

import { Injectable } from '@angular/core';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { ActivatedRoute, Params, QueryParamsHandling, Router } from '@angular/router';
import { SwuiTopFilterDataService } from '../swui-schema-top-filter/top-filter-data.service';

@Injectable()
export class SwuiGridUrlHandlerService {

  allowQueryParamsUpdate = true;

  pageSortQueryParams: {
    limit: string,
    offset: string,
    sortBy: string,
    sortOrder: string,
  } = {
    limit: 'limit',
    offset: 'offset',
    sortBy: 'sortBy',
    sortOrder: 'sortOrder',
  };

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
  ) {
  }

  setAllowQueryParamsUpdate( allow: boolean ): void {
    this.allowQueryParamsUpdate = allow;
  }

  setParams( queryParams: Params, queryParamsHandling: QueryParamsHandling = '' ) {
    if (this.allowQueryParamsUpdate) {
      this.router.navigate([],
        {
          relativeTo: this.activatedRoute,
          queryParams,
          queryParamsHandling,
          preserveFragment: true,
        }
      );
    }
  }

  getParams(): Params {
    return this.activatedRoute.snapshot.queryParams;
  }

  fetchPageSize( initial: number = 0 ): number {
    const params = this.getParams();
    let fetched = initial;

    if (params && 'limit' in params) {
      fetched = parseInt(params['limit'], 10);
    }

    return fetched;
  }

  setParamsToPaginator( paginator: MatPaginator ) {
    const params = this.getParams();
    if (Object.keys(params).length) {
      if ('offset' in params) {
        paginator.pageIndex = parseInt(params[this.pageSortQueryParams.offset], 10) / paginator.pageSize;
      }
    }
  }

  setParamsToSort( sort: MatSort ) {
    const params = this.getParams();
    if (Object.keys(params).length) {
      if ('sortOrder' in params) {
        sort.direction = params[this.pageSortQueryParams.sortOrder].toLowerCase();
      }
      if ('sortBy' in params) {
        sort.active = params[this.pageSortQueryParams.sortBy];
      }
    }
  }

  getFilterQueryParams(): { [params: string]: any } {
    const params = this.getParams();
    const ignoredParams = Object.keys(this.pageSortQueryParams);
    return Object.keys(params)
      .filter(( key ) => ignoredParams.indexOf(key) === -1)
      .reduce(( data: { [key: string]: any }, key: string ) => {
        data[key] = params[key];
        return data;
      }, {});
  }
}

@Injectable()
export class MockUrlHandler {

  // @ts-ignore
  setAllowQueryParamsUpdate( allow: boolean ) {
  }

  // @ts-ignore
  setParams( params: Params ) {
  }

  // @ts-ignore
  fetchPageSize( initial: number, override: boolean ): number {
    return initial;
  }

  // @ts-ignore
  setParamsToPaginator( paginator: MatPaginator ): void {

  }

  // @ts-ignore
  setParamsToFilter( filter: SwuiTopFilterDataService ) {
  }

  // @ts-ignore
  setParamsToSort( sort: MatSort ) {
  }


}

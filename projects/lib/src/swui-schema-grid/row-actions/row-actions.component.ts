import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';

export class RowAction {
  title: string;
  icon?: string;
  svgIcon?: string;
  fontSet?: string;
  fontIcon?: string;
  inMenu?: boolean;
  showOnHover?: boolean;
  fn: (...args: any[]) => any | void;
  canActivateFn: (item?: any) => boolean;
  availableFn?: (item?: any) => boolean;

  constructor(value: any) {
    const config = value || {};
    this.title = config.title || '';
    this.icon = config.icon || undefined;
    this.svgIcon = config.svgIcon || undefined;
    this.fontSet = config.fontSet || undefined;
    this.fontIcon = config.fontIcon || undefined;
    this.inMenu = typeof config.inMenu !== 'undefined' ? config.inMenu : true;
    this.fn = config.fn || (() => true);
    this.canActivateFn = config.canActivateFn || (() => true);
    this.availableFn = config.availableFn || (() => true);
    this.showOnHover = config.showOnHover || false;
  }
}

@Component({
    selector: 'lib-swui-grid-row-actions',
    templateUrl: './row-actions.component.html',
    styleUrls: ['./row-actions.component.scss'],
    standalone: false
})
export class SwuiGridRowActionsComponent implements OnInit, OnChanges {
  @Input()
  set actions(actionsItems: any[]) {
    if (!Array.isArray(actionsItems) || !actionsItems.length) {
      return;
    }
    this._actions = actionsItems.map(actionItem => new RowAction(actionItem));
  }

  get actions(): RowAction[] {
    return this._actions;
  }

  @Input() row: any;
  @Input() menuIcon = 'more_horiz';
  @Input() ignorePlainLink = false;

  standaloneActions: RowAction[] = [];
  menuActions: RowAction[] = [];
  private _actions: RowAction[] = [];

  constructor() {
  }

  ngOnInit(): void {
    this.processActions();
  }

  handleMenuClick(event: MouseEvent, action: RowAction) {
    event.preventDefault();
    if (action.canActivateFn(this.row)) {
      action.fn(this.row);
    }
  }

  ngOnChanges( changes: SimpleChanges ): void {
    if ('actions' in changes && !changes['actions'].isFirstChange()) {
      this.processActions();
    }
  }

  private processActions() {
    this._actions = this._actions
      .filter((action) => {
        let filtered = true;
        if (!!action.availableFn) {
          filtered = action.availableFn(this.row);
        }
        return filtered;
      });

    this.populateActions();
  }

  private populateActions() {
    this.menuActions = this._actions.filter(item => item.inMenu);
    this.standaloneActions = this._actions.filter(item => !item.inMenu);
  }
}

import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SwuiGridRowActionsComponent } from './row-actions.component';
import { TranslateModule } from '@ngx-translate/core';
import { MatMenuModule } from '@angular/material/menu';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';

describe('SwuiGridRowActionsComponent', () => {
  let component: SwuiGridRowActionsComponent;
  let fixture: ComponentFixture<SwuiGridRowActionsComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        TranslateModule.forRoot(),
        MatIconModule,
        MatButtonModule,
        MatMenuModule,
        MatTooltipModule,
      ],
      declarations: [SwuiGridRowActionsComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiGridRowActionsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

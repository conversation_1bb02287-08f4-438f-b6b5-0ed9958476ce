import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridRowActionsComponent } from './row-actions.component';
import { MatMenuModule } from '@angular/material/menu';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';


@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    MatIconModule,
    MatButtonModule,
    MatMenuModule,
    MatTooltipModule,
  ],
  declarations: [
    SwuiGridRowActionsComponent
  ],
  exports: [
    SwuiGridRowActionsComponent
  ]
})
export class SwuiGridRowActionsModule {
}

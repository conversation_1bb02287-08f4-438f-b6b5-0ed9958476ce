<ng-container *ngIf="actions.length > 1 || (actions.length && ignorePlainLink); else plainLink">
  <div class="sw-row-actions">
    <button
      *ngFor="let action of standaloneActions"
      mat-icon-button
      class="sw-row-actions__button"
      [ngClass]="{'show-on-hover': action.showOnHover}"
      [disabled]="!action.canActivateFn(row)"
      (click)="handleMenuClick($event, action)"
      matTooltip="{{ action.title  | translate }}">

      <mat-icon
        *ngIf="action.icon || action.fontSet || action.fontIcon || action.svgIcon"
        [fontSet]="action.fontSet"
        [fontIcon]="action.fontIcon"
        [svgIcon]="action.svgIcon"
        [ngClass]="{'disabled': !action.canActivateFn(row)}">
        {{ action.icon }}
      </mat-icon>
    </button>

    <ng-container *ngIf="menuActions.length">
      <button
        mat-icon-button
        matTooltip="Actions"
        [matMenuTriggerFor]="menu"
        class="sw-row-actions__button">
        <mat-icon>{{ menuIcon }}</mat-icon>
      </button>

      <mat-menu #menu="matMenu" xPosition="before" class="sw-mat-menu">
        <button
          *ngFor="let action of menuActions"
          class="sw-row-actions__menu-item sw-mat-menu__item"
          mat-menu-item
          [ngClass]="{'disabled': !action.canActivateFn(row)}"
          [disabled]="!action.canActivateFn(row)"
          (click)="handleMenuClick($event, action)">

          <mat-icon
            *ngIf="action.icon || action.fontSet || action.fontIcon || action.svgIcon"
            class="sw-row-actions__menu-icon sw-mat-menu__icon"
            [fontSet]="action.fontSet"
            [fontIcon]="action.fontIcon"
            [svgIcon]="action.svgIcon">

            {{ action.icon }}
          </mat-icon>
          <span>{{ action.title | translate }}</span>
        </button>
      </mat-menu>
    </ng-container>
  </div>

</ng-container>

<ng-template #plainLink>
  <button
    *ngFor="let action of actions"
    mat-button
    class="sw-row-actions__button"
    [disabled]="!action.canActivateFn(row)"
    (click)="handleMenuClick($event, action)">

    <mat-icon
      *ngIf="action.icon || action.fontSet || action.fontIcon || action.svgIcon"
      [fontSet]="action.fontSet"
      [fontIcon]="action.fontIcon"
      [svgIcon]="action.svgIcon">

      {{ action.icon }}
    </mat-icon>
    <span>{{ action.title | translate }}</span>
  </button>
</ng-template>

import { InjectionToken } from '@angular/core';
import { getDefaultWidgetsList } from './registry/default-list';
import { WidgetListItem } from './registry/registry';

export type WidgetAddType = 'EXPAND' | 'REPLACE';
export const WidgetAddTypes: { EXPAND: WidgetAddType, REPLACE: WidgetAddType, } = {
  EXPAND: 'EXPAND',
  REPLACE: 'REPLACE'
};

export const GRID_CONFIG = new InjectionToken<SwuiGridConfig>('SWUI_GRID_CONFIG');

export interface SwuiGridConfig {
  widgets?: WidgetListItem[];
  widgetAddType?: WidgetAddType;
}
export interface SwuiValidGridConfig extends SwuiGridConfig {
  widgets: WidgetListItem[];
  widgetAddType: WidgetAddType;
}

export function getValidGridConfig( config?: SwuiGridConfig ): SwuiValidGridConfig {
  let configWidgets;
  let widgetAddType = WidgetAddTypes.EXPAND;
  let widgets = getDefaultWidgetsList();

  if (config) {
    if (config.widgets) {
      configWidgets = config.widgets;
    }
    if (config.widgetAddType) {
      widgetAddType = config.widgetAddType;
    }
  }

  if (configWidgets) {
    if (widgetAddType === WidgetAddTypes.REPLACE) {
      widgets = configWidgets;
    }
    if (widgetAddType === WidgetAddTypes.EXPAND) {
      // needs to perform override if it's required
      const customTypes = configWidgets.map((item) => item.type);
      widgets = widgets.filter((item) => customTypes.indexOf(item.type) === -1);

      widgets = [...widgets, ...configWidgets];
    }
  }

  return { widgets, widgetAddType };
}

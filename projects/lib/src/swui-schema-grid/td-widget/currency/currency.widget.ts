import { Component, Inject, OnDestroy, Optional } from '@angular/core';
import { SettingsService } from '../../../services/settings/settings.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';

export interface SwuiCurrencyTdWidgetSchema {
  td?: {
    delimiter?: string;
    fractionCount?: number;
    nowrap?: boolean;
  };
}

@Component({
    selector: 'lib-swui-td-currency-widget',
    templateUrl: './currency.widget.html',
    styles: ['.no-wrap {white-space: nowrap}'],
    standalone: false
})
export class SwuiTdCurrencyWidgetComponent implements OnDestroy {
  delimiter?: string;
  fractionCount?: number;
  currencyLocale: string = window.navigator.language;
  value: string | number;
  nowrap = false;

  private readonly destroyed$ = new Subject<void>();

  constructor(
    @Inject(SWUI_GRID_WIDGET_CONFIG) { value, schema }: SwuiGridWidgetConfig<SwuiCurrencyTdWidgetSchema>,
    @Optional() private settings: SettingsService,
  ) {
    this.value = value;
    this.delimiter = schema.td?.delimiter;
    this.fractionCount = schema.td?.fractionCount;
    this.nowrap = schema.td && schema.td.nowrap || false;
    this.subscribeForSettings();
  }

  ngOnDestroy(): void {
    this.destroyed$.next(undefined);
    this.destroyed$.complete();
  }

  private subscribeForSettings() {
    if (this.settings) {
      this.settings.appSettings$
        .pipe(takeUntil(this.destroyed$))
        .subscribe(( { currencyFormat } ) => {
          this.currencyLocale = currencyFormat;
        });
    }
  }
}

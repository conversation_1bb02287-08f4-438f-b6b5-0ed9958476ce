<ng-container *ngIf="!readonly; else readonlyState">
   <span class="sw-chip" [ngClass]="getStatusColor(currentStatus)" [matMenuTriggerFor]="statusMenu">
    <mat-progress-bar mode="buffer" *ngIf="loading"></mat-progress-bar>
    <ng-container *ngIf="!loading">
      <ng-container *ngIf="useTranslate; else noTranslate">
        {{ getStatusName(currentStatus) | translate }}
      </ng-container>
      <ng-template #noTranslate>
        {{ getStatusName(currentStatus) }}
      </ng-template>
      <mat-icon>keyboard_arrow_down</mat-icon>
    </ng-container>
  </span>

  <mat-menu #statusMenu="matMenu" xPosition="before" class="sw-mat-menu">
    <button
      mat-menu-item
      *ngFor="let status of availableStatuses"
      class="sw-mat-menu__item"
      (click)="statusClick($event, status)">
      <ng-container *ngIf="useTranslate; else statusNotTranslated">
        {{ status.displayName | translate }}
      </ng-container>
      <ng-template #statusNotTranslated>
        {{ status.displayName }}
      </ng-template>
    </button>
  </mat-menu>
</ng-container>

<ng-template #readonlyState>
  <span class="sw-chip readonly" [ngClass]="getStatusColor(currentStatus)">
    <ng-container *ngIf="useTranslate; else noTranslate">
      {{ getStatusName(currentStatus) | translate }}
    </ng-container>
    <ng-template #noTranslate>
      {{ getStatusName(currentStatus) }}
    </ng-template>
  </span>
</ng-template>

<ul class="flex-list flex-list--nowrap no-margin-bottom jp-list">
  <li *ngFor="let jackpot of jackpots">
    <span class="label" [ngClass]="getRandomClassByValue(jackpot.jackpotType)">{{ jackpot.jackpotId }}</span>
  </li>
  <li *ngIf="jackpots.length" class="jp-list__editable no-margin-right">
    <a class="text-default" (click)="manageClicked($event)">
      <i class="icon-pencil3 text-size-small"></i></a>
  </li>
</ul>

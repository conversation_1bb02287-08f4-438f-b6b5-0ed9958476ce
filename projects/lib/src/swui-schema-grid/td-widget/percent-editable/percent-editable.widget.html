<div class="percent-widget">
  <ng-template #defaultTpl>
    <ng-container *ngIf="!loading">
      <div class="percent-widget__trigger">
        <div class="percent-widget__value sw-chip sw-chip-blue" *ngIf="percentValue !== undefined && mode === 'view'">
          {{ percentValue | formattedNumber : fractionCount : delimiter }}%
        </div>
        <div class="percent-widget__edit" (click)="onEditClick($event)">
          <mat-icon>edit</mat-icon>
        </div>
      </div>

      <mat-icon class="percent-widget__warning" *ngIf="showUndefinedWarning && !value && value !== 0">warning</mat-icon>

      <div *ngIf="mode === 'edit'" class="percent-widget__area">
        <button mat-icon-button (click)="valueDown()">
          <mat-icon>remove</mat-icon>
        </button>

        <input
          #input
          class="percent-widget__input"
          [value]="percentValue"
          (change)="valueChanged()"
          (keyup.enter)="onKeyPress()"/>

        <button mat-icon-button (click)="valueUp()">
          <mat-icon>add</mat-icon>
        </button>
        <button mat-icon-button (click)="saveChanges()">
          <mat-icon>done</mat-icon>
        </button>
      </div>
    </ng-container>

  </ng-template>

  <ng-container *ngIf="loading; else defaultTpl">
    <div class="percent-widget__loading">
      <mat-progress-bar mode="indeterminate"></mat-progress-bar>
    </div>
  </ng-container>

</div>

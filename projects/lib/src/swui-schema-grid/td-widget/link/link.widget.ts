import { Component, Inject } from '@angular/core';
import { Truncate } from '../../pipes/truncate.interface';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';

export interface SwuiLinkTdWidgetSchema {
  td?: {
    titleFn?: ( row: any, schema: SwuiLinkTdWidgetSchema ) => string;
    isDisabled?: ( row: any, schema: SwuiLinkTdWidgetSchema ) => boolean;
    titlePostfixFn?: ( row: any, schema: SwuiLinkTdWidgetSchema ) => string;
    linkFn?: ( row: any, schema: SwuiLinkTdWidgetSchema ) => string[];
    truncate?: Truncate;
    useTranslate?: boolean;
  };
}

@Component({
    selector: 'lib-swui-td-link-widget',
    templateUrl: './link.widget.html',
    styleUrls: ['link.widget.scss'],
    standalone: false
})
export class SwuiTdLinkWidgetComponent {
  readonly title: string;
  readonly titlePostfix: string;
  readonly routerLinkData: string[];
  readonly useTranslate: boolean;
  readonly truncate?: Truncate;
  readonly disabled?: boolean;

  constructor( @Inject(SWUI_GRID_WIDGET_CONFIG) { row, schema, value }: SwuiGridWidgetConfig<SwuiLinkTdWidgetSchema> ) {
    const titleFn = schema.td?.titleFn;
    const isDisabled = schema.td?.isDisabled;
    const titlePostfixFn = schema.td?.titlePostfixFn;
    const linkFn = schema.td?.linkFn;

    if (schema.td && 'truncate' in schema.td) {
      this.truncate = schema.td.truncate;
    }

    this.useTranslate = schema.td && 'useTranslate' in schema.td ? schema.td?.useTranslate || false : true;

    this.title = (titleFn && titleFn(row, schema)) || value;
    this.disabled = (isDisabled && isDisabled(row, schema)) || false;
    this.titlePostfix = (titlePostfixFn && titlePostfixFn(row, schema)) || '';
    this.routerLinkData = linkFn && linkFn(row, schema) || [];
  }
}

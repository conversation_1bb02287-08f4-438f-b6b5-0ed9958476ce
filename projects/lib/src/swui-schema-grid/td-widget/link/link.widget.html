<ng-container *ngIf="useTranslate; else noTranslate">
  <a href="#" class="widget widget-link" [class.widget-link__disabled]="disabled" [routerLink]="routerLinkData" title="{{title | translate}}">
    <ng-container *ngIf="truncate; else noTruncate">
      {{ title || '--' | translate | truncate: truncate}}
    </ng-container>
    <ng-template #noTruncate>
      {{ title || '--' | translate }}
    </ng-template>
  </a>
  <span *ngIf="titlePostfix">{{ titlePostfix }}</span>
</ng-container>

<ng-template #noTranslate>
  <a href="#" class="widget widget-link" [class.widget-link__disabled]="disabled" [routerLink]="routerLinkData" title="{{title}}">
    <ng-container *ngIf="truncate; else noTranslateNoTruncate">
      {{ title || '--' | truncate: truncate }}
    </ng-container>
    <ng-template #noTranslateNoTruncate>
      {{ title || '--' }}
    </ng-template>
  </a>
  <span *ngIf="titlePostfix">{{ titlePostfix }}</span>
</ng-template>

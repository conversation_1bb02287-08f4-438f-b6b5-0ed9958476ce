import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { RouterModule } from '@angular/router';

import { SwuiTdBooleanWidgetComponent } from './boolean/boolean.widget';
import { SwuiTdClickWidgetComponent } from './click/click.widget';
import { SwuiTdColorfulLabelsWidgetComponent } from './colorful-labels/colorful-labels.widget';
import { SwuiTdGameLabelsWidgetComponent } from './game-labels/game-labels.widget';
import { SwuiTdGamesLabelsWidgetComponent } from './games-labels/games-labels.widget';
import { SwuiTdJackpotWidgetComponent } from './jackpot/jackpot.widget';
import { SwuiTdNumberWidgetComponent } from './number/number.widget';
import { SwuiTdStringWidgetComponent } from './string/string.widget';
import { SwuiTdUserWidgetComponent } from './user/user.widget';
import { SwuiTdPercentWidgetComponent } from './percent/percent.widget';
import { SwuiTdTimestampWidgetComponent } from './timestamp/timestamp.widget';
import { SwuiTdCalcWidgetComponent } from './calc/calc.widget';
import { SwuiTdCalcAsyncWidgetComponent } from './calc-async/calc-async.widget';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatIconModule } from '@angular/material/icon';
import { SwuiTdIconPopoverWidgetComponent } from './icon-popover/icon-popover.widget';
import { SwuiTdCurrencyWidgetComponent } from './currency/currency.widget';
import { SwuiTdLinkWidgetComponent } from './link/link.widget';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { SwuiTdIconWidgetComponent } from './icon/icon.widget';
import { GridPipesModule } from '../pipes/grid-pipes.module';
import { SwuiTdPercentEditableWidgetComponent } from './percent-editable/percent-editable.widget';
import { SwuiTdStatusWidgetComponent } from './status/status.widget';
import { SwuiTdInactivityWidgetComponent } from './inactivity/inactivity.widget';
import { SwuiTdListWidgetComponent } from './list/list.widget';
import { SwuiTdImageWidgetComponent } from './image/image.widget';

export const matModules = [
  MatTooltipModule,
  MatIconModule,
  MatMenuModule,
  MatButtonModule,
  MatProgressBarModule,
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    RouterModule,
    GridPipesModule,
    ...matModules
  ],
  declarations: [
    SwuiTdBooleanWidgetComponent,
    SwuiTdColorfulLabelsWidgetComponent,
    SwuiTdGameLabelsWidgetComponent,
    SwuiTdGamesLabelsWidgetComponent,
    SwuiTdJackpotWidgetComponent,
    SwuiTdNumberWidgetComponent,
    SwuiTdStringWidgetComponent,
    SwuiTdUserWidgetComponent,
    SwuiTdPercentWidgetComponent,
    SwuiTdPercentEditableWidgetComponent,
    SwuiTdTimestampWidgetComponent,
    SwuiTdCalcWidgetComponent,
    SwuiTdCalcAsyncWidgetComponent,
    SwuiTdIconPopoverWidgetComponent,
    SwuiTdCurrencyWidgetComponent,
    SwuiTdStatusWidgetComponent,
    SwuiTdLinkWidgetComponent,
    SwuiTdIconWidgetComponent,
    SwuiTdInactivityWidgetComponent,
    SwuiTdListWidgetComponent,
    SwuiTdImageWidgetComponent,
    SwuiTdClickWidgetComponent
  ],

})
export class SwuiTdWidgetsModule {
}

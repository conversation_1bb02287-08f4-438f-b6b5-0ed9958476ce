<ng-container *ngIf="value && value.length > 1; else singleVal">
  <div class="widget-list-label">
    {{value[0]}}
    {{ ('COMPONENTS.WIDGET.andMore' | translate: {number: (value.length - 1)}) }}
  </div>
  <button
    mat-icon-button
    [matMenuTriggerFor]="menu">
    <mat-icon>arrow_drop_down</mat-icon>
  </button>
</ng-container>
<ng-template #singleVal>
  <span class="widget-list-label">{{value[0]}}</span>
</ng-template>

<mat-menu class="widget-list-menu" #menu="matMenu">
  <div class="widget-list-menu__inner" *ngIf="value && value.length" (click)="$event.stopPropagation();">
    <div
      class="widget-list-menu__item"
      mat-menu-item
      *ngFor="let id of value">
      {{ id | translate }}
    </div>
  </div>
</mat-menu>

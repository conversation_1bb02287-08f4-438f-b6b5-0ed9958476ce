import { Component, Inject } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';

export interface SwuiIconPopoverTdWidgetSchema {
  td?: {
    titleFn?: ( row: any, field: string ) => string;
    classFn?: ( row: any, field: string ) => any;
    sanitizeValue?: boolean;
  };
}

@Component({
    selector: 'lib-swui-td-icon-popover-widget',
    templateUrl: './icon-popover.widget.html',
    standalone: false
})
export class SwuiTdIconPopoverWidgetComponent {
  popoverText: any;
  classObj: any;
  value: string;

  constructor(
    @Inject(SWUI_GRID_WIDGET_CONFIG) { field, schema, row, value }: SwuiGridWidgetConfig<SwuiIconPopoverTdWidgetSchema>,
    sanitizer: DomSanitizer
  ) {
    const titleFn = schema.td?.titleFn;
    const classFn = schema.td?.classFn;

    this.value = value;
    this.popoverText = titleFn && titleFn(row, field);
    this.classObj = classFn && classFn(row, field);

    if (schema.td?.sanitizeValue) {
      this.popoverText = sanitizer.bypassSecurityTrustHtml(this.popoverText);
    }
  }

}

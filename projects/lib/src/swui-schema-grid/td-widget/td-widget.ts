import { SwuiBooleanTdWidgetSchema } from './boolean/boolean.widget';
import { SwuiCalcTdWidgetSchema } from './calc/calc.widget';
import { SwuiAsyncCalcTdWidgetSchema } from './calc-async/calc-async.widget';
import { SwuiClickTdWidgetSchema } from './click/click.widget';
import { SwuiCurrencyTdWidgetSchema } from './currency/currency.widget';
import { SwuiGameLabelsTdWidgetSchema } from './game-labels/game-labels.widget';
import { SwuiIconTdWidgetSchema } from './icon/icon.widget';
import { SwuiIconPopoverTdWidgetSchema } from './icon-popover/icon-popover.widget';
import { SwuiImageTdWidgetSchema } from './image/image.widget';
import { SwuiInactivityTdWidgetSchema } from './inactivity/inactivity.widget';
import { SwuiLinkTdWidgetSchema } from './link/link.widget';
import { SwuiListTdWidgetSchema } from './list/list.widget';
import { SwuiPercentWidgetSchema } from './percent/percent.widget';
import { SwuiStatusTdWidgetParamsSchema } from './status/status.widget';
import { SwuiStringTdWidgetSchema } from './string/string.widget';
import { SwuiTimestampTdWidgetSchema } from './timestamp/timestamp.widget';
import { SwuiGamesLabelsTdWidgetSchema } from './games-labels/games-labels.widget';
import { SwuiPercentEditableWidgetSchema } from './percent-editable/percent-editable.widget';

export interface WidgetActionEvent {
  field: any;
  row: any;
  payload: any;
}

export type SwuiTdWidgetSchema =
  SwuiBooleanTdWidgetSchema
  | SwuiCalcTdWidgetSchema
  | SwuiAsyncCalcTdWidgetSchema
  | SwuiClickTdWidgetSchema
  | SwuiCurrencyTdWidgetSchema
  | SwuiGameLabelsTdWidgetSchema
  | SwuiGamesLabelsTdWidgetSchema
  | SwuiIconTdWidgetSchema
  | SwuiIconPopoverTdWidgetSchema
  | SwuiImageTdWidgetSchema
  | SwuiInactivityTdWidgetSchema
  | SwuiLinkTdWidgetSchema
  | SwuiListTdWidgetSchema
  | SwuiPercentWidgetSchema
  | SwuiPercentEditableWidgetSchema
  | SwuiStatusTdWidgetParamsSchema
  | SwuiStringTdWidgetSchema
  | SwuiTimestampTdWidgetSchema;

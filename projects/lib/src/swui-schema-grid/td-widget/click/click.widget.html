<ng-container *ngIf="useTranslate; else noTranslate">
  <a href="#"
     class="widget widget-link"
     title="{{title | translate}}"
     [ngClass]="{'disabled': isDisabled}"
     (click)="onClick($event)">
    <ng-container *ngIf="truncate; else noTruncate">
      {{ title || '--' | translate | truncate: truncate}}
    </ng-container>
    <ng-template #noTruncate>
      {{ title || '--' | translate }}
    </ng-template>
  </a>
</ng-container>

<ng-template #noTranslate>
  <a href="#"
     class="widget widget-link"
     title="{{title}}"
     [ngClass]="{'disabled': isDisabled}"
     (click)="onClick($event)">
    <ng-container *ngIf="truncate; else noTranslateNoTruncate">
      {{ title || '--' | truncate: truncate }}
    </ng-container>
    <ng-template #noTranslateNoTruncate>
      {{ title || '--' }}
    </ng-template>
  </a>
</ng-template>

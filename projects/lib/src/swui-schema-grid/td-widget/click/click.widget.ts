import { Component, EventEmitter, Inject } from '@angular/core';
import { Truncate } from '../../pipes/truncate.interface';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';

export interface SwuiClickTdWidgetSchema {
  td?: {
    titleFn?: ( row: any, schema: SwuiClickTdWidgetSchema ) => string;
    clickFn?: ( row: any, schema: SwuiClickTdWidgetSchema ) => string[];
    isDisabled?: ( row: any ) => boolean;
    truncate?: Truncate;
    useTranslate?: boolean;
  };
}

@Component({
    selector: 'lib-swui-td-click-widget',
    templateUrl: './click.widget.html',
    styleUrls: ['./click.widget.scss'],
    standalone: false
})
export class SwuiTdClickWidgetComponent {
  readonly title: string;
  readonly useTranslate: boolean;
  readonly isDisabled: boolean | undefined;
  readonly truncate?: Truncate;
  private readonly action: EventEmitter<any>;

  constructor( @Inject(SWUI_GRID_WIDGET_CONFIG) private config: SwuiGridWidgetConfig<SwuiClickTdWidgetSchema> ) {
    const { row, schema, value, action } = config;
    const titleFn = schema.td?.titleFn;
    const isDisabled = schema.td?.isDisabled;

    if (schema.td && 'truncate' in schema.td) {
      this.truncate = schema.td.truncate;
    }

    this.useTranslate = schema.td && 'useTranslate' in schema.td ? schema.td?.useTranslate || false : true;

    this.title = (titleFn && titleFn(row, schema)) || value;
    this.isDisabled = isDisabled && isDisabled(row);
    this.action = action;
  }

  onClick( event: MouseEvent ) {
    event.preventDefault();
    if (this.action) {
      const { field, row } = this.config;
      this.action.emit({ row, field });
    }
  }
}

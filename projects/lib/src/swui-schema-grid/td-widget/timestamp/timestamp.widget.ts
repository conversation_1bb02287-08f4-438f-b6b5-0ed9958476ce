import { Component, Inject, OnDestroy, OnInit, Optional } from '@angular/core';
import moment from 'moment';
import 'moment-timezone';
import { SettingsService } from '../../../services/settings/settings.service';
import { filter, takeUntil } from 'rxjs/operators';
import { AppSettings } from '../../../services/settings/app-settings';
import { Subject } from 'rxjs';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';


export interface SwuiTimestampTdWidgetSchema {
  td?: {
    noDatePlaceholder?: string;
    showTimeZone?: boolean;
    useTranslate?: boolean;
    dateFormat?: string;
    timeFormat?: string;
    timeZone?: string;
    classFn?: ( row: any, schema: SwuiTimestampTdWidgetSchema ) => string;
    nowrap?: boolean;
    dateOptions?: {
      timeDisableLevel?: string;
    };
  };
}

const FORMAT_DATETIME_SMALL = 'DD/MM/YY HH:mm:ss';

@Component({
    selector: 'lib-swui-td-timestamp-widget',
    templateUrl: './timestamp.widget.html',
    styles: ['.no-wrap {white-space: nowrap}'],
    standalone: false
})
export class SwuiTdTimestampWidgetComponent implements OnInit, OnDestroy {
  value!: string;
  formatted = '';
  noDatePlaceholder = '';
  readonly useTranslate: boolean;
  showTimeZone = false;
  timeDisableLevel?: string;
  classObj: any;
  schema: SwuiTimestampTdWidgetSchema;
  nowrap = false;

  private readonly destroyed$ = new Subject<void>();

  constructor(
    @Inject(SWUI_GRID_WIDGET_CONFIG) { row, field, value: value1, schema }: SwuiGridWidgetConfig<SwuiTimestampTdWidgetSchema>,
    @Optional() private settings: SettingsService,
  ) {
    if ('_meta' in row && field in row._meta) {
      this.value = row._meta[field];
    } else {
      this.value = value1;
    }
    const classFn = schema.td?.classFn;

    this.useTranslate = schema.td?.useTranslate || false;
    this.nowrap = schema.td?.nowrap || false;
    this.classObj = classFn && classFn(row, schema);

    if (schema.td?.noDatePlaceholder) {
      this.noDatePlaceholder = schema.td?.noDatePlaceholder;
    }

    this.showTimeZone = schema.td?.showTimeZone || false;

    if (schema.td && schema.td.dateOptions) {
      this.timeDisableLevel = schema.td.dateOptions.timeDisableLevel;
    }
    this.schema = schema;
  }

  ngOnInit() {
    if (this.settings) {
      this.subscribeForSettings();
    } else {
      this.formatted = this.formatValue(this.schema.td || {});
    }
  }

  ngOnDestroy(): void {
    this.destroyed$.next(undefined);
    this.destroyed$.complete();
  }

  private formatValue( { dateFormat, timeFormat, timeZone }: { dateFormat?: string; timeFormat?: string; timeZone?: string } ): string {
    let format;

    if (dateFormat && timeFormat) {
      timeFormat = this.filterTimeFormat(timeFormat);
      format = `${dateFormat} ${timeFormat}`;
    } else {
      format = this.filterTimeFormat(FORMAT_DATETIME_SMALL);
    }

    let abr = '';
    if (this.showTimeZone && timeFormat && timeZone) {
      const zone = moment.tz.zone(timeZone);
      const offset = moment.tz(this.value, timeZone).utcOffset();
      abr = zone ? ' ' + zone.abbr(offset) : '';
    }

    if (this.value) {
      const date = timeZone ? moment.tz(moment.utc(this.value), timeZone) : moment(this.value);
      return `${date.format(format)}${abr}`;
    }
    return '';
  }

  private filterTimeFormat( format: string ) {
    if (this.timeDisableLevel === 'seconds') {
      format = format.replace(/([\:|\.]ss)/, '');
    }
    return format;
  }

  private subscribeForSettings() {
    this.settings.appSettings$
      .pipe(
        filter(settings => !!settings),
        takeUntil(this.destroyed$),
      )
      .subscribe(( { dateFormat, timeFormat, timezoneName }: AppSettings ) => {
        this.formatted = this.formatValue({
          dateFormat,
          timeFormat,
          timeZone: timezoneName,
        });
      });
  }
}

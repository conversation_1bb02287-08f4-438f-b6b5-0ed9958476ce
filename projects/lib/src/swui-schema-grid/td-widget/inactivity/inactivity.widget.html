<div class="sw-inactivity" [ngClass]="{'active': value}">
  <svg class="sw-inactivity__svg"  viewBox="0 0 17 17" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <circle class="sw-inactivity__circle" cx="8" cy="8" r="7"></circle>
    <g class="sw-inactivity__icon">
      <rect class="sw-inactivity__rect small" width="5" height="2" x="8" y="1.5"></rect>
      <rect class="sw-inactivity__rect long" width="8" height="2" x="-3" y="11"></rect>
    </g>
  </svg>
</div>

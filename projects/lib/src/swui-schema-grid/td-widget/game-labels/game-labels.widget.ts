import { Component, Inject } from '@angular/core';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';

export interface SwuiGameLabelsTdWidgetSchema {
  classFn?: ( row: any, schema: any ) => any;
  td?: {
    group?: string;
  };
}

@Component({
    selector: 'lib-swui-td-game-labels-widget',
    templateUrl: './game-labels.widget.html',
    standalone: false
})
export class SwuiTdGameLabelsWidgetComponent {
  items: { title: string }[] = [];
  classObj: any;

  constructor( @Inject(SWUI_GRID_WIDGET_CONFIG) { schema, row }: SwuiGridWidgetConfig<SwuiGameLabelsTdWidgetSchema> ) {
    const { classFn } = schema;
    this.classObj = classFn && classFn(row, schema);

    if (schema.td && 'group' in schema.td) {
      this.items = row.labels.filter(( { group }: any ) => group === schema.td?.group);
    }
  }
}

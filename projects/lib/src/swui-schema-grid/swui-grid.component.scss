$color-grid-border: #e4e5e9;
$color-table-data: #2a2c44;
.sw-grid {
  display: flex;
  flex-direction: column;
  &.sticky {
    height: 100%
  }

  &__refresh {
    color: #939DB1;
    &:hover {
      color: $color-table-data;
      transition: all 0.15s ease-in-out;
    }
  }

  &__header {
    display: flex;
    align-items: flex-end;
    width: 100%;
  }

  &__amount {
    flex: 1;
    padding: 0 4px 5px 4px;
    font-size: 14px;
    color: $color-table-data;
    white-space: nowrap;
  }

  .sw-grid__body {
    position: relative;
    width: 100%;
    overflow-x: auto;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border: 1px solid $color-grid-border;
    background: #fff;
  }

  &__table {
    width: 100%;
    background: transparent;

    .mat-header-row {
      height: 42px;
    }

    .mat-row {
      height: 45px;
      &:nth-child(odd) {
        background-color: #eef1f5;
      }
      &:nth-child(even) {
        background-color: #f9f9fa;
      }
    }

    .mat-cell {
      font-size: 14px;
      line-height: 1;
      color: $color-table-data;
      border-bottom: none;
      padding: 0 8px;
      &:first-child {
        padding-left: 24px;
      }
      &:last-child {
        padding-right: 24px;
      }
      &.left {
        text-align: left;
      }
      &.right {
        text-align: right;
      }
      &.center {
        text-align: center;
      }
      &.sortable-cell {
        padding-right: 26px;
      }
    }

    .mat-header-cell {
      height: 42px;
      padding: 0;
      border-bottom: none;
      font-size: 16px;
      color: $color-table-data;
      &:last-child {
        .sw-grid__th-inner {
          padding-right: 24px;
          border-top-right-radius: 4px;
        }
      }
      &:first-child {
        .sw-grid__th-inner {
          padding-left: 24px;
          border-top-left-radius: 4px;
        }
      }
      &.left {
        .sw-grid__th-inner {
          justify-content: flex-start;
        }
      }
      &.right {
        .sw-grid__th-inner {
          justify-content: flex-end;
        }
      }
      &.center {
        .sw-grid__th-inner {
          justify-content: center;
        }
      }
    }
  }

  &__th-inner {
    display: flex;
    align-items: center;
    height: 100%;
    width: 100%;
    padding: 0 8px;
    background: #fff;

    &-actions {
      justify-content: center;
    }
  }

  &__actions {
    display: flex;
    padding-bottom: 5px;
  }
  &__default {
    display: flex;
    color: $color-table-data;
    button {
      width: 30px;
      height: 30px;
      line-height: 30px;
    }
  }

  &__pagination {
    background: transparent;

    &.hidden {
      display: none;
    }
  }

  &__empty {
    height: 150px;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
  }

  &__loading {
    width: 100%;
    height: 100%;
    top: 0;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(#fff, 0.5);
  }
}

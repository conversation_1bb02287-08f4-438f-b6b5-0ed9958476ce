<div class="sw-bulk">
  <button
    mat-icon-button
    matTooltip="Bulk actions"
    [matMenuTriggerFor]="menu"
    [disabled]="!rows.length"
    class="sw-bulk__trigger">
    <mat-icon>storage</mat-icon>
  </button>

  <mat-menu #menu="matMenu" xPosition="before" class="sw-bulk__menu">
    <button
      mat-menu-item
      *ngFor="let action of actions"
      class="sw-bulk__item"
      [disabled]="action.isDisabledAction()"
      (click)="prepareAction($event, action)">

      <mat-icon *ngIf="action.icon" class="sw-bulk__icon">{{ action.icon }}</mat-icon>
      <span>{{ action.title | translate }}</span>
    </button>
  </mat-menu>
</div>


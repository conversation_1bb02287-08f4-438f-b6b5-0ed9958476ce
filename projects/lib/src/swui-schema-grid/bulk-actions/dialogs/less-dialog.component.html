<mat-dialog-content>
  <p> {{'COMPONENTS.BULK_ACTIONS.messageLess' | translate: {
    availableRowsNumber: available.length,
    checkedRowsNumber: checked.length
  } }}</p>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-button cdkFocusInitial (click)="declineAction()">
    {{ 'COMPONENTS.BULK_ACTIONS.btnNo' | translate }}
  </button>
  <button mat-button (click)="runAction()">
    {{'COMPONENTS.BULK_ACTIONS.btnYes' | translate}}
  </button>
</mat-dialog-actions>

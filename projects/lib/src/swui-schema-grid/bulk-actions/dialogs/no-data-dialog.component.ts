import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { BulkAction } from '../bulk-actions.model';

export interface NoDataDialogData {
  currentAction: BulkAction;
  declineAction: Function;

}

@Component({
    selector: 'lib-swui-no-data-dialog',
    templateUrl: 'no-data-dialog.component.html',
    standalone: false
})

export class NoDataDialogComponent {
  currentAction: BulkAction;
  declineAction: Function;

  constructor(
    public dialog: MatDialog,
    @Inject(MAT_DIALOG_DATA) data: NoDataDialogData,
  ) {
    this.currentAction = data.currentAction;
    this.declineAction = data.declineAction;
  }

}

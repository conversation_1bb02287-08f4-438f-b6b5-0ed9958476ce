import { ComponentType } from '@angular/cdk/portal';
import { MatDialogConfig } from '@angular/material/dialog';

export type ActionModelFn = ( ...args: any[] ) => any | void;
export type ActionModelCanActivateFn = ( item?: any ) => boolean;
export type IsDisabledActionFn = ( ...args: any[] ) => boolean;

export class ActionModel<T = any, D = any> {
  title: string;
  icon?: string;
  params?: any;

  fn: ActionModelFn;
  dialogFn?: () => void;

  canActivateFn: ActionModelCanActivateFn;
  canActivateMessage?: string;
  isDisabledAction: IsDisabledActionFn;
  isDisabledMessage: string;

  dialog?: {
    type: 'component';
    componentRef: ComponentType<T>;
    config?: MatDialogConfig<D>;
  };

  constructor( config: any ) {
    this.title = config.title || null;
    this.icon = config.icon || '';
    this.fn = config.fn || (() => true);
    this.dialog = config.dialog;
    this.canActivateFn = config.canActivateFn || (() => true);
    this.canActivateMessage = config.canActivateMessage || null;
    this.isDisabledAction = config.isDisabledAction || (() => false);
    this.isDisabledMessage = config.isDisabledMessage || null;
  }
}

export class BulkAction<T = any, D = any> extends ActionModel<T, D> {
}

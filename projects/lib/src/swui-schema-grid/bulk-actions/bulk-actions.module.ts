import { NgModule } from '@angular/core';
import { BulkActionsComponent } from './bulk-actions.component';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ConfirmDialogComponent } from './dialogs/confirm-dialog.component';
import { LessDialogComponent } from './dialogs/less-dialog.component';
import { NoDataDialogComponent } from './dialogs/no-data-dialog.component';

export const matModules = [
  MatMenuModule,
  MatDialogModule,
  MatButtonModule,
  MatIconModule,
  MatTooltipModule,
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule,
    ...matModules,
  ],
  declarations: [
    BulkActionsComponent,
    ConfirmDialogComponent,
    LessDialogComponent,
    NoDataDialogComponent,
  ],
  exports: [
    BulkActionsComponent,
  ]
})
export class SwuiGridBulkActionsModule {
}

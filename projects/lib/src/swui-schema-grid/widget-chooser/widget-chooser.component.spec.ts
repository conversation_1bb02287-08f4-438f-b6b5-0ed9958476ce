import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SwuiGridWidgetChooserComponent } from './widget-chooser.component';

describe('SwuiGridWidgetChooserComponent', () => {
  let component: SwuiGridWidgetChooserComponent;
  let fixture: ComponentFixture<SwuiGridWidgetChooserComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [SwuiGridWidgetChooserComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiGridWidgetChooserComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

import { Component, Inject } from '@angular/core';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';

@Component({
    selector: 'lib-swui-footer-string-widget',
    templateUrl: './string.widget.html',
    standalone: false
})
export class SwuiFooterStringWidgetComponent {
  value: any;

  constructor( @Inject(SWUI_GRID_WIDGET_CONFIG) { value }: SwuiGridWidgetConfig<{}> ) {
    this.value = value;
  }
}

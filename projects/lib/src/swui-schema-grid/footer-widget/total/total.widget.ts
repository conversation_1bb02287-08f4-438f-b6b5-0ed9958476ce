import { Component, Inject, OnDestroy } from '@angular/core';
import { map, takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { SwuiGridDataSource } from '../../swui-grid.datasource';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';

export interface SwuiTotalFooterWidgetSchema {
  footer?: {
    format?: string;
    delimiter?: string;
    fractionCount?: number;
    prefix?: string;
  };
}

@Component({
    selector: 'lib-swui-footer-total-widget',
    templateUrl: 'total.widget.html',
    standalone: false
})
export class SwuiFooterTotalWidgetComponent implements OnDestroy {
  value = 0;

  readonly format?: string;
  readonly delimiter?: string;
  readonly fractionCount?: number;
  readonly prefix?: string;

  private readonly destroyed$ = new Subject<void>();

  constructor(
    @Inject(SWUI_GRID_WIDGET_CONFIG) { field, dataSource, schema: { footer } }: SwuiGridWidgetConfig<SwuiTotalFooterWidgetSchema>
  ) {
    this.format = footer?.format;
    this.prefix = footer?.prefix;
    this.delimiter = footer?.delimiter;
    this.fractionCount = footer?.fractionCount;

    if (dataSource) {
      this.connectToDataSource(field, dataSource);
    }
  }

  ngOnDestroy(): void {
    this.destroyed$.next(undefined);
    this.destroyed$.complete();
  }

  private connectToDataSource( field: string, dataSource: SwuiGridDataSource<any> ) {
    dataSource.connect().pipe(
      map<any[], number>(( data: any[] ) => {
        return data.reduce(( total, row ) => {
          if (field in row) {
            total += row[field];
          }
          return total;
        }, 0);
      }),
      takeUntil(this.destroyed$),
    ).subscribe(total => {
      this.value = total;
    });
  }
}

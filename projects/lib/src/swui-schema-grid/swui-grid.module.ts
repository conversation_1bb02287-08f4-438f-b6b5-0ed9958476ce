import { ModuleWithProviders, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SwuiTdWidgetsModule } from './td-widget/td-widgets.module';
import { WidgetRegistry } from './registry/registry';
import { DefaultWidgetRegistry } from './registry/default-registry';
import { SwuiGridWidgetChooserModule } from './widget-chooser/widget-chooser.module';
import { SwuiGridRowActionsModule } from './row-actions/row-actions.module';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiGridComponent } from './swui-grid.component';
import { SwuiGridBulkActionsModule } from './bulk-actions/bulk-actions.module';
import { SwuiDefaultWidgetModule } from './default-widget/default-widget.module';
import { SwuiColumnsManagementModule } from './columns-management/columns-management.module';
import { GRID_CONFIG, SwuiGridConfig } from './swui-grid.config';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { SwuiProgressContainerModule } from '../swui-progress-container/swui-progress-container.module';
import { SwuiGridUrlHandlerService } from './swui-grid-url-handler.service';
import { SwuiFooterWidgetsModule } from './footer-widget/footer-widgets.module';
import { MatSortModule } from '@angular/material/sort';
import { MatTableModule } from '@angular/material/table';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatButtonModule } from '@angular/material/button';

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),

    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatCheckboxModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,

    SwuiTdWidgetsModule,
    SwuiFooterWidgetsModule,
    SwuiGridRowActionsModule,
    SwuiGridWidgetChooserModule,
    SwuiDefaultWidgetModule,
    SwuiGridBulkActionsModule,
    SwuiColumnsManagementModule,
    MatProgressSpinnerModule,
    SwuiProgressContainerModule,
  ],
  declarations: [
    SwuiGridComponent,
  ],
  exports: [
    SwuiGridComponent,
  ],
  providers: [
    SwuiGridUrlHandlerService,
  ]
})
export class SwuiGridModule {
  static forRoot( config?: SwuiGridConfig ): ModuleWithProviders<SwuiGridModule> {
    return {
      ngModule: SwuiGridModule,
      providers: [
        {
          provide: WidgetRegistry,
          useClass: DefaultWidgetRegistry,
        },
        {
          provide: GRID_CONFIG,
          useValue: config,
        }
      ]
    };
  }

}

import { SchemaTopFilterField } from '../swui-schema-top-filter/swui-schema-top-filter.model';
import { SwuiFooterWidgetSchema } from './footer-widget/footer-widget';
import { SwuiTdWidgetSchema } from './td-widget/td-widget';
import { InjectionToken } from '@angular/core';
import { SwuiGridWidgetConfig } from './registry/registry';

export const SWUI_GRID_WIDGET_CONFIG = new InjectionToken<SwuiGridWidgetConfig<any>>('grid-widget-config');

interface BaseSchemaField {
  type?: string;
  title?: string;
  value?: any;

  [name: string]: any;
}

export type SwuiGridSchemaField = BaseSchemaField & SwuiTdWidgetSchema & SwuiFooterWidgetSchema & {
  field: string;

  isList?: boolean;
  isListVisible?: boolean;

  isSortable?: boolean;
  sortStartFrom?: 'asc' | 'desc';

  alignment?: {
    th?: string;
    td?: string;
  };
  class?: string;

  td?: BaseSchemaField;
  footer?: BaseSchemaField;
};

export type SwuiGridField = SwuiGridSchemaField & SchemaTopFilterField;

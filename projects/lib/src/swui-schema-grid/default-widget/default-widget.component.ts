import { Component, Inject } from '@angular/core';
import { SwuiGridWidgetConfig } from '../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../swui-grid.model';

export interface SwuiDefaultWidgetSchema {
  type?: string;
}

@Component({
    template: `
    <span class="label label-danger">Widget:</span>
    <span class="label label-danger">Cannot find '{{ field }}: {{ type }}'</span> {{ value || title }}
  `,
    standalone: false
})
export class DefaultWidgetComponent {
  readonly field: string;
  readonly title?: string;
  readonly value: string;
  readonly type?: string;

  constructor( @Inject(SWUI_GRID_WIDGET_CONFIG) { field, title, value, schema: { type } }: SwuiGridWidgetConfig<SwuiDefaultWidgetSchema> ) {
    this.field = field;
    this.title = title;
    this.value = value;
    this.type = type;
  }
}

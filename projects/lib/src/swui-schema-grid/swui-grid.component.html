<div class="sw-grid" [ngClass]="{'sticky': stickyHeader}">
  <div class="sw-grid__header">
    <div class="sw-grid__amount">
      <span *ngIf="!isEmpty && !blindPaginator && showTotalItems">
        {{total + ' ' + (totalItemsTitle | translate)}}
      </span>
    </div>
    <div class="sw-grid__actions sw-grid__actions--projected">
      <ng-content></ng-content>
    </div>
    <div class="sw-grid__actions sw-grid__actions--native">
      <lib-swui-bulk-actions *ngIf="bulkActions && bulkActions.length && !bulkSelectionOnly"
                             [actions]="bulkActions" [rows]="selection.selected">
      </lib-swui-bulk-actions>

      <div class="sw-grid__default">
        <button
          *ngIf="!disableRefreshAction"
          type="button"
          mat-icon-button
          matTooltip="Refresh"
          class="sw-grid__refresh"
          (click)="refreshData()">
          <mat-icon>refresh</mat-icon>
        </button>

        <lib-swui-columns-management
          *ngIf="columnsManagement"
          [gridId]="gridId"
          [schema]="schema"
          (columnsChange)="columnsChanged($event)"
        ></lib-swui-columns-management>
      </div>
    </div>
  </div>

  <div class="sw-grid__body">

    <table mat-table class="sw-grid__table" [dataSource]="dataSource" matSort [matSortActive]="sortActive" [matSortDirection]="sortDirection || ''">

      <!-- Checkbox Column -->
      <ng-container matColumnDef="{{ bulkActionsColumnName }}">
        <th mat-header-cell *matHeaderCellDef class="sw-grid__th">
          <div class="sw-grid__th-inner">
            <mat-checkbox (change)="$event ? masterToggle() : null"
                          [checked]="selection.hasValue() && isAllSelected()"
                          [indeterminate]="selection.hasValue() && !isAllSelected()"
                          [disabled]="isEmpty || (loading$ | async)">
            </mat-checkbox>
          </div>
        </th>
        <td mat-cell *matCellDef="let row">
          <mat-checkbox (click)="$event.stopPropagation()"
                        (change)="$event ? selection.toggle(getSelectionRow(row)) : null"
                        [checked]="selection.isSelected(getSelectionRow(row))"
                        [disabled]="!isRowAvailable(row)">
          </mat-checkbox>
        </td>
        <ng-container *ngIf="footer">
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>
      </ng-container>

      <ng-container *ngFor="let item of columnDefSchema" [matColumnDef]="item.field">
        <ng-container *ngIf="item.isSortable; else defaultColumnTitle">
          <th
            mat-header-cell
            *matHeaderCellDef
            mat-sort-header
            [start]="item.sortStartFrom || 'asc'"
            [class]="'start-from-' + (item.sortStartFrom || 'asc')"
            [ngClass]="item.alignment?.th">
            {{ item['title'] | translate }}
          </th>
        </ng-container>
        <ng-template #defaultColumnTitle>
          <th mat-header-cell *matHeaderCellDef [ngClass]="item.alignment?.th">
            <div class="sw-grid__th-inner">
              {{ item['title'] | translate }}
            </div>
          </th>
        </ng-template>

        <td mat-cell
            *matCellDef="let row"
            grid-widget-chooser
            type="td"
            [registry]="registry"
            [schema]="item"
            [ngClass]="[
              item.isSortable ? 'sortable-cell' : '',
              item.alignment?.td || '',
              item.class || ''
            ]"
            [action]="widgetActionEmitted"
            [row]="row"
        >
        </td>

        <ng-container *ngIf="footer">
          <td mat-footer-cell *matFooterCellDef grid-widget-chooser type="footer" [registry]="registry"
              [schema]="item" [dataSource]="dataSource">
          </td>
        </ng-container>
      </ng-container>

      <ng-container matColumnDef="{{ rowActionsColumnName }}">
        <th mat-header-cell
            *matHeaderCellDef
            [ngClass]="{ 'row-actions__short': rowActions.length > 1, 'row-actions': rowActions.length == 1 }">
          <div class="sw-grid__th-inner sw-grid__th-inner-actions">
            {{ rowActionsColumnTitle | translate }}
          </div>
        </th>
        <td mat-cell *matCellDef="let row"
            [ngClass]="{ 'row-actions__short': rowActions.length > 1, 'row-actions': rowActions.length == 1 }">
          <lib-swui-grid-row-actions [actions]="rowActions" [row]="row" [menuIcon]="rowActionsMenuIcon"
                                     [ignorePlainLink]="ignorePlainLink">
          </lib-swui-grid-row-actions>
        </td>
        <ng-container *ngIf="footer">
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>
      </ng-container>
      <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: stickyHeader"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      <ng-container *ngIf="footer">
        <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
      </ng-container>
    </table>

    <div *ngIf="loading || (loading$ | async)" class="sw-grid__loading">
      <lib-swui-progress-container class="loading"></lib-swui-progress-container>
    </div>

    <div class="sw-grid__empty" *ngIf="isEmpty">
      {{ 'COMPONENTS.GRID.EMPTY_LIST' | translate }}
    </div>

  </div>

  <mat-paginator
    *ngIf="pagination"
    [class.hidden]="isEmpty"
    hidePageSize
    [showFirstLastButtons]="!blindPaginator"
    (page)="onPageClick()"
    class="sw-grid__pagination">
  </mat-paginator>
</div>

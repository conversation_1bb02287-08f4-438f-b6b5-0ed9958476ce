import { SwuiTdClickWidgetComponent } from '../td-widget/click/click.widget';
import { WidgetListItem } from './registry';
import { SwuiTdStringWidgetComponent } from '../td-widget/string/string.widget';
import { SwuiTdTimestampWidgetComponent } from '../td-widget/timestamp/timestamp.widget';
import { SwuiTdUserWidgetComponent } from '../td-widget/user/user.widget';
import { SwuiTdBooleanWidgetComponent } from '../td-widget/boolean/boolean.widget';
import { SwuiTdGameLabelsWidgetComponent } from '../td-widget/game-labels/game-labels.widget';
import { SwuiTdGamesLabelsWidgetComponent } from '../td-widget/games-labels/games-labels.widget';
import { SwuiTdColorfulLabelsWidgetComponent } from '../td-widget/colorful-labels/colorful-labels.widget';
import { SwuiTdJackpotWidgetComponent } from '../td-widget/jackpot/jackpot.widget';
import { SwuiTdPercentWidgetComponent } from '../td-widget/percent/percent.widget';
import { SwuiTdCalcWidgetComponent } from '../td-widget/calc/calc.widget';
import { SwuiTdLinkWidgetComponent } from '../td-widget/link/link.widget';
import { SwuiTdCalcAsyncWidgetComponent } from '../td-widget/calc-async/calc-async.widget';
import { SwuiTdIconPopoverWidgetComponent } from '../td-widget/icon-popover/icon-popover.widget';
import { SwuiTdCurrencyWidgetComponent } from '../td-widget/currency/currency.widget';
import { SwuiTdIconWidgetComponent } from '../td-widget/icon/icon.widget';
import { SwuiFooterStringWidgetComponent } from '../footer-widget/string/string.widget';
import { SwuiFooterTotalWidgetComponent } from '../footer-widget/total/total.widget';
import { SwuiTdPercentEditableWidgetComponent } from '../td-widget/percent-editable/percent-editable.widget';
import { SwuiTdStatusWidgetComponent } from '../td-widget/status/status.widget';
import { SwuiTdInactivityWidgetComponent } from '../td-widget/inactivity/inactivity.widget';
import { SwuiTdListWidgetComponent } from '../td-widget/list/list.widget';
import { SwuiTdImageWidgetComponent } from '../td-widget/image/image.widget';

export function getDefaultWidgetsList(): WidgetListItem[] {
  return [
    { type: 'tdstring', component: SwuiTdStringWidgetComponent },
    { type: 'tdnumber', component: SwuiTdStringWidgetComponent },
    { type: 'tddate', component: SwuiTdTimestampWidgetComponent },
    { type: 'tdrange', component: SwuiTdStringWidgetComponent },
    { type: 'tdmultiselect', component: SwuiTdStringWidgetComponent },
    { type: 'tdchoice', component: SwuiTdStringWidgetComponent },
    { type: 'tdstatus', component: SwuiTdStatusWidgetComponent },
    { type: 'tddatetimerange', component: SwuiTdTimestampWidgetComponent },
    { type: 'tdarray', component: SwuiTdStringWidgetComponent },
    { type: 'tduser', component: SwuiTdUserWidgetComponent },
    { type: 'tdboolean', component: SwuiTdBooleanWidgetComponent },
    { type: 'tdgamelabels', component: SwuiTdGameLabelsWidgetComponent },
    { type: 'tdgameslabels', component: SwuiTdGamesLabelsWidgetComponent },
    { type: 'tdcolorfullabels', component: SwuiTdColorfulLabelsWidgetComponent },
    { type: 'tdjackpot', component: SwuiTdJackpotWidgetComponent },
    { type: 'tdtimestamp', component: SwuiTdTimestampWidgetComponent },
    { type: 'tdpercent', component: SwuiTdPercentWidgetComponent },
    { type: 'tdpercenteditable', component: SwuiTdPercentEditableWidgetComponent },
    { type: 'tdcalc', component: SwuiTdCalcWidgetComponent },
    { type: 'tdlink', component: SwuiTdLinkWidgetComponent },
    { type: 'tdclick', component: SwuiTdClickWidgetComponent },
    { type: 'tdcalcasync', component: SwuiTdCalcAsyncWidgetComponent },
    { type: 'tdiconpopover', component: SwuiTdIconPopoverWidgetComponent },
    { type: 'tdcurrency', component: SwuiTdCurrencyWidgetComponent },
    { type: 'tdicon', component: SwuiTdIconWidgetComponent },
    { type: 'tdinactivity', component: SwuiTdInactivityWidgetComponent },
    { type: 'tdlist', component: SwuiTdListWidgetComponent },
    { type: 'tdimage', component: SwuiTdImageWidgetComponent },

    { type: 'footerstring', component: SwuiFooterStringWidgetComponent },
    { type: 'footertotal', component: SwuiFooterTotalWidgetComponent },
  ];
}

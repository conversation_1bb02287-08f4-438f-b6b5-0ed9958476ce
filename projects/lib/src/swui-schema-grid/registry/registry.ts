import { StaticProvider, TypeProvider } from '@angular/core';
import { DefaultWidgetComponent } from '../default-widget/default-widget.component';
import { SWUI_GRID_WIDGET_CONFIG, SwuiGridSchemaField } from '../swui-grid.model';
import { SwuiGridDataSource } from '../swui-grid.datasource';

export interface SwuiGridWidgetConfig<T> {
  field: string;
  title?: string;
  value: any;
  row: any;
  action: any;
  schema: T;
  dataSource?: SwuiGridDataSource<any>;
}

export interface SwuiGridWidgetRegistry {
  registryType: string;

  getWidgetRegistryConfig( scope: string, schema: SwuiGridSchemaField ): WidgetRegistryConfig;
}

export type WidgetRegistryProvidersFn = ( config: SwuiGridWidgetConfig<any> ) => StaticProvider[];

// used in widget factory
export interface WidgetRegistryConfig {
  component: TypeProvider;
  fn: WidgetRegistryProvidersFn;
}

export interface WidgetListItem {
  type: string;
  component: TypeProvider;
  providerFn?: WidgetRegistryProvidersFn | undefined; // if not defined, then will be determined from type prefix
}

function getProvider( config: SwuiGridWidgetConfig<any> ): StaticProvider[] {
  return [{
    provide: SWUI_GRID_WIDGET_CONFIG,
    useValue: config
  }];
}

export abstract class WidgetRegistry implements SwuiGridWidgetRegistry {

  protected readonly configs: { [type: string]: WidgetRegistryConfig } = {};

  protected constructor( public registryType: string ) {
  }

  register( configName: string, component: TypeProvider, providerFn?: WidgetRegistryProvidersFn ) {
    this.configs[configName] = { component, fn: providerFn || getProvider };
  }

  registerWidgetsList( list: WidgetListItem[], registryType?: string ) {
    if (registryType) {
      this.registryType = registryType;
    }
    list.forEach(item => {
      const { type, component, providerFn } = item;
      this.register(type, component, providerFn);
    });
  }

  getWidgetRegistryConfig( scope: 'td' | 'footer', schema: SwuiGridSchemaField ): WidgetRegistryConfig {
    const type = schema[scope]?.type || schema.type || 'string';
    const name = scope + type;
    return this.configs[name] || {
      component: DefaultWidgetComponent,
      fn: getProvider,
    };
  }
}

import { WidgetRegistry } from './registry';
import { Inject, Injectable } from '@angular/core';
import { getValidGridConfig, GRID_CONFIG, SwuiGridConfig } from '../swui-grid.config';


@Injectable()
export class DefaultWidgetRegistry extends WidgetRegistry {
  constructor(
    @Inject(GRID_CONFIG) private readonly config: SwuiGridConfig
  ) {
    super('default');

    const { widgets } = getValidGridConfig(this.config);
    this.registerWidgetsList(widgets);
  }

}

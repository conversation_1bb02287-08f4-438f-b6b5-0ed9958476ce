import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SwuiGridComponent } from './swui-grid.component';
import { CommonModule } from '@angular/common';
import { SwuiGridModule } from './swui-grid.module';
import { TranslateModule } from '@ngx-translate/core';
import { RouterTestingModule } from '@angular/router/testing';
import { MockUrlHandler, SwuiGridUrlHandlerService } from './swui-grid-url-handler.service';
import { SwHubAuthService } from '../services/sw-hub-auth/sw-hub-auth.service';
import { SwDexieModule } from '../services/sw-dexie/sw-dexie.module';
import { DEXI_CONFIG } from '../services/sw-dexie/sw-dexie.service';
import { ReplaySubject } from 'rxjs';

export interface TestTableDataItem {
  [key: string]: any;
}

describe('SwuiGridComponent', () => {
  let component: SwuiGridComponent<TestTableDataItem>;
  let fixture: ComponentFixture<SwuiGridComponent<TestTableDataItem>>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        CommonModule,
        TranslateModule.forRoot(),
        SwuiGridModule.forRoot(),
        RouterTestingModule,
        SwDexieModule.forRoot('test')
      ],
      providers: [
        { provide: SwuiGridUrlHandlerService, useClass: MockUrlHandler },
        { provide: SwHubAuthService, useValue: {
            logged: new ReplaySubject<void>(),
            username: 'username-test',
            entityKey: 'entityKey-test'
          } },
        { provide: DEXI_CONFIG, useValue: 'hubName' },
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent<SwuiGridComponent<TestTableDataItem>>(SwuiGridComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

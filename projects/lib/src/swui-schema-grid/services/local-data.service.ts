import { Injectable } from '@angular/core';
import { GridDataService } from './grid-data.service';
import { HttpHeaders, HttpParams, HttpResponse } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable()
export class LocalDataService<T extends object> implements GridDataService<T> {

  public localData: T[] = [];

  constructor() {
  }

  getGridData( params: HttpParams ): Observable<HttpResponse<T[]>> {
    const offset = Number(params.get('offset'));
    const limit = Number(params.get('limit') || Infinity);
    const sortBy = params.get('sortBy') || '';
    const sortOrder = params.get('sortOrder') || '';

    return of([...this.localData])
      .pipe(
        map(( list: T[] ) => {
          const dataExist = list.length > 0;
          const sortOrderIsSet = typeof sortOrder !== 'undefined' && sortOrder !== '';
          const sortByIsCorrect = dataExist && sortBy in list[0];

          if (dataExist && sortOrderIsSet && sortByIsCorrect) {
            const mod = sortOrder.toLowerCase() === 'asc' ? 1 : -1;
            list = list.sort(( a: any, b: any ) => {
              return a[sortBy] > b[sortBy] ? mod : -1 * mod;
            });
          }
          return list;
        }),
        map(( list: T[] ) => {
          return list.slice(offset, offset + limit);
        }),
        map(( body: T[] ) => {
          let headers: HttpHeaders = new HttpHeaders();
          headers = headers.append('x-paging-limit', (limit ? limit.toString() : '20'));
          headers = headers.append('x-paging-offset', (offset ? offset.toString() : '0'));
          headers = headers.append('x-paging-total', this.localData.length.toString());
          return new HttpResponse({ body, headers });
        })
      );
  }
}

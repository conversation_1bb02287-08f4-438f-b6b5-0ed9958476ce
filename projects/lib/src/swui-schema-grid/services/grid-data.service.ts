import { HttpParams, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface GridRequestData {
  [key: string]: any;
}

export interface GridDataService<T> {
  getGridData( params: HttpParams, data?: GridRequestData ): Observable<HttpResponse<T[]>>;
}

export abstract class SwuiGridDataService<T> implements GridDataService<T> {
  abstract getGridData( params: HttpParams, data?: GridRequestData ): Observable<HttpResponse<T[]>>;
}

import { moduleMetadata, storiesOf } from '@storybook/angular';
import { SwuiColumnsManagementModule } from './columns-management.module';
import { I18nModule } from '../../i18n.module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { SCHEMA_LIST } from '../_stories/users-example.schema';
import { action } from 'storybook/actions';

const EN = {
  COMPONENTS: {
    COLUMNS_MANAGEMENT: {
      menuTitle: '',
      tooltip: ''
    },
    MENU_SELECT: {
      clear: 'Clear',
      cancel: 'Cancel',
      apply: 'Apply',
      search: 'Search'
    }
  }
};

const template = `
  <div style="height: 100vh; padding: 32px; overflow: auto;" class="mat-body-1">
    <div class="mat-elevation-z0">
      <lib-swui-columns-management
        [gridId]="gridId"
        [schema]="schema"
        (columnsChange)="change($event)"
      ></lib-swui-columns-management>
    </div>
  </div>
`;

storiesOf('Grid/Columns Management', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        I18nModule.forRoot({ translations: { en: EN } }),
        SwuiColumnsManagementModule
      ]
    })
  )
  .add('usage', () => ({
    template,
    props: {
      gridId: 'grid-examples-columns',
      schema: SCHEMA_LIST,
      change: action('change')
    },
  }));

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiMenuSelectModule } from '../../swui-menu-select/swui-menu-select.module';
import { SwuiColumnsManagementComponent } from './columns-management.component';


@NgModule({
  imports: [
    CommonModule,
    TranslateModule,
    MatButtonModule,
    MatTooltipModule,
    MatMenuModule,
    SwuiMenuSelectModule,
  ],
  declarations: [
    SwuiColumnsManagementComponent,
  ],
  exports: [
    SwuiColumnsManagementComponent
  ],
})
export class SwuiColumnsManagementModule {
}

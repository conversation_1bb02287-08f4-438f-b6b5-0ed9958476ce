import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';


export interface SchemaFieldVisibilityData {
  index: number;
  isListVisible: boolean;
}

export interface SchemaVisibilityData {
  [fieldName: string]: SchemaFieldVisibilityData;
}

export abstract class ColumnsManagementDataProvider {
  abstract getColumns( gridId: string ): Observable<SchemaVisibilityData>;

  abstract saveColumns( gridId: string, data: SchemaVisibilityData ): Observable<SchemaVisibilityData>;
}

export class LocalStorageColumnsDataProvider extends ColumnsManagementDataProvider {
  getColumns( gridId: string ): Observable<SchemaVisibilityData> {
    return of(localStorage.getItem(gridId)).pipe(
      map(data => data !== null ? JSON.parse(data) : {})
    );
  }

  saveColumns( gridId: string, data: SchemaVisibilityData ): Observable<SchemaVisibilityData> {
    localStorage.setItem(gridId, JSON.stringify(data));
    return of(data);
  }
}

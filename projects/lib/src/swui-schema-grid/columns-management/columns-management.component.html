<button
  type="button"
  mat-icon-button
  matTooltip="{{ 'COMPONENTS.COLUMNS_MANAGEMENT.tooltip' | translate }}"
  [matMenuTriggerFor]="columnsMenu"
  class="sw-columns">

  <svg
    class="sw-columns__icon"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">

    <rect width="8" height="3" rx="2" ry="2" x="3" y="4"/>
    <rect width="8" height="3" rx="2" ry="2" x="3" y="10"/>
    <rect width="8" height="3" rx="2" ry="2" x="3" y="16"/>

    <rect width="8" height="3" rx="2" ry="2" x="13" y="4"/>
    <rect width="8" height="3" rx="2" ry="2" x="13" y="10"/>
    <rect width="8" height="3" rx="2" ry="2" x="13" y="16"/>

  </svg>
</button>

<mat-menu #columnsMenu="matMenu">
  <lib-swui-menu-select
    [title]="'COMPONENTS.COLUMNS_MANAGEMENT.menuTitle' | translate"
    [data]="data"
    [selected]="selected"
    [selectAll]="true"
    (applyData)="onApply($event)"
    (cancelClick)="onCancel()">
  </lib-swui-menu-select>
</mat-menu>

import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { TranslateModule } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { SwuiMenuSelectModule } from '../../swui-menu-select/swui-menu-select.module';
import { SwuiColumnsManagementComponent } from './columns-management.component';


describe('SwuiColumnsManagementComponent', () => {
  let component: SwuiColumnsManagementComponent;
  let fixture: ComponentFixture<SwuiColumnsManagementComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        TranslateModule.forRoot(),
        MatButtonModule,
        MatTooltipModule,
        MatMenuModule,
        SwuiMenuSelectModule,
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiColumnsManagementComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

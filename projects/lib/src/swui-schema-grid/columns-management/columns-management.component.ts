import { Component, EventEmitter, Inject, Input, OnDestroy, OnInit, Optional, Output, ViewChild } from '@angular/core';
import { MatMenuTrigger } from '@angular/material/menu';
import { BehaviorSubject, of, Subject } from 'rxjs';
import { switchMap, take, takeUntil } from 'rxjs/operators';
import { SwuiSelectOption } from '../../swui-select/swui-select.interface';
import { ColumnsManagementDataProvider, LocalStorageColumnsDataProvider, SchemaVisibilityData } from './columns-management.model';
import { SwuiGridSchemaField } from '../swui-grid.model';


type SelectOption = SwuiSelectOption & { index: number; selected: boolean; };

function buildSelectOptions( schema: SwuiGridSchemaField[] | undefined, columnsSet: SchemaVisibilityData ): SelectOption[] {
  return (schema || [])
    .filter(( { field } ) => field !== 'row-actions-column' && field !== 'bulk-actions-column')
    .map<SelectOption>(( { field, title, isListVisible }, index ) => {
      const data = (columnsSet || {})[field];
      return {
        id: field,
        text: title || '',
        index: data ? data.index : index,
        selected: data ? data.isListVisible : isListVisible === undefined ? true : isListVisible
      };
    })
    .sort(( { index: a }, { index: b } ) => a - b);
}

@Component({
    selector: 'lib-swui-columns-management',
    templateUrl: './columns-management.component.html',
    styleUrls: ['./columns-management.component.scss'],
    standalone: false
})
export class SwuiColumnsManagementComponent implements OnInit, OnDestroy {

  @Input() set gridId( value: string | undefined ) {
    this.gridId$.next(value);
  }

  @Input() set schema( value: SwuiGridSchemaField[] | undefined ) {
    this._schema = value;
    this.setSelectOptions(value, this.columnsSet);
  }

  @Output() columnsChange = new EventEmitter<SchemaVisibilityData>();

  @ViewChild(MatMenuTrigger, { static: true }) menuRef?: MatMenuTrigger;

  data: SwuiSelectOption[] = [];
  selected: string[] = [];

  private _schema?: SwuiGridSchemaField[];
  private columnsSet: SchemaVisibilityData = {};
  private selectOptions: SelectOption[] = [];

  private readonly gridId$ = new BehaviorSubject<string | undefined>(undefined);
  private readonly destroyed = new Subject<void>();
  private readonly dataProvider;

  constructor( @Optional() @Inject(ColumnsManagementDataProvider) provider: ColumnsManagementDataProvider ) {
    this.dataProvider = provider || new LocalStorageColumnsDataProvider();
  }

  ngOnInit(): void {
    this.gridId$.pipe(
      switchMap(gridId => gridId ? this.dataProvider.getColumns(gridId) : of({})),
      takeUntil(this.destroyed)
    ).subscribe(columnsSet => {
      this.setColumnsSet(columnsSet);
    });
  }

  ngOnDestroy(): void {
    this.destroyed.next(undefined);
    this.destroyed.complete();
  }

  onApply( values: string[] ) {
    if (this.menuRef) {
      this.menuRef.closeMenu();
    }
    const gridId = this.gridId$.value;
    if (gridId) {
      const selectedData = new Set(values);
      const data = this.selectOptions.reduce<SchemaVisibilityData>(( result, { id, index } ) => ({
        ...result,
        [id]: {
          isListVisible: selectedData.has(id),
          index
        }
      }), {});
      this.dataProvider.saveColumns(gridId, data).pipe(
        take(1)
      ).subscribe(columnsSet => {
        this.setColumnsSet(columnsSet);
      });
    }
  }

  onCancel() {
    if (this.menuRef) {
      this.menuRef.closeMenu();
    }
  }

  private setColumnsSet( value: SchemaVisibilityData ): void {
    this.columnsSet = value;
    this.setSelectOptions(this._schema, value);
    this.emit();
  }

  private setSelectOptions( schema: SwuiGridSchemaField[] | undefined, value: SchemaVisibilityData ): void {
    this.selectOptions = buildSelectOptions(schema, value);
    this.data = this.selectOptions.map<SwuiSelectOption>(( { id, text } ) => ({ id, text }));
    this.selected = this.selectOptions
      .filter(( { selected } ) => selected)
      .map<string>(( { id } ) => id);
  }

  private emit(): void {
    if (this._schema) {
      this.columnsChange.emit(this._schema.reduce(( result, { field, isListVisible }, index ) => {
        const data = this.columnsSet[field];
        return ({
          ...result,
          [field]: {
            index: data ? data.index : index,
            isListVisible: data ? data.isListVisible : isListVisible
          }
        });
      }, {}));
    }
  }
}

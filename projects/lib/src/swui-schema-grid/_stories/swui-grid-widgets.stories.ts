import { moduleMetadata, storiesOf } from '@storybook/angular';
import { APP_BASE_HREF } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';
import { delay, take } from 'rxjs/operators';
import { SwDexieModule } from '../../services/sw-dexie/sw-dexie.module';
import { template } from './swui-grid.stories';

import { I18nModule } from '../../i18n.module';
import { SwuiGridModule } from '../swui-grid.module';
import { TestWidgetModule } from './test-widget/test-widget.module';
import {
  currencyFieldExample,
  iconFieldExample,
  operatorsFieldExample,
  percentFieldExample,
  SCHEMA_LIST as demoSchema
} from './users-example.schema';
import { users as demoData } from './users-example.data';
import { WidgetActionEvent } from '../td-widget/td-widget';
import { RouteNoopModule } from './route-noop.module';
import { SwuiGridSchemaField } from '../swui-grid.model';


const EN = require('./locale.json');

storiesOf('Grid/Widgets', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        TestWidgetModule,
        I18nModule.forRoot({ translations: { en: EN } }),
        SwuiGridModule.forRoot(),
        RouteNoopModule,
        SwDexieModule.forRoot('SwUboLibrary'),
      ],
      providers: [{ provide: APP_BASE_HREF, useValue: '/iframe.html' }]
    })
  )
  .add('List', () => ({
    template,
    props: {
      title: 'List',
      schema: [...demoSchema, operatorsFieldExample].map(
        ( item: SwuiGridSchemaField ) => {
          if (item.field === 'operators') {
            item = {
              ...item,
              td: {
                type: 'list',
                arrayKey: 'id',
                valueFn: ( row: any ) => {
                  let result = [];
                  if (row && row.operators && Array.isArray(row.operators)) {
                    result = row.operators;
                  } else if (row && row.operators) {
                    result = row.operators.brands;
                  }
                  return result ? result : [];
                }
              },
            };
          }
          return item;
        }).filter(item => ['username', 'email', 'status', 'active', 'operators'].indexOf(item.field) > -1),
      data: demoData.map(
        items => {
          items = {
            ...items
          };
          return items;
        }
      )
    },
  }))
  .add('Calc, Datetime, Percent', () => ({
    template,
    props: {
      gridId: 'grid-04',
      title: 'Calc, Datetime, Percent',
      schema: [...demoSchema, percentFieldExample].map(
        ( item: SwuiGridSchemaField ) => {
          if (item.field === 'createdAt') {
            item = { ...item, title: 'Datetime' };
          }
          return item;
        })
        .filter(item => ['username', 'createdAt', 'profileFilled', 'calcTest'].indexOf(item.field) > -1),
      data: demoData
        .map(( item: any ) => {
          item['profileFilled'] = Math.floor(Math.random() * 100) + 1;
          return item;
        })
        .map(item => ({ username: item.username, createdAt: item.createdAt, profileFilled: item.profileFilled, calcTest: item.calcTest })),
    },
  }))
  .add('Status, Inactivity, Calc-Async, Icon-Popover', () => ({
    template,
    props: {
      gridId: 'grid-05',
      title: 'Status, Active, Calc-Async, Icon-Popover',
      widgetActionEmitted: ( data: WidgetActionEvent ) => {
        console.log('Widget action was emitted with data', data);
        // api emulation
        of(data).pipe(
          delay(500),
          take(1),
        ).subscribe(( actionData: WidgetActionEvent ) => {
          actionData.row.status = data.payload.status;
          actionData.payload.onCompleteFn();
        });
      },
      schema: [...demoSchema].map(( item: SwuiGridSchemaField ) => {
        if (item.field === 'email') {
          item = {
            ...item,
            title: 'Icon Popover',
            td: {
              type: 'iconpopover',
              titleFn: ( row: any ) => {
                return row.email + ': Lorem ipsum dolor sit amet';
              },
              classFn: () => {
                return '';
              }
            }
          };
        }
        if (item.field === 'username') {
          item = {
            ...item,
            title: 'Calc Async',
            td: {
              type: 'calcasync',
              useTranslate: false,
              source: of(
                [
                  { username: '2123123', comment: 'Async-data passed!' }
                ]
              ).pipe(delay(1000)),
              titleFn: ( data: any, row: any ) => {
                const foundItem = data.find(( i: any ) => {
                  return i.username.toLowerCase() === row.username.toLowerCase();
                });
                return foundItem ? foundItem.comment : row.username;
              },
              classFn: ( data: any, row: any ) => {
                return data
                  .find(( i: any ) => i.username.toLowerCase() === row.username.toLowerCase()) ? 'bg-warning' : 'bg-slate';
              }
            }
          };
        }
        if (item.field === 'status') {
          item = {
            ...item,
            td: {
              ...item.td,
              readonly: false
            }
          };
        }
        return item;
      })
        .filter(item => ['username', 'email', 'status', 'active', 'activity'].indexOf(item.field) > -1),
      data: [...demoData]
        .map(item => ({ username: item.username, email: item.email, status: item.status, active: item.active, activity: item.activity })),
    },
  }))
  .add('Currency, Link, Icon, Footer, Image', () => ({
    template,
    props: {
      footer: true,
      gridId: 'grid-06',
      title: 'Currency, Link, Icon, Footer, Image',
      schema: [...demoSchema, currencyFieldExample, iconFieldExample].map(
        ( item: SwuiGridSchemaField ) => {
          if (item.field === 'iconField') {
            item = {
              ...item,
              title: 'Icon',
              td: {
                type: 'icon',
                icon: 'search',
                titleFn: () => 'results',
                classFn: () => '',
                linkFn: () => [],
                canActivateFn: ( row: any ) => {
                  return row.username === '2222';
                }
              },
            };
          }
          if (item.field === 'username') {
            item = {
              ...item,
              title: 'Link',
              td: {
                ...item.td,
                type: 'link',
                linkFn: () => {
                  return [];
                }
              },
              footer: {
                value: 'Total (page)',
              }
            };
          }
          if (item.field === 'bnsRedeemed') {
            item = {
              ...item,
              footer: {
                type: 'total',
                format: 'currency',
              },
            };
          }
          return item;
        }
      ).filter(item => ['username', 'bnsRedeemed', 'iconField', 'image'].indexOf(item.field) > -1),
      data: demoData
        .map(( item: any ) => {
          item['bnsRedeemed'] = Math.floor(Math.random() * 1000000) + 1;
          return item;
        })
        .map(item => ({ username: item.username, bnsRedeemed: item.bnsRedeemed, image: item.image })),
    },
  }))
  .add('Percent-Editable', () => ({
    template,
    props: {
      gridId: 'grid-05',
      title: 'Percent-Editable',
      widgetActionEmitted: ( data: WidgetActionEvent ) => {
        console.log('Widget action was emitted with data', data);
        // api emulation
        of(data).pipe(
          delay(500),
          take(1),
        ).subscribe(( actionData: WidgetActionEvent ) => {
          actionData.payload.onCompleteFn();
        });
      },
      schema: [
        {
          field: 'royalties',
          title: 'royalties',
          type: 'number',
          isList: true,
          isViewable: false,
          isSortable: false,
          isFilterable: true,
          td: {
            type: 'percenteditable',
            formatted: false,
            remoteSave: true,
          },
          fractionCount: 2,
        },
      ],
      data: [
        {
          'royalties': 12,
        }
      ],
    },
  }));

import { SchemaFilterMatchEnum } from '../../swui-schema-top-filter/swui-schema-top-filter.model';
import { SwuiGridField } from '../swui-grid.model';

const USER_STATUS_CLASS_MAP = {
  normal: 'sw-chip sw-chip-green',
  suspended: 'sw-chip sw-chip-blue'
};

export const USER_STATUS_LIST = [
  { id: 'normal', code: 'normal', displayName: 'ENTITY_SETUP.USERS.statusActive', hidden: false },
  { id: 'suspended', code: 'suspended', displayName: 'ENTITY_SETUP.USERS.statusInactive', hidden: false },
  { id: 'test', code: 'test', displayName: 'test', hidden: true }
];

const SCHEMA: SwuiGridField[] = [
  {
    field: 'username',
    title: 'Username',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    isListVisible: true,
    class: 'test',
    td: {
      type: 'string',
      nowrap: true,
      truncate: {
        maxLength: 20,
        isEllipsis: true,
      }
    },
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'calcTest',
    title: 'calcTest',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    isListVisible: true,
    td: {
      type: 'calc',
      titleFn: (row: any) => row.calcTest || '-',
      truncate: {
        maxLength: 20,
        isEllipsis: true,
      }
    },
    alignment: {
      th: 'left',
      td: 'left',
    },
  },
  {
    field: 'email',
    title: 'E-mail',
    type: 'string',
    placeholder: '<EMAIL>',
    dataSource: '',
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    form: {
      pattern: '^\\S+@\\S+$',
    },
    alignment: {
      th: 'right',
      td: 'right',
    },
  },
  {
    field: 'status',
    title: 'COMPONENTS.GRID.STATUS',
    type: 'select',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    data: USER_STATUS_LIST,
    td: {
      type: 'status',
      statusList: USER_STATUS_LIST,
      classMap: USER_STATUS_CLASS_MAP,
      readonlyFn: ( row ) => {
        return row.status === 'test';
      }
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  },

  {
    field: 'active',
    title: 'Active',
    type: 'select',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    td: {
      type: 'inactivity',
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  },

  {
    field: 'image',
    title: 'Image',
    type: 'base64image',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    td: {
      type: 'image',
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  },

  {
    field: 'activity',
    title: 'Calc inactivity',
    type: 'select',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: true,
    td: {
      type: 'inactivity',
      valueFn: ( row: any, schema: { field: string } ) => row[schema.field] === 'enabled',
    },
    alignment: {
      th: 'center',
      td: 'center',
    },
  },

  // Dates
  {
    field: 'createdAt',
    title: 'Created At',
    type: 'datetimerange',
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.dateTime',
    config: {
      timePicker: true,
    },
    td: {
      type: 'timestamp',
      showTimeZone: true
    },
    alignment: {
      th: 'right',
      td: 'right',
    },
    filterMatch: {
      from: SchemaFilterMatchEnum.GreaterThanEquals,
      to: SchemaFilterMatchEnum.LessThan,
    },
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
  },
  {
    field: 'updatedAt',
    title: 'Updated At',
    type: 'date',
    scope: 'ALL.DEFAULT_SCHEMA_SECTIONS.dateTime',
    config: {
      timePicker: true,
    },
    td: {
      type: 'timestamp',
      showTimeZone: true
    },
    alignment: {
      th: 'right',
      td: 'right',
    },
    isList: true,
    isViewable: true,
    isSortable: true,
    isFilterable: true,
  },
];


export const SCHEMA_LIST = SCHEMA.filter(( { isList } ) => isList);


export const percentFieldExample = {
  field: 'profileFilled',
  title: 'Percent',
  td: {
    type: 'percent',
  },
  isList: true,
  isViewable: true,
  isSortable: false,
  isFilterable: false,
};

export const operatorsFieldExample = {
  field: 'operators',
  title: 'Operators',
  td: {
    type: 'list',
  },
  isList: true,
  isViewable: true,
  isSortable: false,
  isFilterable: false,
};

export const currencyFieldExample = {
  field: 'bnsRedeemed',
  title: 'Currency',
  type: 'string',
  td: {
    type: 'currency',
  },
  isList: true,
  isViewable: true,
  isSortable: false,
  isFilterable: false,
};

export const iconFieldExample = {
  field: 'iconField',
  title: 'Icon',
  td: {
    type: 'icon',
    icon: 'search',
    titleFn: () => 'Test title',
    classFn: () => 'testClass',
    linkFn: () => []
  },
  alignment: {
    td: 'center',
    th: 'center'
  },
  isList: true,
  isViewable: true,
  isSortable: false,
  isFilterable: false,
};

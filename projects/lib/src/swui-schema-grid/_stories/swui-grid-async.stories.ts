import { moduleMetadata, storiesOf } from '@storybook/angular';
import { APP_BASE_HREF } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ReplaySubject } from 'rxjs';

import { I18nModule } from '../../i18n.module';
import { SwDexieModule } from '../../services/sw-dexie/sw-dexie.module';
import { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';
import { GridDemoService } from './grid-demo.service';
import { SwuiGridModule } from '../swui-grid.module';
import { SCHEMA_LIST as demoSchema } from './users-example.schema';
import { template } from './swui-grid.stories';
import { SwuiGridDataService } from '../services/grid-data.service';
import { RouteNoopModule } from './route-noop.module';


const EN = require('./locale.json');

storiesOf('Grid/Async', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        I18nModule.forRoot({ translations: { en: EN } }),
        SwuiGridModule.forRoot(),
        RouteNoopModule,
        SwDexieModule.forRoot('SwUboLibrary'),
      ],
      providers: [
        { provide: APP_BASE_HREF, useValue: '/iframe.html' },
        { provide: SwuiGridDataService, useClass: GridDemoService },
        {
          provide: SwHubAuthService, useValue: {
            isLogged() {
              return true;
            },
            username: 'username',
            allowedTo() {
              return true;
            },
            isTwoFactor: true,
            logged: new ReplaySubject<void>()
          }
        },
      ]
    }))
  .add('Async Loading', () => ({
    template,
    props: {
      gridId: 'grid-examples-async-loading',
      schema: demoSchema,
      sortActive: 'createdAt',
      sortDirection: 'desc'
    },
  }));

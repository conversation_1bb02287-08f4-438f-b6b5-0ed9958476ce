import { HttpHeaders, HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';
import { GridDataService } from '../services/grid-data.service';
import { users } from './users-example.data';

function filterBy( name: string, params: HttpParams, list: any[] ): any[] {
  const criteria = params.get(name);
  if (criteria) {
    return list.filter(row => row[name].indexOf(criteria) !== -1);
  }
  return list;
}

function filter( params: HttpParams, list: any[] ): any[] {
  ['username', 'email'].forEach(name => {
    list = filterBy(name, params, list);
  });
  return list;
}

function sort( params: HttpParams, list: any[] ): any[] {
  if (list.length > 0) {
    const sortBy = params.get('sortBy');
    const sortOrder = params.get('sortOrder');
    if (sortBy && sortOrder && sortOrder !== '' && sortBy in list[0]) {
      const mod = sortOrder.toLowerCase() === 'asc' ? 1 : -1;
      return list.sort(( a, b ) => a[sortBy] > b[sortBy] ? mod : -1 * mod);
    }
  }
  return list;
}

@Injectable()
export class GridDemoService implements GridDataService<any> {

  getGridData( params: HttpParams ): Observable<HttpResponse<any[]>> {
    const offset = params.get('offset');
    const limit = params.get('limit');
    const source = sort(params, filter(params, users));
    let body = [...source];
    if (offset && Number(offset) > 0) {
      body = body.slice(Number(offset));
    }
    if (limit && Number(limit) < body.length) {
      body = body.slice(0, Number(limit));
    }
    const headers: HttpHeaders = new HttpHeaders({
      'x-paging-limit': limit ? limit.toString() : '20',
      'x-paging-offset': offset ? offset.toString() : '0',
      'x-paging-total': source.length.toString()
    });
    return of(new HttpResponse({ body, headers })).pipe(
      delay(1500)
    );
  }
}

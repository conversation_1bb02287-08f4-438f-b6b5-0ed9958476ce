import { Component, OnInit } from '@angular/core';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'test-grid',
    template: `<mat-card class="mat-elevation-z0" style="margin: 32px">
    <lib-swui-grid [schema]="testSchema" [data]="testData" [columnsManagement]="false" [pagination]="false">
      <div style="width:100%">
        <button mat-stroked-button (click)="doReplaceData()" color="primary">Replace Data</button>
        <button style="margin-left:5px" mat-stroked-button (click)="doEmptyData()" color="warn">Empty Data</button>
      </div>
    </lib-swui-grid>
  </mat-card>
  `,
    standalone: false
})
export class TestGridComponent implements OnInit {
  public testSchema = [
    {
      field: 'foo',
      title: 'Test column',
      type: 'string',
      isList: true,
    }
  ];

  public testData = [
    {
      foo: 'Initial'
    }
  ];

  constructor() {
  }

  ngOnInit() {
  }


  doReplaceData() {
    this.testData = [{
      foo: 'CHANGED'
    }];
  }

  doEmptyData() {
    this.testData = [];
  }

}

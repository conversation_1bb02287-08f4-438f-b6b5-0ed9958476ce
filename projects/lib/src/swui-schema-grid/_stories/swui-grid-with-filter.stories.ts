import { APP_BASE_HREF } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { moduleMetadata, storiesOf } from '@storybook/angular';
import { BehaviorSubject, ReplaySubject } from 'rxjs';

import { I18nModule } from '../../i18n.module';
import { SettingsService } from '../../services/settings/settings.service';
import { SwDexieModule } from '../../services/sw-dexie/sw-dexie.module';
import { SwHubAuthService } from '../../services/sw-hub-auth/sw-hub-auth.service';
import { SwuiSchemaTopFilterModule } from '../../swui-schema-top-filter/swui-schema-top-filter.module';
import { SwuiTopFilterDataService } from '../../swui-schema-top-filter/top-filter-data.service';
import { SwuiGridDataService } from '../services/grid-data.service';
import { SwuiGridModule } from '../swui-grid.module';
import { GridDemoService } from './grid-demo.service';
import { RouteNoopModule } from './route-noop.module';
import { SCHEMA_LIST } from './users-example.schema';


const EN = require('./locale.json');

storiesOf('Grid/Top Filter', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        I18nModule.forRoot({ translations: { en: EN } }),
        SwuiGridModule.forRoot(),
        RouteNoopModule,
        SwuiSchemaTopFilterModule,
        SwDexieModule.forRoot('SwUboLibrary'),
      ],
      providers: [
        SwuiTopFilterDataService,
        { provide: APP_BASE_HREF, useValue: '/iframe.html' },
        { provide: SwuiGridDataService, useClass: GridDemoService },
        {
          provide: SettingsService,
          useValue: {
            appSettings$: new BehaviorSubject({})
          }
        },
        {
          provide: SwHubAuthService, useValue: {
            isLogged() {
              return true;
            },
            username: 'username',
            allowedTo() {
              return true;
            },
            isTwoFactor: true,
            logged: new ReplaySubject<void>()
          }
        },
      ]
    })
  )
  .add('async grid', () => ({
    template: `
    <div style="height: 100vh; padding: 32px; overflow: auto;" class="mat-body-1">
      <div class="mat-elevation-z0">
        <lib-swui-schema-top-filter [schema]="schema"></lib-swui-schema-top-filter>
        <lib-swui-grid
          [gridId]="gridId"
          [data]="data"
          [schema]="schema"
          [pageSize]="9"
          [bulkActions]="bulkActions"
          (widgetActionEmitted)="widgetActionEmitted($event)"
          [rowActions]="rowActions">
        </lib-swui-grid>
      </div>
    </div>
      `,
    props: {
      gridId: 'grid-examples-async-loading',
      schema: SCHEMA_LIST,
      savedFilteredPageName: 'savedFilteredPageName',
      savedFilteredPageParams: {
        username: null,
        calcTest: null,
      }
    },
  }));

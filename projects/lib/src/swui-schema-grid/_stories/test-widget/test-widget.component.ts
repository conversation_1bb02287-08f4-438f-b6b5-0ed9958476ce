import { Component, Inject } from '@angular/core';
import { SwuiGridWidgetConfig } from '../../registry/registry';
import { SWUI_GRID_WIDGET_CONFIG } from '../../swui-grid.model';

@Component({
    template: '<b style="font-weight: lighter; text-transform: uppercase;">{{ value }}</b>',
    standalone: false
})
export class TestWidgetComponent {
  value: any;

  constructor( @Inject(SWUI_GRID_WIDGET_CONFIG) { value }: SwuiGridWidgetConfig<any> ) {
    this.value = value;
  }
}

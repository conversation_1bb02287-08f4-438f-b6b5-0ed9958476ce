import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'formattedMoney', pure: true,
    standalone: false
})
export class FormattedMoneyPipe implements PipeTransform {

  constructor() {
  }

  transform( input: any = 0, fractionCount: number = 2, delimiter: string = ' ',
             currencyLocale: string = window.navigator.language,
  ): string {
    let result;
    if (input === null) {
      result = '';
    } else if (input.toLocaleString && currencyLocale) {
      result = parseFloat(input).toLocaleString(currencyLocale, {
        minimumFractionDigits: fractionCount,
        maximumFractionDigits: fractionCount,
        useGrouping: !!delimiter,
      });
    } else {
      result = parseFloat(input).toFixed(fractionCount).replace(/(\d)(?=(\d{3})+($|\.))/g, '$1' + delimiter);
    }

    return result;
  }
}

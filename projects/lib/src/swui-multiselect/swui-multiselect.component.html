<input
  #input
  matInput
  type="text"
  [placeholder]="placeholder"
  [formControl]="selectControl"
  [matMenuTriggerFor]="menu"
  readonly>

<mat-menu #menu="matMenu" class="swui-multiselect-menu" [ngClass]="{ 'disabled': disabled }">
  <lib-swui-menu-select
    [data]="data"
    [selected]="value"
    [title]="title"
    [searchPlaceholder]="searchPlaceholder"
    [showSearch]="showSearch"
    (applyData)="onApply($event)"
    (cancelClick)="onCancel()">
  </lib-swui-menu-select>
</mat-menu>


import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

import { SwuiMultiselectComponent } from './swui-multiselect.component';
import { SwuiMenuSelectModule } from '../swui-menu-select/swui-menu-select.module';
import { MatMenuModule } from '@angular/material/menu';
import { MatInputModule } from '@angular/material/input';


export const MULTISELECT_MODULES = [
  ReactiveFormsModule,
  MatInputModule,
  MatMenuModule,
  SwuiMenuSelectModule,
];

@NgModule({
  declarations: [SwuiMultiselectComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ...MULTISELECT_MODULES
  ],
  exports: [SwuiMultiselectComponent],
})
export class SwuiMultiselectModule {
}

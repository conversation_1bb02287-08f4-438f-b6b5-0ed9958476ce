import { moduleMetadata, storiesOf } from '@storybook/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { APP_BASE_HREF } from '@angular/common';

import { I18nModule } from '../i18n.module';
import { SwuiMultiselectModule } from './swui-multiselect.module';
import { SwuiMultiselectComponent } from './swui-multiselect.component';
import { SwuiSelectOption } from '../swui-select/swui-select.interface';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCardModule } from '@angular/material/card';


const testData: SwuiSelectOption[] = [
  { id: '1', text: 'Solo Option1' },
  { id: '2', text: 'Test Option2' },
  { id: '3', text: 'Option3', disabled: true },
  { id: '4', text: 'Test Option4' },
  { id: '5', text: 'Option5' },
  { id: '6', text: 'Option6' },
  { id: '7', text: 'Option7 Option7 Option7' },
];

const EN = require('./locale.json');

const template = `
  <mat-card style="margin: 32px">
    <mat-form-field appearance="outline">
      <mat-label>
        Test label
      </mat-label>
      <lib-swui-multiselect
        [title]="title"
        [data]="data"
        [showSearch]="showSearch"
        [disabled]="disabled"
        [placeholder]="placeholder"
        [ngModel]="value">
      </lib-swui-multiselect>
    </mat-form-field>
  </mat-card>
`;

storiesOf('Forms/Multiselect', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        I18nModule.forRoot({ translations: { en: EN } }),
        SwuiMultiselectModule,
        MatFormFieldModule,
        MatCardModule,
      ],
      providers: [
        { provide: APP_BASE_HREF, useValue: '/' },
      ]
    })
  )
  .add('default', () => ({
    component: SwuiMultiselectComponent,
    template,
    props: {
      data: testData,
      title: 'Test',
      placeholder: 'Placeholder'
    }
  }))
  .add('search', () => ({
    component: SwuiMultiselectComponent,
    template,
    props: {
      data: testData,
      title: 'Test',
      showSearch: true
    }
  }))
  .add('disabled', () => ({
    component: SwuiMultiselectComponent,
    template,
    props: {
      data: testData,
      title: 'Test',
      disabled: true
    }
  }))
  .add('disabled value', () => ({
    component: SwuiMultiselectComponent,
    template,
    props: {
      data: testData,
      title: 'Test',
      disabled: true,
      value: ['1']
    }
  }));

import { Component, ElementRef, HostBinding, Input, Optional, Self, ViewChild } from '@angular/core';
import { UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';
import { FocusMonitor } from '@angular/cdk/a11y';
import { TranslateService } from '@ngx-translate/core';

import { SwuiSelectOption } from '../swui-select/swui-select.interface';
import { MatMenuTrigger } from '@angular/material/menu';
import { MatFormFieldControl } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';
import { ErrorStateMatcher } from '@angular/material/core';


const CONTROL_NAME = 'lib-swui-multiselect';
let nextUniqueId = 0;

@Component({
    selector: 'lib-swui-multiselect',
    templateUrl: './swui-multiselect.component.html',
    styleUrls: ['./swui-multiselect.component.scss'],
    providers: [{ provide: MatFormFieldControl, useExisting: SwuiMultiselectComponent }],
    standalone: false
})
export class SwuiMultiselectComponent extends SwuiMatFormFieldControl<string[] | undefined> {
  @Input() searchPlaceholder = 'COMPONENTS.MULTISELECT.search';
  @Input() title = '';
  @Input() showSearch = false;
  @Input() disableAllOption = false;

  @Input()
  set data( val: SwuiSelectOption[] ) {
    this._data = val && Array.isArray(val) ? val : [];
  }

  get data(): SwuiSelectOption[] {
    return this._data;
  }

  @Input()
  get value(): string[] {
    return this._value;
  }

  set value( val: string[] ) {
    this._value = val && Array.isArray(val) ? val : [];
    this.setSelectValue(this._value);
  }

  get empty() {
    return !this.value || !this.value.length;
  }

  readonly selectControl = new UntypedFormControl();
  readonly controlType = CONTROL_NAME;

  @ViewChild('input') input?: MatInput;
  @ViewChild(MatMenuTrigger, { static: true }) selectRef?: MatMenuTrigger;
  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;

  private _data: SwuiSelectOption[] = [];
  private _value: string[] = [];

  @HostBinding('class.floating')
  get shouldLabelFloat() {
    return this.selectControl.value;
  }

  constructor( fm: FocusMonitor,
               elRef: ElementRef<HTMLElement>,
               @Optional() @Self() ngControl: NgControl,
               @Optional() parentFormGroup: FormGroupDirective,
               errorStateMatcher: ErrorStateMatcher,
               private readonly translate: TranslateService ) {
    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);
  }

  onContainerClick( event: Event ): void {
    event.stopPropagation();
    if (this.elRef && this.selectRef && (event.target as Element).tagName.toLowerCase() !== 'input' && !this.disabled) {
      this.elRef.nativeElement.focus();
      this.selectRef.openMenu();
    }
  }

  writeValue( val: string[] | undefined ): void {
    this._value = val && Array.isArray(val) ? val : [];
    this.setSelectValue(this._value);
  }

  onCancel() {
    if (this.selectRef) {
      this.selectRef.closeMenu();
    }
  }

  onApply( data: string[] ) {
    this.setSelectValue(data);
    this._value = data;
    this.onChange(data);
    if (this.selectRef) {
      this.selectRef.closeMenu();
    }
  }

  protected onDisabledState( value: boolean ) {
    if (value) {
      this.selectControl.disable();
    } else {
      this.selectControl.enable();
    }
  }

  protected isErrorState(): boolean {
    if (this.input) {
      return this.input.errorState;
    }
    return false;
  }

  private setSelectValue( data: string[] ) {
    const multiMessage = this.translate.instant('COMPONENTS.MULTISELECT.multiple');
    const singleItem = this.data.find(( el: SwuiSelectOption ) => el.id === data[0]);
    const selectValue = data.length > 1 ? `(${multiMessage})` : (singleItem ? singleItem.text : '');
    this.selectControl.setValue(selectValue);
  }
}

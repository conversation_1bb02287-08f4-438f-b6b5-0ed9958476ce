<form [formGroup]="form" class="sw-start-time" [ngClass]="{'no-seconds': disableSeconds}">
  <div class="sw-start-time__control">
    <input
      #hours
      matInput
      type="text"
      (input)="processInputValue('hours')"
      class="sw-start-time__input"
      formControlName="hours">
  </div>
  <div class="sw-start-time__control">
    <input
      #minutes
      matInput
      type="text"
      (input)="processInputValue('minutes')"
      class="sw-start-time__input"
      formControlName="minutes">
  </div>
  <div class="sw-start-time__control" *ngIf="!disableSeconds">
    <input
      #seconds
      matInput
      type="text"
      (input)="processInputValue('seconds')"
      class="sw-start-time__input"
      formControlName="seconds">
  </div>
</form>


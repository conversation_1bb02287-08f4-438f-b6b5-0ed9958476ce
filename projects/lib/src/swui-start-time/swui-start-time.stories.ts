import { moduleMetadata, storiesOf } from '@storybook/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MatFormFieldModule } from '@angular/material/form-field';

import { SwuiStartTimeModule } from './swui-start-time.module';
import { SwuiStartTimeComponent } from './swui-start-time.component';


const template = `
  <mat-form-field appearance="outline">
    <mat-label>Test</mat-label>
    <lib-swui-start-time
      [value]="value"
      [required]="required"
      [disableSeconds]="disableSeconds"
      [disabled]="disabled">
    </lib-swui-start-time>
  </mat-form-field>
`;

const testValue = (1000 * 60 * 60 * 5) + (1000 * 60 * 4) + (1000 * 3);

storiesOf('Date/StartTime', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        SwuiStartTimeModule,
        MatFormFieldModule,
      ],
    })
  )
  .add('default', () => ({
    component: SwuiStartTimeComponent,
    template,
    props: {
      value: testValue
    },
  }))
  .add('disabled', () => ({
    component: SwuiStartTimeComponent,
    template,
    props: {
      value: testValue,
      disabled: true
    },
  }))
  .add('required', () => ({
    component: SwuiStartTimeComponent,
    template,
    props: {
      required: true
    },
  }))
  .add('disable seconds', () => ({
    component: SwuiStartTimeComponent,
    template,
    props: {
      disableSeconds: true
    },
  }));


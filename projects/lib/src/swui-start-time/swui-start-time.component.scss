:host.floating {
  .sw-start-time {
    &__control {
      &:after {
        opacity: 1;
        transition: opacity 0.1s ease-in-out;
      }
    }
  }
}

.sw-start-time {
  display: flex;
  width: 100%;
  &__control {
    position: relative;
    width: calc(100% / 3);
    margin: 0;
    padding: 0;
    &:after {
      content: ':';
      position: absolute;
      top: 0;
      right: -2px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 4px;
      height: 100%;
      opacity: 0;
    }
    &:last-child {
      &:after {
        display: none;
      }
    }
  }
  &__input {
    width: 100%;
    padding: 0 4px;
    text-align: center;
    &:focus {
      outline: none !important;
    }
  }
  &.no-seconds {
    .sw-start-time {
      &__control {
        width: calc( 100% / 2 );
      }
    }
  }
}

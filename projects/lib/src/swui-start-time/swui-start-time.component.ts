import { Component, ElementRef, HostBinding, Input, OnInit, Optional, Self, ViewChild } from '@angular/core';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, FormGroupDirective, NgControl } from '@angular/forms';
import { MatFormFieldControl } from '@angular/material/form-field';
import { coerceBooleanProperty } from '@angular/cdk/coercion';
import { FocusMonitor } from '@angular/cdk/a11y';
import moment from 'moment';
import { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';
import { ErrorStateMatcher } from '@angular/material/core';
import { takeUntil } from 'rxjs/operators';

interface StartTime {
  hours: string;
  minutes: string;
  seconds?: string;
}

const CONTROL_NAME = 'lib-swui-start-time';
let nextUniqueId = 0;

@Component({
    selector: 'lib-swui-start-time',
    templateUrl: './swui-start-time.component.html',
    styleUrls: ['./swui-start-time.component.scss'],
    providers: [{ provide: MatFormFieldControl, useExisting: SwuiStartTimeComponent }],
    standalone: false
})
export class SwuiStartTimeComponent extends SwuiMatFormFieldControl<number | undefined> implements OnInit {
  @Input()
  get value(): number | undefined {
    return this._value;
  }

  set value( value: number | undefined ) {
    if (!value) {
      return;
    }
    this._value = value;
    this.patchForm(this._value);
    this.stateChanges.next(undefined);
  }

  @Input()
  set disableSeconds( val: boolean ) {
    this._disableSeconds = coerceBooleanProperty(val);
    if (!this._disableSeconds && !this.form.disabled) {
      this.secondsControl.enable();
    } else {
      this.secondsControl.disable();
    }
  }

  get disableSeconds(): boolean {
    return this._disableSeconds;
  }

  get empty() {
    return !this.hoursControl.value && !this.minutesControl.value && !this.secondsControl.value;
  }

  readonly controlType = CONTROL_NAME;
  readonly form: UntypedFormGroup;

  @ViewChild('hours') hoursInput?: ElementRef;
  @ViewChild('minutes') minutesInput?: ElementRef;
  @ViewChild('seconds') secondsInput?: ElementRef;

  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;

  @HostBinding('class.floating')
  get shouldLabelFloat() {
    return this.focused || !this.empty;
  }

  private _value: number | undefined;
  private _disableSeconds = false;

  constructor( fm: FocusMonitor,
               elRef: ElementRef<HTMLElement>,
               @Optional() @Self() ngControl: NgControl,
               @Optional() parentFormGroup: FormGroupDirective,
               errorStateMatcher: ErrorStateMatcher,
               fb: UntypedFormBuilder ) {
    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);
    this.form = fb.group({
      hours: ['00'],
      minutes: ['00'],
      seconds: ['00']
    });
  }

  ngOnInit() {
    this.form.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(( val: StartTime ) => {
      this._value = this.processForm(val);
      this.onChange(this._value);
    });
  }

  onContainerClick( event: Event ) {
    event.stopPropagation();
    if (this.hoursInput && (event.target as Element).tagName.toLowerCase() !== 'input' && !this.disabled) {
      this.hoursInput.nativeElement.focus();
    }
  }

  writeValue( value: number ): void {
    if (!value) {
      return;
    }
    this._value = value;
    this.patchForm(value);
  }

  processInputValue( el: string ) {
    const control = this.form.get(el) as UntypedFormControl;
    const value = control.value;
    const maxLength = 2;
    const maxValue = 59;
    const maxHoursValue = 23;
    const reg = /^\d+$/;

    if (!value || value === '0') {
      control.setValue('00');
    }

    if (value && value.length > maxLength && value.toString().charAt(0) === '0') {
      control.setValue(value.substr(1));
      if (el !== 'hours' && value > maxValue) {
        control.setValue(maxValue.toString());
      } else if (el === 'hours' && value > maxValue) {
        control.setValue(maxHoursValue.toString());
      }
    }

    if (value && value > maxValue && el !== 'hours' && value.toString().charAt(0) !== '0') {
      control.setValue(maxValue.toString());
    }

    if (value && value > maxHoursValue && el === 'hours') {
      control.setValue(maxHoursValue.toString());
    }

    if (value && !value.toString().match(reg)) {
      control.setValue(value.replace(/[^\d,]/g, ''));
    }
  }

  get hoursControl(): UntypedFormControl {
    return this.form.get('hours') as UntypedFormControl;
  }

  get minutesControl(): UntypedFormControl {
    return this.form.get('minutes') as UntypedFormControl;
  }

  get secondsControl(): UntypedFormControl {
    return this.form.get('seconds') as UntypedFormControl;
  }

  protected onDisabledState( disabled: boolean ) {
    if (disabled) {
      this.form.disable();
    } else {
      this.form.enable();
    }
  }

  protected isErrorState(): boolean {
    if (this.hoursInput && this.minutesInput && this.secondsInput) {
      return this.hoursInput.nativeElement.errorState ||
        this.minutesInput.nativeElement.errorState ||
        this.secondsInput.nativeElement.errorState;
    }
    return false;
  }

  private patchForm( value: number ) {
    const processValue = ( v: number ): string => v < 10 ? '0' + v.toString() : v.toString();
    const duration = moment.duration(value, 'milliseconds');
    this.form.patchValue({
      hours: processValue(duration.hours()),
      minutes: processValue(duration.minutes()),
      seconds: processValue(duration.seconds())
    });
  }

  private processForm( val: StartTime ): number | undefined {
    const hours = val.hours ? parseInt(val.hours, 10) : undefined;
    const minutes = val.minutes ? parseInt(val.minutes, 10) : undefined;
    const seconds = val.seconds && !this.disableSeconds ? parseInt(val.seconds, 10) : undefined;

    return hours !== undefined || minutes !== undefined || seconds !== undefined ?
      moment.duration(hours, 'hours').asMilliseconds() +
      moment.duration(minutes, 'minutes').asMilliseconds() +
      moment.duration(seconds, 'seconds').asMilliseconds() :
      undefined;
  }
}

import { DebugElement } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';

import { SwuiDateRangeComponent } from './swui-date-range.component';
import { SWUI_DATE_RANGE } from './swui-date-range.module';
import { SwuiDateRange, SwuiDateRangeModel } from './swui-date-range.model';
import { SwuiDatePickerConfig, SwuiDatePickerConfigModel } from '../swui-date-picker/swui-date-picker-config.model';

function createFakeEvent( type: string ) {
  const event = document.createEvent('Event');
  event.initEvent(type, true, true);
  return event;
}

function dispatchFakeEvent( node: Node | Window, type: string ) {
  node.dispatchEvent(createFakeEvent(type));
}

describe('SwuiDateRangeComponent', () => {
  let component: SwuiDateRangeComponent;
  let fixture: ComponentFixture<SwuiDateRangeComponent>;
  let testValue: SwuiDateRange;
  let host: DebugElement;
  let testConfig: SwuiDatePickerConfig;
  let defaultConfig: SwuiDatePickerConfig;
  let expectedFormattedString: string;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [SwuiDateRangeComponent],
      imports: [
        BrowserAnimationsModule,
        TranslateModule.forRoot(),
        ...SWUI_DATE_RANGE
      ]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiDateRangeComponent);
    component = fixture.componentInstance;
    host = fixture.debugElement;
    testValue = new SwuiDateRangeModel({
      from: '2020-08-10T00:00:00.000Z',
      to: '2020-08-20T00:00:00.000Z'
    });
    testConfig = {
      dateFormat: 'MM:DD',
      timeFormat: 'hh:mm z',
      timeZone: 'Asia/Taipei',
      timePicker: true
    };
    defaultConfig = {
      dateFormat: 'DD.MM.YYYY',
      timeFormat: 'HH:mm:ss',
      timePicker: false,
      timeDisableLevel: undefined,
      timeZone: undefined
    };
    expectedFormattedString = '10.08.2020 - 20.08.2020';

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set value', () => {
    component.value = testValue;
    expect(component.value).toEqual(testValue);
    expect(component.valueControl.value).toBe(expectedFormattedString);
  });

  it('should writeValue', () => {
    component.writeValue(testValue);
    expect(component.value).toEqual(testValue);
    expect(component.valueControl.value).toBe(expectedFormattedString);
  });

  it('should set required', () => {
    expect(component.required).toBeFalsy();

    component.required = true;
    expect(component.required).toBeTruthy();
  });

  it('should set disabled', () => {
    expect(component.disabled).toBeFalsy();
    expect(component.valueControl.disabled).toBeFalsy();

    component.disabled = true;
    expect(component.disabled).toBeTruthy();
    expect(component.valueControl.disabled).toBeTruthy();
  });

  it('should setDisabledState', () => {
    expect(component.disabled).toBeFalsy();
    expect(component.valueControl.disabled).toBeFalsy();

    component.setDisabledState(true);
    expect(component.disabled).toBeTruthy();
    expect(component.valueControl.disabled).toBeTruthy();
  });

  it('should get empty true if controls are empty', () => {
    expect(component.empty).toBeTruthy();

    component.value = testValue;
    expect(component.empty).toBeFalsy();
  });

  it('should set placeholder', () => {
    expect(component.placeholder).toBe('');

    component.placeholder = 'test';
    expect(component.placeholder).toBe('test');
  });

  it('should get error state false', () => {
    expect(component.errorState).toBeFalsy();
  });

  it('should init controls', () => {
    expect(component.valueControl).toBeDefined();
  });

  it('should set state changes', () => {
    const nextSpy = spyOn(component.stateChanges, 'next');
    component.required = true;
    expect(nextSpy).toHaveBeenCalled();
  });

  it('should set host id', () => {
    expect(host.nativeElement.getAttribute('id')).toBeDefined();
  });

  it('should complete state change on destroy', () => {
    const completeSpy = spyOn(component.stateChanges, 'complete');
    component.ngOnDestroy();
    expect(completeSpy).toHaveBeenCalled();
  });

  it('should setDescribedByIds', () => {
    const testIds = ['test1', 'test2'];
    component.setDescribedByIds(testIds);
    expect(component.describedBy).toBe(testIds.join(' '));
  });

  it('should set onChange in registerOnChange', () => {
    let test = false;
    const fn = () => {
      test = true;
    };
    component.registerOnChange(fn);
    component.apply(new MouseEvent('click'));
    expect(test).toBe(true);
  });

  it('should set onChange in registerOnTouched', () => {
    let test = false;
    const fn = () => {
      test = true;
    };
    component.registerOnTouched(fn);
    dispatchFakeEvent(host.nativeElement, 'focus');
    dispatchFakeEvent(host.nativeElement, 'blur');
    component.apply(new MouseEvent('click'));
    expect(test).toBe(true);
  });

  it('should call onTouched on focus', () => {
    spyOn(component, 'onTouched');
    dispatchFakeEvent(host.nativeElement, 'focus');
    dispatchFakeEvent(host.nativeElement, 'blur');
    expect(component.onTouched).toHaveBeenCalled();
  });

  it('should set config', () => {
    expect(component.config).toEqual(new SwuiDatePickerConfigModel(defaultConfig));

    component.config = testConfig;
    expect(component.config).toEqual(new SwuiDatePickerConfigModel(testConfig));
  });

  it('should format value', () => {
    component.value = testValue;
    expect(component.valueControl.value).toBe(expectedFormattedString);

    component.config = testConfig;
    expect(component.valueControl.value).toBe('08:10 08:00 CST - 08:20 08:00 CST');
  });

  it('should cancel', () => {
    component.value = testValue;
    component.form.setValue({ from: '2020-08-10T00:00:00.000Z', to: '2020-08-20T00:00:00.000Z' });
    component.cancel(createFakeEvent('click'));
    expect(component.menuTriggerRef ? component.menuTriggerRef.menuOpen : true).toBeFalsy();
    expect(component.value.toString()).toEqual(testValue.toString());
  });

  it('should apply', () => {
    component.value = testValue;
    const newValue = { from: '2020-08-10T00:00:00.000Z', to: '2020-08-20T00:00:00.000Z' };
    component.form.setValue(newValue);
    component.apply(createFakeEvent('click'));
    expect(component.menuTriggerRef ? component.menuTriggerRef.menuOpen : true).toBeFalsy();
    expect(component.value.toString()).toEqual(newValue.toString());
  });

  it('should clear', () => {
    component.value = testValue;
    component.clear(createFakeEvent('click'));
    expect(component.form.value).toEqual({ from: null, to: null });
    component.apply(createFakeEvent('click'));
    expect(component.value.toString()).toEqual({ from: '', to: '' }.toString());
  });

  it('should patch form onPeriodSelect', () => {
    const testPeriod = component.customPeriods[0].fn;
    component.onPeriodSelect(testPeriod);

    expect(component.form.value).toEqual({ from: testPeriod().from, to: testPeriod().to });
  });

  it('should set selectedIndex onSelectedIndexChange', () => {
    component.onSelectedIndexChange(1);

    expect(component.selectedIndex).toBe(1);
  });

});

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

import { SwuiDateRangeComponent } from './swui-date-range.component';
import { SwuiDateTimeChooserModule } from '../swui-date-time-chooser/swui-date-time-chooser.module';
import { MatMenuModule } from '@angular/material/menu';
import { MatInputModule } from '@angular/material/input';
import { MatTabsModule } from '@angular/material/tabs';
import { MatButtonModule } from '@angular/material/button';
import { MatRippleModule } from '@angular/material/core';


export const SWUI_DATE_RANGE = [
  ReactiveFormsModule,
  MatInputModule,
  MatMenuModule,
  MatTabsModule,
  MatButtonModule,
  MatRippleModule,
  SwuiDateTimeChooserModule,
];

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ...SWUI_DATE_RANGE,
  ],
  declarations: [
    SwuiDateRangeComponent,
  ],
  exports: [
    SwuiDateRangeComponent,
  ]
})
export class SwuiDateRangeModule {
}

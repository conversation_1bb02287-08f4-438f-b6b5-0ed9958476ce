$color-highlight: #EDF3FF;
$color-link: #1468cf;
$color-border: #D9DBDF;
$color-text: #2a2c44;

.swui-date-range {
  &__footer {
    border-top: 1px solid $color-border;
  }
  &__header,
  &__footer {
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 12px;
  }
  &__header,
  &__footer {
    font-size: 14px;
  }
  &__header {
    border-bottom: 1px solid $color-border;
  }
  &__body {
    padding: 16px;
  }
  &__title {
    font-weight: 500;
  }
  &__clear {
    margin: 0 -4px 0 auto;
    font-size: 12px;
    line-height: 24px;
    border-radius: 4px;
    padding: 0 4px;
    color: $color-link;
    cursor: pointer;
    letter-spacing: 0.0892857em;
  }
  &__actions {
    display: flex;
    margin-left: auto;
  }
  &__button {
    min-width: auto;
    height: 24px;
    padding: 0 10px;
    font-size: 12px;
    line-height: 24px;
    font-weight: 400;
    text-transform: initial !important;
  }
}

.custom {
  font-size: 12px;
  line-height: 1;
  &__item {
    display: flex;
    align-items: center;
    height: 28px;
    padding: 0 12px;
    cursor: pointer;
    &.selected {
      background-color: $color-highlight;
    }
  }
}

.between {
  display: flex;
  &__item {
    padding: 0 12px;
  }
}

<input
  #input
  matInput
  type="text"
  [ngClass]="{'time-range': config?.timePicker && !!value?.from && !!value?.to}"
  #date="matMenuTrigger"
  [formControl]="valueControl"
  readonly="readonly"
  [matMenuTriggerFor]="menu"
  (menuClosed)="onMenuClose()"
  (menuOpened)="onMenuOpen()">

<mat-menu #menu="matMenu" class="swui-date-range" [ngClass]="customClass">
  <div class="swui-date-range__wrapper" (click)="prevent($event)">
    <div class="swui-date-range__header">
      <div class="swui-date-range__title">{{title | translate}}</div>
      <div class="swui-date-range__clear" mat-ripple matRippleColor="rgba(19, 115, 213, 0.2)"  (click)="clear($event)">
        {{'COMPONENTS.DATE_RANGE.clear' | translate}}
      </div>
    </div>
    <div class="swui-date-range__body">
      <mat-tab-group
        #tabSet
        [animationDuration]="0"
        (selectedIndexChange)="onSelectedIndexChange($event)">
        <mat-tab label="Time period">
          <div class="custom">
            <div
              *ngFor="let period of customPeriods"
              class="custom__item"
              matRipple
              [ngClass]="{'selected': isSelected(period)}"
              (click)="onPeriodSelect(period.fn)">
              {{period.title | translate}}
            </div>
          </div>
        </mat-tab>

        <mat-tab label="Between dates">
          <form [formGroup]="form" class="between">
            <div class="between__item">
              <lib-swui-date-time-chooser
                [timePicker]="config?.timePicker"
                [timeZone]="config?.timeZone"
                [timeDisableLevel]="config?.timeDisableLevel"
                [isFromRange]="true"
                [formControl]="fromControl"
                [toDate]="toControl.value"
                [minDate]="processedMinDate"
                [maxDate]="processedMaxDate">
              </lib-swui-date-time-chooser>
            </div>
            <div class="between__item">
              <lib-swui-date-time-chooser
                [timePicker]="config?.timePicker"
                [timeZone]="config?.timeZone"
                [timeDisableLevel]="config?.timeDisableLevel"
                [isToRange]="true"
                [formControl]="toControl"
                [fromDate]="fromControl.value"
                [chooseStart]="config?.chooseStart"
                [minDate]="processedMinDate"
                [maxDate]="processedMaxDate">
              </lib-swui-date-time-chooser>
            </div>
          </form>
        </mat-tab>
      </mat-tab-group>
    </div>
    <div class="swui-date-range__footer">
      <div class="swui-date-range__actions">
        <button
          mat-button
          color="primary"
          (click)="cancel($event)"
          class="swui-date-range__button swui-date-range__button--cancel">
          {{'COMPONENTS.DATE_RANGE.cancel' | translate}}
        </button>
        <button
          mat-flat-button
          color="primary"
          (click)="apply($event)"
          class="swui-date-range__button">
          {{'COMPONENTS.DATE_RANGE.apply' | translate}}
        </button>
      </div>
    </div>
  </div>


</mat-menu>

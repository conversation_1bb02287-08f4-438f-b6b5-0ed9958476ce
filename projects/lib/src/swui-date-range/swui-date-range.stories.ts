import type { <PERSON>a, StoryObj } from '@storybook/angular';
import { moduleMetadata } from '@storybook/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import moment from 'moment';
import { I18nModule } from '../i18n.module';

import { SwuiDateRangeModule } from './swui-date-range.module';
import { SwuiDateRangeComponent } from './swui-date-range.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';

const EN = require('./locale.json');

export const template = `
  <mat-card style="margin: 32px">
    <mat-form-field appearance="outline" style="width: 500px">
    <mat-label>Test control label</mat-label>
    <lib-swui-date-range
      [title]="title"
      [config]="config"
      [disabled]="disabled"
      [minDate]="minDate"
      [maxDate]="maxDate"
      [customPeriods]="config?.customPeriods"
      [value]="value">
    </lib-swui-date-range>
    <mat-icon matPrefix>search</mat-icon>
  </mat-form-field>
</mat-card>
`;


const meta: Meta<SwuiDateRangeComponent> = {
  title: 'Date/Date Range',
  component: SwuiDateRangeComponent,
  decorators: [
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        I18nModule.forRoot({ translations: { en: EN } }),
        SwuiDateRangeModule,
        MatFormFieldModule,
        MatCardModule,
        MatIconModule,
      ],
    })
  ],
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<SwuiDateRangeComponent>;

export const Default: Story = {
  args: {
    title: 'Test title'
  },
  render: (args) => ({
    props: args,
    template: template,
  }),
};

export const WithValue: Story = {
  args: {
    title: 'Test title',
    value: { from: '2020-08-13T23:00:00.000Z', to: '2020-08-21T23:00:00.000Z' }
  },
  render: (args) => ({
    props: args,
    template: template,
  }),
};

export const WithConfig: Story = {
  args: {
    title: 'Test title',
    value: { from: '2020-08-13T00:00:00.000Z', to: '2020-08-21T00:00:00.000Z' },
    config: {
      dateFormat: 'YY/MM/DD',
      timePicker: true,
      timeFormat: 'HH:mm z',
      timeZone: 'Asia/Taipei',
      timeDisableLevel: {
        hour: true,
        minute: false,
        second: false
      }
    }
  },
  render: (args) => ({
    props: args,
    template: template,
  }),
};

export const Disabled: Story = {
  args: {
    title: 'Test title',
    value: { from: '2020-08-13T23:00:00.000Z', to: '2020-08-21T23:00:00.000Z' },
    disabled: true
  },
  render: (args) => ({
    props: args,
    template: template,
  }),
};

export const MinMax: Story = {
  args: {
    title: 'Test title',
    minDate: '2020-08-10T00:00:00.000Z',
    maxDate: '2020-08-28T00:00:00.000Z'
  },
  render: (args) => ({
    props: args,
    template: template,
  }),
};

export const MaxMonthPeriod: Story = {
  args: {
    title: 'Test title',
    value: { from: '2022-05-15T11:00:00.000Z', to: '2022-06-15T10:59:59.000Z' },
    config: {
      maxPeriod: 'month',
      timePicker: true,
      timeDisableLevel: {
        hour: true,
        minute: true,
        second: true
      },
      timeZone: 'Asia/Tbilisi'
    },
  },
  render: (args) => ({
    props: args,
    template: template,
  }),
};

export const CustomPeriods: Story = {
  args: {
    title: 'Test title',
    config: {
      // Note: customPeriods configuration would go here
      // but is commented out due to type compatibility issues
    }
  },
  render: (args) => ({
    props: args,
    template: template,
  }),
};


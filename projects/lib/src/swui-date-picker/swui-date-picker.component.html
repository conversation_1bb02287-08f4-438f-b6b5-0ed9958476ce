<input
  matInput
  type="text"
  [placeholder]="placeholder | translate"
  [formControl]="inputControl"
  [matMenuTriggerFor]="menu"
  (menuOpened)="onOpen()"
  readonly>

<mat-menu #menu="matMenu" class="swui-date-picker-menu">
  <div class="swui-date-picker" [ngClass]="{'timepicker': config?.timePicker}" (click)="prevent($event)">
    <div class="swui-date-picker__header">
      <div class="swui-date-picker__title">{{title | translate}}</div>
      <div class="swui-date-picker__clear" mat-ripple matRippleColor="rgba(19, 115, 213, 0.2)"  (click)="clear($event)">
        {{'COMPONENTS.DATE_RANGE.clear' | translate}}
      </div>
    </div>
    <div class="swui-date-picker__body">
      <lib-swui-date-time-chooser
        [formControl]="dateControl"
        [minDate]="minDate"
        [maxDate]="maxDate"
        [timePicker]="config?.timePicker"
        [timeZone]="config?.timeZone"
        [timeDisableLevel]="config?.timeDisableLevel">
      </lib-swui-date-time-chooser>
    </div>
    <div class="swui-date-picker__footer">
      <div class="swui-date-picker__actions">
        <button
          mat-button
          color="primary"
          (click)="cancel($event)"
          class="swui-date-picker__button swui-date-picker__button--cancel">
          {{'COMPONENTS.DATE_RANGE.cancel' | translate}}
        </button>
        <button
          mat-flat-button
          color="primary"
          (click)="apply($event)"
          class="swui-date-picker__button">
          {{'COMPONENTS.DATE_RANGE.apply' | translate}}
        </button>
      </div>
    </div>
  </div>

</mat-menu>


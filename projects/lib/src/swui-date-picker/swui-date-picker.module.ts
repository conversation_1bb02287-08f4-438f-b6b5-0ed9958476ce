import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';

import { SwuiDatePickerComponent } from './swui-date-picker.component';
import { SwuiDateTimeChooserModule } from '../swui-date-time-chooser/swui-date-time-chooser.module';
import { MatMenuModule } from '@angular/material/menu';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatRippleModule } from '@angular/material/core';


export const DATE_PICKER_MODULES = [
  SwuiDateTimeChooserModule,
  ReactiveFormsModule,
  MatMenuModule,
  MatInputModule,
  MatButtonModule,
  MatRippleModule,
];

@NgModule({
  declarations: [SwuiDatePickerComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ...DATE_PICKER_MODULES,
  ],
  exports: [
    SwuiDatePickerComponent,
  ]
})

export class SwuiDatePickerModule {
}

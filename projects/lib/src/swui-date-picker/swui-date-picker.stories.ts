import { FormsModule } from '@angular/forms';
import { moduleMetadata, storiesOf } from '@storybook/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { DATE_PICKER_MODULES, SwuiDatePickerModule } from './swui-date-picker.module';
import { SwuiDatePickerComponent } from './swui-date-picker.component';
import { I18nModule } from '../i18n.module';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCardModule } from '@angular/material/card';

const EN = require('./locale.json');

const template = `
  <mat-card style="margin: 32px">
    <mat-form-field appearance="outline">
      <mat-label>Test label</mat-label>
      <lib-swui-date-picker
        [title]="'Title'"
        [disabled]="disabled"
        [minDate]="minDate"
        [maxDate]="maxDate"
        [config]="config"
        [(ngModel)]="value">
      </lib-swui-date-picker>
    </mat-form-field>
  </mat-card>
`;

storiesOf('Date/DatePicker', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        FormsModule,
        BrowserAnimationsModule,
        I18nModule.forRoot({ translations: { en: EN } }),
        SwuiDatePickerModule,
        ...DATE_PICKER_MODULES,
        MatCardModule,
        MatFormFieldModule,
      ],
    })
  )
  .add('default', () => ({
    component: SwuiDatePickerComponent,
    template,
    props: {},
  }))
  .add('timepicker', () => ({
    component: SwuiDatePickerComponent,
    template,
    props: {
      config: {
        timePicker: true,
        timeFormat: 'HH:mm z'
      }
    },
  }))
  .add('timezone +8', () => ({
    component: SwuiDatePickerComponent,
    template,
    props: {
      config: {
        timePicker: true,
        timeFormat: 'HH:mm z',
        timeZone: 'Asia/Taipei'
      }
    },
  }))
  .add('value timezone +8', () => ({
    component: SwuiDatePickerComponent,
    template,
    props: {
      value: '2020-07-03T00:00:00.000Z',
      config: {
        timePicker: true,
        timeFormat: 'HH:mm z',
        timeZone: 'Asia/Taipei'
      }
    },
  }))
  .add('disabled', () => ({
    component: SwuiDatePickerComponent,
    template,
    props: {
      value: '2020-07-03T00:00:00.000Z',
      disabled: true,
      config: {
        timePicker: true,
        timeFormat: 'HH:mm z',
      }
    },
  }))
  .add('min max', () => ({
    component: SwuiDatePickerComponent,
    template,

    props: {
      minDate: '2020-08-13T00:00:00.000Z',
      maxDate: '2020-08-23T00:00:00.000Z',
    },
  }));

import { unitOfTime } from 'moment';
import { SwuiTimepickerTimeDisableLevel } from '../swui-timepicker/swui-timepicker.interface';

export interface SwuiDatePickerConfig {
  dateFormat?: string;
  timePicker?: boolean;
  timeDisableLevel?: SwuiTimepickerTimeDisableLevel;
  timeFormat?: string;
  timeZone?: string;
  maxPeriod?: unitOfTime.Base;
  chooseStart?: boolean;
}

export class SwuiDatePickerConfigModel {
  dateFormat?: string;
  timePicker?: boolean;
  timeDisableLevel?: SwuiTimepickerTimeDisableLevel;
  timeFormat?: string;
  timeZone?: string;
  maxPeriod?: unitOfTime.Base;
  chooseStart?: boolean;

  constructor(config: SwuiDatePickerConfig | undefined) {
    this.dateFormat = config && config.dateFormat ? config.dateFormat : 'DD.MM.YYYY';
    this.timeFormat = config && config.timeFormat ? config.timeFormat : 'HH:mm:ss';
    this.timePicker = config && config.timePicker ? config.timePicker : false;
    this.timeDisableLevel = config && config.timeDisableLevel ? config.timeDisableLevel : undefined;
    this.timeZone = config && config.timeZone ? config.timeZone : undefined;
    this.maxPeriod = config?.maxPeriod;
    this.chooseStart = config?.chooseStart || false;
  }
}

import { moduleMetadata, storiesOf } from '@storybook/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { SwuiChipsAutocompleteComponent } from './swui-chips-autocomplete.component';
import { SwuiChipsAutocompleteModule } from './swui-chips-autocomplete.module';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';


export const template = `
  <mat-form-field appearance="outline">
    <mat-label>Test label</mat-label>
    <lib-swui-chips-autocomplete
      [value]="value"
      [disabled]="disabled"
      [required]="required"
      [items]="items">
    </lib-swui-chips-autocomplete>
    <mat-icon matSuffix>search</mat-icon>
  </mat-form-field>
`;
export const items = [
  { id: 'one', text: 'One' },
  { id: 'two', text: 'Two' },
  { id: 'three', text: 'Three' },
  { id: 'four', text: 'Four', disabled: true },
  { id: 'five', text: 'Five', disabled: true }
];

storiesOf('Chips Autocomplete', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        SwuiChipsAutocompleteModule,
        MatFormFieldModule,
        MatIconModule,
      ],
    })
  )
  .add('Chips Autocomplete with Options', () => ({
    component: SwuiChipsAutocompleteComponent,
    props: {
      items
    },
    template
  }))
  .add('Chips Autocomplete disabled', () => ({
    template,
    component: SwuiChipsAutocompleteComponent,
    props: {
      value: ['one'],
      disabled: true,
      items
    }
  }))
  .add('Chips Autocomplete required', () => ({
    template,
    component: SwuiChipsAutocompleteComponent,
    props: {
      value: [],
      required: true,
      items
    }
  }));

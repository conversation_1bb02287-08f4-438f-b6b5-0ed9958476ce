<mat-chip-set #chipSet [disabled]="disabled" class="scroll-chip-list">

  <mat-chip
    *ngFor="let item of selectedItems"
    [disabled]="itemsMap.get(item) && itemsMap.get(item).disabled"
    (removed)="remove(item)">
    {{ getItemText(item) }}
    <mat-icon matChipRemove>cancel</mat-icon>
  </mat-chip>

  <input
    #input
    (blur)="closePanel()"
    (click)="onInputClick()"
    [formControl]="inputFormControl"
    [matAutocomplete]="auto"
    [matChipInputFor]="chipSet"
    [matChipInputSeparatorKeyCodes]="separatorKeysCodes">

</mat-chip-set>

<mat-autocomplete #auto="matAutocomplete" (optionSelected)="clear()">
  <mat-option *ngIf="inputFormControl.value && !hasFounded && !!addFn"
              [value]="inputFormControl.value"
              (mousedown)="onMenuItemMousedown()"
              (onSelectionChange)="add($event)">
    {{ inputFormControl.value }} <span class="add-tag">(New)</span>
  </mat-option>
  <mat-option
    *ngFor="let item of filteredItems | async"
    [value]="item"
    (mousedown)="onMenuItemMousedown()"
    [disabled]="itemsMap.get(item.id) && (itemsMap.get(item.id).selected || itemsMap.get(item.id).disabled)"
    (onSelectionChange)="selected($event)">
    {{ item.text }}
  </mat-option>

</mat-autocomplete>

import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';

import { SwuiChipsAutocompleteComponent } from './swui-chips-autocomplete.component';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';

export const CHIPS_AUTOCOMPLETE_MODULES = [
  MatAutocompleteModule,
  MatButtonModule,
  MatChipsModule,
  MatIconModule,
  MatInputModule,
  ReactiveFormsModule
];

@NgModule({
  declarations: [
    SwuiChipsAutocompleteComponent
  ],
  exports: [
    SwuiChipsAutocompleteComponent
  ],
  imports: [
    CommonModule,
    ...CHIPS_AUTOCOMPLETE_MODULES
  ]
})
export class SwuiChipsAutocompleteModule {
}

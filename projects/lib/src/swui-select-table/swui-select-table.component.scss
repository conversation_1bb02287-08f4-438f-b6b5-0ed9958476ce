.swui-select-table {
  position: relative;
  border: 1px solid rgba(0,0,0,.12);
  border-radius: 4px;

  .loader-wrapper {
    display: flex;
    justify-content: center;
    padding: 18px 0;
  }

  &__disabled {
    opacity: .6;

    .table {
      pointer-events: none;
    }
  }

  &__header {
    display: flex;
    border-bottom: 1px solid rgba(0,0,0,.12);
    padding: 6px 18px;

    mat-form-field {
      width: 250px;
    }

    button {
      font-size: 14px;
    }
  }

  .table {
    display: flex;
    flex-flow: column wrap;

    &-row {
      display: flex;
      align-items: center;

      &:nth-child(odd):not(&__header) {
        background-color: #eef1f5;
      }

      mat-checkbox {
        padding: 0 18px;
      }

      &__header {
        background-color: white;
        font-weight: bold;
      }
    }

    &-cell {
      flex: 1;
      display: flex;
      align-items: center;
      height: 100%;
      padding: 0 18px;
    }
  }

  &__viewport {
    height: 100%;
    max-height: 210px;
  }
}

.selected {
  color: #1468cf;
}

.selected-single {
  color: #1468cf;
  background: #e0e0e0;
}

.disabled-option {
  pointer-events: none;
  opacity: .5;
}

.inline {
  display: inline;
}

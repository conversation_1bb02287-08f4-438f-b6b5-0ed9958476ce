import { CommonModule } from '@angular/common';
import { Component, DebugElement, ViewChild } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UntypedFormControl } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiSelectTableComponent } from './swui-select-table.component';
import { SwuiSelectTableOption } from './swui-select-table.interface';
import { SELECT_TABLE_MODULES } from './swui-select-table.module';

const options = [
  { id: '1', text: 'Solo Option1' },
  { id: '2', text: 'Test Option2' },
  { id: '3', text: 'Option3', disabled: true },
  { id: '4', text: 'Test Option4' },
  { id: '5', text: 'Option5' },
];

@Component({
    template: `
    <lib-swui-select-table [data]="data" [formControl]="control" [loading]="loading"></lib-swui-select-table>
  `,
    standalone: false
})
class SelectTableComponent {
  data: any[] = options;
  control = new UntypedFormControl();
  loading = false;
  @ViewChild(SwuiSelectTableComponent, { static: true }) select: SwuiSelectTableComponent | undefined;
}

describe('SwuiSelectTableComponent', () => {
  let component: SwuiSelectTableComponent;
  let fixture: ComponentFixture<SwuiSelectTableComponent>;
  let testOptions: SwuiSelectTableOption[] = [];
  let testValue: string[];
  let testColumns: any[];
  let selectControl: UntypedFormControl;
  let host: DebugElement;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        CommonModule,
        NoopAnimationsModule,
        TranslateModule.forRoot(),
        ...SELECT_TABLE_MODULES
      ],
      declarations: [SwuiSelectTableComponent, SelectTableComponent]
    });
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiSelectTableComponent);
    component = fixture.componentInstance;
    host = fixture.debugElement;
    testColumns = [{
      field: 'id',
      name: 'First'
    }, {
      field: 'text',
      name: 'Second'
    }];
    testOptions = options;
    testValue = ['2'];
    component.data = testOptions;
    component.columns = testColumns;
    selectControl = component.selectControl;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set value', () => {
    component.value = testValue;
    expect(component.value).toEqual(testValue);
    expect(selectControl.value).toEqual(testOptions.filter(opt => opt.id === testValue[0]));
  });

  it('should set disabled', () => {
    component.disabled = true;
    expect(component.disabled).toBeTruthy();

    component.disabled = false;
    expect(component.disabled).toBeFalsy();
  });

  it('should set empty', () => {
    expect(component.empty).toBeTruthy();

    component.value = testValue;
    expect(component.empty).toBeFalsy();
  });

  it('should controlType to be defined', () => {
    expect(component.controlType).toBe('lib-swui-select-table');
  });

  it('should set host id', () => {
    expect(host.nativeElement.getAttribute('id')).toBeDefined();
  });

  it('should filter', () => {
    component.showSearch = true;
    component.ngOnInit();

    component.searchControl.setValue(testValue[0]);
    expect(component.options).toEqual([testOptions[1]]);

    component.searchControl.setValue('99');
    expect(component.options).toEqual([]);
  });

  it('should call onChange onInit when selected value changed', () => {
    spyOn(component, 'onChange');
    component.ngOnInit();
    selectControl.setValue(testOptions);
    expect(component.onChange).toHaveBeenCalled();
  });

  it('should set onChange in registerOnChange', () => {
    component.ngOnInit();
    let test = false;
    const fn = () => {
      test = true;
    };
    component.registerOnChange(fn);
    selectControl.setValue(testOptions);
    expect(test).toBe(true);
  });

  it('should setDisabledState', () => {
    component.setDisabledState(true);
    expect(component.disabled).toBeTruthy();

    component.setDisabledState(false);
    expect(component.disabled).toBeFalsy();
  });

  it('should write value', () => {
    component.writeValue(testValue);
    expect(component.value).toEqual(testValue);
  });

  it('should set disableEmptyOption', () => {
    expect(component.disableEmptyOption).toBe(false);
  });

  it('should set showSearch', () => {
    expect(component.showSearch).toBe(false);
  });

  it('should set searchPlaceholder', () => {
    expect(component.searchPlaceholder).toBe('Search');
  });

  it('should set multiple value', () => {
    component.writeValue(['1', '2']);
    expect(component.value).toEqual(['1', '2']);
  });

  it('should toggleAll', () => {
    component.toggleAll();
    expect(component.value).toEqual(['1', '2', '4', '5']);
    component.toggleAll();
    expect(component.value).toEqual([]);
  });

  it('should set disableAllOption', () => {
    component.disableAllOption = true;
    expect(component.disableAllOption).toBeTruthy();
  });

  it('should set loading', () => {
    fixture.destroy();
    const multiFixture = TestBed.createComponent(SelectTableComponent);
    const instance = multiFixture.componentInstance;
    instance.loading = true;
    multiFixture.detectChanges();
    expect(multiFixture.nativeElement.querySelector('lib-swui-progress-container')).toBeTruthy();
    instance.loading = false;
    multiFixture.detectChanges();
    expect(multiFixture.nativeElement.querySelector('lib-swui-progress-container')).toBeNull();
  });

  it('should set view selected', () => {
    component.writeValue(testValue);
    expect(component.options.length).toEqual(testOptions.length);
    component.showSelected();
    expect(component.options.length).toEqual(testValue.length);
    component.showSelected();
    expect(component.options.length).toEqual(testOptions.length);
  });
});

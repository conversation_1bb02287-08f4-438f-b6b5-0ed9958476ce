<div class="swui-select-table" [class.swui-select-table__disabled]="disabled || loading">
  <div class="swui-select-table__header">
    <mat-form-field *ngIf="showSearch" appearance="outline" class="no-field-padding">
      <input matInput
             type="text"
             placeholder="Search"
             [formControl]=searchControl>
    </mat-form-field>
    <button mat-button color="primary" [disabled]="disabled || loading"
            (click)="showSelected()">{{ viewSelected ? 'Show all' : 'Show selected'}}</button>
  </div>
  <div class="table">
    <div [style.height.px]="itemHeight"
         class="table-row table-row__header">
      <mat-checkbox [checked]="allChecked" (click)="toggleAll($event)" class="inline">
      </mat-checkbox>
      <div *ngFor="let column of columns" class="table-cell">{{ column.name }}</div>
    </div>
  </div>
  <div *ngIf="loading" class="loader-wrapper">
    <lib-swui-progress-container class="loading"></lib-swui-progress-container>
  </div>
  <cdk-virtual-scroll-viewport
    [itemSize]="itemHeight"
    minBufferPx="480"
    maxBufferPx="960"
    [style.height.px]="viewportHeight">
    <div class="table">
      <div *cdkVirtualFor="let option of options"
           [style.height.px]="itemHeight"
           class="table-row"
           [ngClass]="{'selected': option.state?.checked, 'disabled-option': option.disabled}"
           (click)="onSelectMultiple($event, option)">
        <mat-checkbox [checked]="option.state?.checked"
                      (click)="onSelectMultiple($event, option)">
        </mat-checkbox>
        <div *ngFor="let column of columns"
             class="table-cell">{{ option[column.field] | translate: option?.data }}</div>
      </div>
    </div>
  </cdk-virtual-scroll-viewport>
</div>

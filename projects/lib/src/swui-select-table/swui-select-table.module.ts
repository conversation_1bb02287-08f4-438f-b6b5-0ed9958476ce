import { ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatPseudoCheckboxModule, MatRippleModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { SwuiProgressContainerModule } from '../swui-progress-container/swui-progress-container.module';
import { SwuiSelectTableComponent } from './swui-select-table.component';


export const SELECT_TABLE_MODULES = [
  FormsModule,
  ReactiveFormsModule,
  MatIconModule,
  MatCheckboxModule,
  MatRippleModule,
  ScrollingModule,
  MatFormFieldModule,
  SwuiProgressContainerModule,
  MatInputModule,
  MatButtonModule
];

@NgModule({
  declarations: [SwuiSelectTableComponent],
    imports: [
        CommonModule,
        TranslateModule.forChild(),
        ...SELECT_TABLE_MODULES,
        MatPseudoCheckboxModule
    ],
  exports: [SwuiSelectTableComponent],
})
export class SwuiSelectTableModule {
}

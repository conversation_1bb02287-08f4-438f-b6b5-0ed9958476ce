import { coerceArray } from '@angular/cdk/coercion';
import { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';
import {
  ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, forwardRef, HostBinding, Input, OnInit, ViewChild
} from '@angular/core';
import { ControlValueAccessor, UntypedFormControl, NG_VALUE_ACCESSOR } from '@angular/forms';
import { MatMenuTrigger } from '@angular/material/menu';
import { ReplaySubject, Subject } from 'rxjs';
import { filter, map, take, takeUntil } from 'rxjs/operators';
import { SwuiSelectTableOption } from './swui-select-table.interface';

const CONTROL_NAME = 'lib-swui-select-table';
let nextUniqueId = 0;

@Component({
    selector: 'lib-swui-select-table',
    templateUrl: './swui-select-table.component.html',
    styleUrls: ['./swui-select-table.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => SwuiSelectTableComponent),
            multi: true
        }
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class SwuiSelectTableComponent implements ControlValueAccessor, OnInit {
  @Input() searchPlaceholder = 'Search';
  @Input() showSearch = false;
  @Input() startSearchLength = 0;
  @Input() disableEmptyOption = false;
  @Input() disableAllOption = false;
  @Input() columns: any[] = [];
  @Input() rowsNumber = 3;
  @Input() loading = false;

  @Input()
  set data( val: SwuiSelectTableOption[] ) {
    this._data = val || [];
    this.options = this._data;

    if (!this.disableAllOption) {
      this.allChecked = this.value?.length === this.enabledData.length;
    }

    this.options?.forEach(option => option.state = {
      checked: this.value?.includes(option.id),
    });

    if (Array.isArray(this.options) && this.options.length) {
      this.onDataReceived.next(this.options);
    }
  }

  get data(): SwuiSelectTableOption[] {
    return this._data;
  }

  @Input()
  set disabled( disabled: boolean ) {
    this.setDisabledState(disabled);
  }

  get disabled(): boolean {
    return this._disabled;
  }

  @Input()
  set value( val: string | string[] | undefined ) {
    this.patchSelectControl(val);
  }

  get value(): string[] | undefined {
    return this.selectControl.value?.map(( v: SwuiSelectTableOption ) => v.id);
  }

  get empty() {
    return !this.selectControl.value?.length;
  }

  get viewportHeight(): number {
    let length = this.data.length;
    length = Math.floor(length / this.rowsNumber) && this.rowsNumber || length % this.rowsNumber;
    return length * this.itemHeight;
  }

  allChecked = false;
  viewSelected = false;
  options: SwuiSelectTableOption[] = [];

  itemHeight = 48;

  onDataReceived = new ReplaySubject<SwuiSelectTableOption[]>(1);

  readonly controlType = CONTROL_NAME;
  readonly searchControl = new UntypedFormControl('');
  readonly selectControl = new UntypedFormControl();
  readonly destroyed$ = new Subject<void>();

  @ViewChild('trigger') trigger?: MatMenuTrigger;
  @ViewChild('search') searchRef?: ElementRef;
  @ViewChild(CdkVirtualScrollViewport, { static: false }) virtualScroll?: CdkVirtualScrollViewport;

  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;

  private _data: SwuiSelectTableOption[] = [];
  private _disabled = false;

  private previousSelected: string[] = [];

  constructor( private readonly cd: ChangeDetectorRef ) {
  }

  onChange = ( _: any ) => {
  };

  ngOnInit(): void {
    this.searchControl.valueChanges.pipe(
      filter<string>(data => this.showSearch && (data?.length >= (this.startSearchLength || 0) || data?.length === 0)),
      map<string, string>(searchString => searchString.toLowerCase()),
      takeUntil(this.destroyed$)
    ).subscribe(search => {
      this.previousSelected = search ? this.selectControl.value : [];
      this.options = this.data.filter(option => {
        for (const column of this.columns) {
          if (option[column.field] && option[column.field].toLowerCase().indexOf(search) > -1) {
            return true;
          }
        }
        return false;
      });
      this.cd.markForCheck();
    });

    this.selectControl.valueChanges.pipe(
      map<SwuiSelectTableOption[], SwuiSelectTableOption[]>(val => {
        const previousSelected = this.previousSelected.filter(id => !this.options.some(option => option.id === id));
        const values = this.enabledData.filter(( { id } ) => previousSelected.some(item => item === id));
        return [...values, ...val];
      }),
      map<SwuiSelectTableOption[], string[]>(val => val.map(item => item ? item.id : '')),
      takeUntil(this.destroyed$)
    ).subscribe(val => {
      this.onChange(val || null);
      this.cd.detectChanges();
    });
  }

  writeValue( val: string | string[] | undefined ): void {
    this.patchSelectControl(val);
  }

  patchSelectControl( val: string | string[] | undefined ) {
    this.onDataReceived
      .pipe(
        take(1),
      )
      .subscribe(
        options => {
          if (!this.disableAllOption) {
            this.allChecked = Array.isArray(val) && val.length === this.enabledData.length;
          }

          const values = coerceArray(val || []);

          options?.forEach(option => option.state = {
            checked: values?.includes(option.id),
          });

          const value = options.filter(opt => values.includes(opt.id));

          this.selectControl.patchValue(value, { emitEvent: false });

          this.cd.detectChanges();
        }
      );
  }

  toggleAll( event?: MouseEvent ) {
    event?.preventDefault();
    event?.stopPropagation();

    this.allChecked = !this.allChecked;
    this.options.forEach(option => {
      if (!option.disabled) {
        option.state.checked = this.allChecked;
      }
    });

    let checkedOptions = this.options.filter(option => option.state.checked);

    this.selectControl.setValue(checkedOptions);
  }

  showSelected() {
    this.viewSelected = !this.viewSelected;

    this.previousSelected = this.viewSelected ? this.selectControl.value : [];
    this.options = this.viewSelected ? this.data.filter(option => {
      return option.state.checked;
    }) : this.data;
    this.cd.markForCheck();
  }

  onSelectMultiple( event: MouseEvent, option: SwuiSelectTableOption ) {
    event.preventDefault();
    event.stopPropagation();

    if (option.state) {
      option.state.checked = !option.state.checked;
    }

    this.selectControl.patchValue(this.data?.filter(item => item.state?.checked));
  }

  stopPropagation( event: Event ) {
    event.stopPropagation();
  }

  private get enabledData(): SwuiSelectTableOption[] {
    return this.data.filter(( { disabled } ) => !disabled);
  }

  registerOnChange( fn: any ): void {
    this.onChange = fn;
  }

  registerOnTouched(): void {
  }

  setDisabledState( isDisabled: boolean ): void {
    this._disabled = isDisabled;
    if (isDisabled) {
      this.searchControl.disable();
    } else {
      this.searchControl.enable();
    }
  }
}

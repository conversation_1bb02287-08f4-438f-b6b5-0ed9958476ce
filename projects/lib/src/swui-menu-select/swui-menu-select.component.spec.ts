import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';

import { SwuiMenuSelectComponent } from './swui-menu-select.component';
import { SwuiSelectOption } from '../swui-select/swui-select.interface';
import { MENU_SELECT_MODULES } from './swui-menu-select.module';


function createFakeEvent(type: string) {
  const event = document.createEvent('Event');
  event.initEvent(type, true, true);
  return event;
}

describe('SwuiMenuSelectComponent', () => {
  let component: SwuiMenuSelectComponent;
  let fixture: ComponentFixture<SwuiMenuSelectComponent>;
  let testOptions: SwuiSelectOption[] = [];
  let testValue: string[];

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        CommonModule,
        BrowserAnimationsModule,
        TranslateModule.forRoot(),
        ...MENU_SELECT_MODULES
      ],
      declarations: [SwuiMenuSelectComponent]
    });
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiMenuSelectComponent);
    component = fixture.componentInstance;
    testOptions = [
      {id: '1', text: 'Solo Option1'},
      {id: '2', text: 'Test Option2'},
      {id: '3', text: 'Option3', disabled: true}
    ];
    testValue = ['1', '2'];
    component.data = testOptions;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set searchPlaceholder', () => {
    component.searchPlaceholder = 'test';
    expect(component.searchPlaceholder).toBe('test');
  });

  it('should set title', () => {
    component.title = 'test';
    expect(component.title).toBe('test');
  });

  it('should set showSearch', () => {
    component.showSearch = true;
    expect(component.showSearch).toBe(true);
  });

  it('should emit cancel click', () => {
    spyOn(component.cancelClick, 'emit');
    component.cancel(createFakeEvent('click'));

    fixture.detectChanges();
    expect(component.cancelClick.emit).toHaveBeenCalled();
  });

  it('should emit applied data', () => {
    spyOn(component.applyData, 'emit');
    component.data = testOptions;
    component.selected = testValue;
    component.apply(createFakeEvent('click'));

    fixture.detectChanges();
    expect(component.applyData.emit).toHaveBeenCalled();
  });

  it('should set processedData', () => {
    component.data = testOptions;
    component.selected = ['1'];
    const testResult = [
      {id: '1', text: 'Solo Option1', data: { checked: true }},
      {id: '2', text: 'Test Option2', data: { checked: false }},
      {id: '3', text: 'Option3', disabled: true, data: { checked: false }}
    ];
    expect(component.processedData).toEqual(testResult);
  });

  it('should clear', () => {
    component.data = testOptions;
    component.selected = ['1'];
    component.clear(createFakeEvent('click'));

    const testResult = [
      {id: '1', text: 'Solo Option1', data: { checked: false }},
      {id: '2', text: 'Test Option2', data: { checked: false }},
      {id: '3', text: 'Option3', disabled: true, data: { checked: false }}
    ];

    expect(component.processedData).toEqual(testResult);
  });

  it('should filter', () => {
    component.showSearch = true;
    component.ngOnInit();
    component.searchControl.setValue('1');
    expect(component.isFiltered('1')).toBe(true);
  });

  it('should get selected length', () => {
    component.data = testOptions;
    component.selected = ['1'];

    expect(component.selectedLength).toBe(1);
  });

});

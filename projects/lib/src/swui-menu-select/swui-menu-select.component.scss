$color-link: #1468cf;
$color-border: #D9DBDF;
$color-text: #2a2c44;

.swui-menu-select {
  min-width: 204px;

  &__footer {
    border-top: 1px solid $color-border;
  }

  &__header,
  &__footer {
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 12px;
  }

  &__header,
  &__footer,
  &__body {
    font-size: 14px;
  }

  &__header {
    justify-content: space-between;

    &-options {
      display: flex;
      margin-left: 4px;
    }
  }

  &__title {
    font-weight: 500;
  }

  &__clear,
  &__all {
    margin: 0 -4px 0 8px;
    font-size: 12px;
    line-height: 24px;
    border-radius: 4px;
    padding: 0 4px;
    color: $color-link;
    cursor: pointer;
    letter-spacing: 0.0892857em;
  }

  &__actions {
    display: flex;
    margin-left: auto;
  }

  &__button {
    min-width: auto;
    height: 24px;
    padding: 0 10px;
    font-size: 12px;
    line-height: 24px;
    font-weight: 400;
    text-transform: initial;
  }

  &__body {
    height: 160px;
    overflow: auto;

    &.search {
      height: 124px;
    }
  }

  &__item {
    display: flex;
    align-items: center;
    height: 36px;
    padding: 0 12px;
    line-height: 1;
    color: $color-text;

    &.invisible {
      display: none;
    }
  }

  &__amount {
    display: flex;
    color: #939DB1;
  }

  &__divider {
    margin: 0 2px;
  }

  &__subheader {
    padding: 0 12px;
  }

  &__search {
    position: relative;
    display: block;
    padding-bottom: 12px;

    input {
      display: block;
      height: 24px;
      width: 100%;
      padding: 0 8px 0 24px;
      font-size: 12px;
      line-height: 1;
      color: $color-text;
      border: 1px solid $color-border;
      border-radius: 4px;
    }
  }

  &__icon {
    position: absolute;
    top: 0;
    left: 0;
    width: 24px;
    height: 24px;
    font-size: 20px;
    line-height: 24px;
    text-align: center;
    color: $color-border;
  }

  &__checkbox {
    margin-right: 10px;
  }

  &__text {
    width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

import { SwuiMenuSelectComponent } from './swui-menu-select.component';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatRippleModule } from '@angular/material/core';


export const MENU_SELECT_MODULES = [
  FormsModule,
  ReactiveFormsModule,
  MatInputModule,
  MatIconModule,
  MatCheckboxModule,
  MatSelectModule,
  MatButtonModule,
  MatRippleModule,
];

@NgModule({
  declarations: [SwuiMenuSelectComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    ...MENU_SELECT_MODULES
  ],
  exports: [SwuiMenuSelectComponent],
})
export class SwuiMenuSelectModule {
}

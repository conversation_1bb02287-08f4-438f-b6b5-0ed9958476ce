<div class="swui-menu-select" (click)="prevent($event)">
  <div class="swui-menu-select__header">
    <div class="swui-menu-select__title">{{title | translate}}</div>
    <div class="swui-menu-select__header-options">
      <div class="swui-menu-select__all" *ngIf="selectAll" mat-ripple matRippleColor="rgba(19, 115, 213, 0.2)" (click)="onSelectAll($event)">{{'ALL.selectAll' | translate}}</div>
      <div class="swui-menu-select__clear" mat-ripple matRippleColor="rgba(19, 115, 213, 0.2)" (click)="clear($event)">{{'COMPONENTS.MENU_SELECT.clear' | translate}}</div>
    </div>
  </div>

  <div class="swui-menu-select__subheader">
    <label *ngIf="showSearch" class="swui-menu-select__search">
      <mat-icon class="swui-menu-select__icon">search</mat-icon>
      <input type="text"
             placeholder="{{searchPlaceholder | translate}}"
             [formControl]="searchControl">
    </label>
  </div>

  <div class="swui-menu-select__body" [ngClass]="{'search': showSearch}">
    <div
      *ngFor="let option of processedData"
      class="swui-menu-select__item"
      [ngClass]="{
        'invisible': !isFiltered(option.id),
        'disabled': option.disabled
      }">
      <mat-checkbox
        [(ngModel)]="option.data.checked"
        [disabled]="option.disabled"
        class="swui-menu-select__checkbox">
        <div class="swui-menu-select__text">
          {{option?.text | translate: option.data}}
        </div>
      </mat-checkbox>
    </div>
  </div>

  <div class="swui-menu-select__footer">
    <div class="swui-menu-select__amount">
      <div class="swui-menu-select__selected">
        {{selectedLength}}
      </div>
      <div class="swui-menu-select__divider">/</div>
      <div class="swui-menu-select__all">{{processedData?.length}}</div>
    </div>
    <div class="swui-menu-select__actions">
      <button
        mat-button
        color="primary"
        (click)="cancel($event)"
        class="swui-menu-select__button swui-menu-select__button--cancel">
        {{'COMPONENTS.MENU_SELECT.cancel' | translate}}
      </button>
      <button
        mat-flat-button
        color="primary"
        (click)="apply($event)"
        class="swui-menu-select__button">
        {{'COMPONENTS.MENU_SELECT.apply' | translate}}
      </button>
    </div>
  </div>
</div>

import { moduleMetadata, storiesOf } from '@storybook/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { APP_BASE_HREF } from '@angular/common';
import { I18nModule } from '../i18n.module';

import { SwuiMenuSelectComponent } from './swui-menu-select.component';
import { SwuiMenuSelectModule } from './swui-menu-select.module';
import { SwuiSelectOption } from '../swui-select/swui-select.interface';
import { MatMenuModule } from '@angular/material/menu';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';


const testData: SwuiSelectOption[] = [
  { id: '1', text: 'Solo Option1' },
  { id: '2', text: 'Test Option2' },
  { id: '3', text: 'Option3', disabled: true },
  { id: '4', text: 'Test Option4' },
  { id: '5', text: 'Option5 Option5 Option5' },
  { id: '6', text: 'Option6' },
  { id: '7', text: 'Option7' },
];

const EN = require('./locale.json');

const template = `
  <mat-card style="margin: 32px">
    <lib-swui-menu-select
      [title]="title"
      [data]="data"
      [selected]="selected"
      [showSearch]="showSearch">
    </lib-swui-menu-select>
  </mat-card>
`;

storiesOf('Forms/Menu Select', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        I18nModule.forRoot({ translations: { en: EN } }),
        SwuiMenuSelectModule,
        MatFormFieldModule,
        MatCardModule,
        MatButtonModule,
        MatIconModule,
        MatMenuModule,
      ],
      providers: [
        { provide: APP_BASE_HREF, useValue: '/' },
      ]
    })
  )
  .add('default', () => ({
    component: SwuiMenuSelectComponent,
    template,
    props: {
      data: testData,
      title: 'Test',
      selected: ['1', '4']
    }
  }))
  .add('search', () => ({
    component: SwuiMenuSelectComponent,
    template,
    props: {
      data: testData,
      title: 'Test',
      showSearch: true,
      selected: ['1', '4']
    }
  }))
  .add('empty', () => ({
    component: SwuiMenuSelectComponent,
    template,
    props: {
      title: 'Test',
      showSearch: true,
      selected: ['1', '4']
    }
  }))
  .add('button', () => ({
    component: SwuiMenuSelectComponent,
    template: `
    <mat-card style="margin: 32px">
      <button mat-icon-button [mat-menu-trigger-for]="menu">
         <mat-icon color="primary">face</mat-icon>
         <mat-menu #menu="matMenu">
           <lib-swui-menu-select
            [title]="title"
            [data]="data"
            [selected]="selected"
            [showSearch]="showSearch">
           </lib-swui-menu-select>
         </mat-menu>
      </button>
    </mat-card>
    `,
    props: {
      title: 'Test',
      showSearch: true,
      data: testData,
      selected: ['1', '4']
    }
  }));


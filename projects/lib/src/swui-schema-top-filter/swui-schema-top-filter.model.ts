import { InputOptionData } from '../swui-dynamic-form/dynamic-form.model';

export enum SchemaFilterMatchEnum {
  Equals = 0,
  Contains = 1,
  NotContains = 2,
  GreaterThan = 3,
  LessThan = 4,
  In = 5,
  GreaterThanEquals = 6,
  LessThanEquals = 7,
  Ne = 8,
  Text= 9,
  Fields= 10,
}

export type SchemaTopFilterField = InputOptionData & {
  field: string;
  filterMatch?: SchemaFilterMatchEnum | Record<string, SchemaFilterMatchEnum>;
  isFilterable?: boolean;
  isFilterableAlways?: boolean;
};

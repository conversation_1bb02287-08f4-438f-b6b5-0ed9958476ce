import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { MatDynamicFormModule } from '../swui-dynamic-form/mat-dynamic-form.module';
import { MatDynamicFormService } from '../swui-dynamic-form/mat-dynamic-form.service';
import { SwuiMenuSelectModule } from '../swui-menu-select/swui-menu-select.module';
import { SwuiSchemaTopFilterComponent } from './swui-schema-top-filter.component';

@NgModule({
  imports: [
    MatDynamicFormModule,
    MatTooltipModule,
    MatMenuModule,
    SwuiMenuSelectModule,
    TranslateModule,
    MatIconModule,
    MatButtonModule,
    CommonModule
  ],
  exports: [SwuiSchemaTopFilterComponent],
  declarations: [SwuiSchemaTopFilterComponent],
  providers: [MatDynamicFormService],
})
export class SwuiSchemaTopFilterModule {
}

import { moduleMetadata, storiesOf } from '@storybook/angular';
import { I18nModule } from '../../i18n.module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { SwuiSchemaTopFilterModule } from '../swui-schema-top-filter.module';
import { SettingsService } from '../../services/settings/settings.service';
import { Component, EventEmitter, Input, NgModule, Output } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

import { action } from 'storybook/actions';
import { SwuiTopFilterDataService } from '../top-filter-data.service';
import { SchemaFilterMatchEnum, SchemaTopFilterField } from '../swui-schema-top-filter.model';

const EN = {
  COMPONENTS: {
    MENU_SELECT: {
      clear: 'Clear',
      cancel: 'Cancel',
      apply: 'Apply',
      search: 'Search'
    }
  },
  USERS: {
    FILTER: {
      firstName: 'First Name'
    }
  }
};

const fields: SchemaTopFilterField[] = [
  {
    field: 'firstName',
    title: 'USERS.FILTER.firstName',
    type: 'string',
    filterMatch: SchemaFilterMatchEnum.Contains,
  }
];

@Component({
  template: `
    <lib-swui-schema-top-filter
      [schema]="schema"
    ></lib-swui-schema-top-filter>
  `,
  providers: [SwuiTopFilterDataService]
})
export class NoopComponent {
  @Input() schema: SchemaTopFilterField[] = [];
  @Output() changed = new EventEmitter<any>();

  constructor( translate: TranslateService, private readonly filterService: SwuiTopFilterDataService ) {
    translate.setDefaultLang('en');
    translate.use('en');
    this.filterService.submitFilter({});
    this.filterService.displayedFilter.subscribe(values => {
      this.changed.emit(values);
    });
  }
}

@NgModule({
  imports: [
    SwuiSchemaTopFilterModule,
  ],
  declarations: [
    NoopComponent,
  ],
})
export class NoopModule {
}

storiesOf('Grid/Top Filter', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        I18nModule.forRoot({ translations: { en: EN } }),
        NoopModule
      ],
      providers: [
        SettingsService
      ]
    })
  )
  .add('usage', () => ({
    component: NoopComponent,
    props: {
      schema: fields,
      changed: action('changed')
    },
  }));

import { ChangeDetectionStrategy, Component, Input, OnD<PERSON>roy, OnInit, Optional, ViewChild } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { MatMenuTrigger } from '@angular/material/menu';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, map, skip, switchMap, take, takeUntil } from 'rxjs/operators';
import { SettingsService } from '../services/settings/settings.service';
import { MatDynamicFormService } from '../swui-dynamic-form/mat-dynamic-form.service';
import { SwuiSelectOption } from '../swui-select/swui-select.interface';
import { SchemaTopFilterField } from './swui-schema-top-filter.model';
import { SwuiTopFilterDataService } from './top-filter-data.service';

export const SCHEMA_FILTER_FIELD_POSTFIX_MAP = [
  '',
  '__contains',
  '__contains!',
  '__gt',
  '__lt',
  '__in',
  '__gte',
  '__lte',
  '__ne',
  'Text',
  'Fields'
];

const CLEARABLE_TYPES: Record<SchemaTopFilterField['type'], boolean> = {
  daterange: true,
  datetimerange: true,
  string: true,
  text: true,
  number: true,
  numericrange: true
};

@Component({
    selector: 'lib-swui-schema-top-filter',
    templateUrl: './swui-schema-top-filter.component.html',
    styleUrls: ['./swui-schema-top-filter.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class SwuiSchemaTopFilterComponent implements OnInit, OnDestroy {
  @Input() set schema( schema: SchemaTopFilterField[] ) {
    this.transformToForm = {};
    this.transformToModel = {};
    this.selectOptionSchema = [];
    this.formSchema = this.transformSchema(schema);
    this.filter?.updateFilter(this.formSchema);

    this.onApplyFilterFields(Object.keys(this._values));
  }

  @Input() name = '';
  @ViewChild(MatMenuTrigger, { static: false }) filterMenuRef?: MatMenuTrigger;
  form = new UntypedFormGroup({});
  formSchema: SchemaTopFilterField[] = [];
  displayFormSchema: SchemaTopFilterField[] = [];
  selectOptionSchema: SwuiSelectOption[] = [];
  selectedOptions: string[] = [];
  private _loading = new BehaviorSubject(true);
  private transformToForm: any = {};
  private transformToModel: any = {};
  private submitted = false;
  private destroy$ = new Subject();
  private _values: any = {};
  private defaultSelected: string[] = [];

  constructor( private readonly settings: SettingsService,
               private readonly dynamicFormService: MatDynamicFormService,
               @Optional() private readonly filter: SwuiTopFilterDataService | null
  ) {
  }

  get loading(): boolean {
    return this._loading.value;
  }

  get loading$(): Observable<boolean> {
    return this._loading.asObservable();
  }

  ngOnInit() {
    this.settings.appSettings$
      .pipe(
        takeUntil(this.destroy$)
      )
      .subscribe(( { timezoneName } ) => {
        this.dynamicFormService.setTimezone(timezoneName);
      });

    this.filter?.appliedFilter
      .pipe(
        debounceTime(10),
        takeUntil(this.destroy$)
      )
      .subscribe(data => {
        this._values = this.transformFilterToForm(data);

        if (this.submitted && !this.loading) {
          this.submitted = false;
          return;
        }

        if (this.loading) {
          this.onApplyFilterFields(Object.keys(this._values));
        }

        this._loading.next(false);
      });

    this.filter?.appliedFilter
      .pipe(
        take(1),
        switchMap(() => this.form.valueChanges),
        debounceTime(500),
        map(data => {
          const result = Object.keys(data)
            .reduce(( res, key ) => {
              const controlValue = Array.isArray(data[key])
                ? data[key].toString()
                : data[key];

              return {
                ...this.transformToModel[key](controlValue),
                ...res
              };
            }, {});

        this.filter?.setFormState(result);

          return result;
        }),
        distinctUntilChanged(( prev, curr ) => {
          return JSON.stringify(this.getFilledObject(prev || {})) === JSON.stringify(this.getFilledObject(curr || {}));
        }),
        skip(1),
        takeUntil(this.destroy$)
      )
      .subscribe(filterData => {
        this.submitted = true;
        this.filter?.submitFilter(filterData, true);
      });
  }

  onApplyFilterFields( data: string[] ) {
    this.selectedOptions = data;
    const uniqueFields = Array.from(new Set([
      ...this.defaultSelected,
      ...data
    ]));

    this.displayFormSchema = uniqueFields.map(fieldName => {
      const schemaItem = this.formSchema.find(( { field } ) => field === fieldName) as SchemaTopFilterField;

      return {
        ...schemaItem,
        value: this._values[fieldName]
      };
    });

    if (this.filterMenuRef && this.filterMenuRef.menu) {
      this.filterMenuRef.closeMenu();
    }
  }

  onCancel() {
    if (this.filterMenuRef) {
      this.filterMenuRef.closeMenu();
    }
  }

  ngOnDestroy() {
    this.destroy$.next(undefined);
    this.destroy$.complete();
  }

  private getFilledObject( obj: any ): any {
    return Object.entries(obj)
      .reduce(( res: any, [key, value] ) => {
        if (value) {
          res[key] = value;
        }

        return res;
      }, {});
  }

  private transformSchema( schema: SchemaTopFilterField[] ): SchemaTopFilterField[] {
    this.defaultSelected = [];
    this.selectOptionSchema = [];

    return schema.map(item => {
      if (item.isFilterableAlways) {
        this.defaultSelected.push(item.field);
      } else {
        this.selectOptionSchema.push({
          id: item.field,
          text: item.title || '',
          data: item.field
        });
      }

      if (item.filterMatch) {
        this.transformToModel[item.field] = ( fieldValue: any ) => {
          if (typeof item.filterMatch !== 'object') {
            const key = `${item.field}${SCHEMA_FILTER_FIELD_POSTFIX_MAP[item.filterMatch as number]}`;
            const val = item.type === 'string' && fieldValue ? fieldValue.trim() : fieldValue;

            return { [key]: val };
          }

          return Object.entries(item.filterMatch as any)
            .reduce(( res: any, [key, value] ) => {
              const keyValue = (fieldValue || {})[key] || null;
              res[`${item.field}${SCHEMA_FILTER_FIELD_POSTFIX_MAP[value as number]}`] = keyValue;

              return res;
            }, {});
        };
        if (typeof item.filterMatch !== 'object') {
          const key = `${item.field}${SCHEMA_FILTER_FIELD_POSTFIX_MAP[item.filterMatch as number]}`;
          this.transformToForm[key] = ( fieldValue: any ) => {
            const value = item.type === 'multiselect'
              ? (fieldValue || '').split(',')
              : fieldValue;
            return { [item.field]: value, field: item.field, isSimply: true };
          };
        } else {
          Object.entries(item.filterMatch).forEach(( [key, value] ) => {
            this.transformToForm[`${item.field}${SCHEMA_FILTER_FIELD_POSTFIX_MAP[value]}`] = ( fieldValue: any ) => {
              return { [key]: fieldValue, field: item.field };
            };
          });
        }
      } else {
        this.transformToModel[item.field] = ( fieldValue: any ) => {
          const val = item.type === 'string' && fieldValue ? fieldValue.trim() : fieldValue;
          return { [item.field]: val };
        };
        this.transformToForm[item.field] = ( fieldValue: any ) => {
          const value = item.type === 'multiselect'
            ? (fieldValue || '').split(',')
            : fieldValue;
          return { [item.field]: value, field: item.field, isSimply: true };
        };
      }

      return { ...item, key: item.field, clearable: CLEARABLE_TYPES[item.type] };
    });
  }

  private transformFilterToForm( filter: any ): any {
    const fields: string[] = Object.keys(this.transformToForm)
      .filter(field => {
        return filter.hasOwnProperty(field);
      });

    return fields
      .reduce(( res: any, key: string ) => {
        const transformData = this.transformToForm[key](filter[key]);
        const { field, isSimply } = transformData;
        const prev = res[field];
        delete transformData.field;

        if (isSimply) {
          res[field] = transformData[field];
        } else {
          res[field] = {
            ...prev,
            ...transformData
          };
        }

        return res;
      }, {});
  }
}

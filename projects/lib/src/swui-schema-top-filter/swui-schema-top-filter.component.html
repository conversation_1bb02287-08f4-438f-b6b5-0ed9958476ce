<div class="top-filter"
     [class.top-filter__hidden]="loading$ | async">
  <lib-dynamic-form
    class="top-filter__form"
    direction="row"
    [options]="displayFormSchema"
    [controlName]="name"
    [form]="form">

    <button *ngIf="selectOptionSchema?.length"
            mat-stroked-button
            type="button"
            class="top-filter__add"
            [matMenuTriggerFor]="filterMenu">
      <mat-icon>add</mat-icon>
      <span>Filter</span>
    </button>

    <mat-menu #filterMenu="matMenu">
      <lib-swui-menu-select
        [title]="'Filter'"
        [data]="selectOptionSchema"
        [selected]="selectedOptions"
        [selectAll]="true"
        (applyData)="onApplyFilterFields($event)"
        (cancelClick)="onCancel()">
      </lib-swui-menu-select>
    </mat-menu>

  </lib-dynamic-form>
</div>

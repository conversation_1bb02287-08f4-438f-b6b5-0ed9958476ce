import { TestBed } from '@angular/core/testing';
import { SwuiTopFilterDataService } from './top-filter-data.service';
import type { SchemaTopFilterField } from './swui-schema-top-filter.model';

describe('SwuiTopFilterDataService', () => {
  let service: SwuiTopFilterDataService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [SwuiTopFilterDataService]
    });
    service = TestBed.inject(SwuiTopFilterDataService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('updateFilter', () => {
    it('should not modify filter when no select fields are present', () => {
      const initialFilter = { field1: 'value1', field2: 'value2' };
      service.submitFilter(initialFilter);

      const schema: SchemaTopFilterField[] = [
        { field: 'field1', type: 'string' },
        { field: 'field2', type: 'string' }
      ];

      service.updateFilter(schema);

      // filter should remain unchanged
      service.appliedFilter.subscribe(filter => {
        expect(filter).toEqual(initialFilter);
      });
    });

    it('should remove values for select fields that are not in the schema options', () => {
      const initialFilter = {
        field1: 'value1',
        selectField: 'option2'
      };
      service.submitFilter(initialFilter);

      const schema: SchemaTopFilterField[] = [
        { field: 'field1', type: 'string' },
        {
          field: 'selectField',
          type: 'select',
          data: [
            { id: 'option1', text: 'Option 1' },
            // option2 is missing from the options
            { id: 'option3', text: 'Option 3' }
          ]
        }
      ];

      service.updateFilter(schema);

      // selectField should be removed as its value doesn't exist in options
      service.appliedFilter.subscribe(filter => {
        expect(filter).toEqual({ field1: 'value1' });
        expect(filter.selectField).toBeUndefined();
      });
    });

    it('should keep values for select fields that exist in the schema options', () => {
      const initialFilter = {
        field1: 'value1',
        selectField: 'option2'
      };
      service.submitFilter(initialFilter);

      const schema: SchemaTopFilterField[] = [
        { field: 'field1', type: 'string' },
        {
          field: 'selectField',
          type: 'select',
          data: [
            { id: 'option1', text: 'Option 1' },
            { id: 'option2', text: 'Option 2' }, // option2 exists in the options
            { id: 'option3', text: 'Option 3' }
          ]
        }
      ];

      service.updateFilter(schema);

      // filter should remain unchanged as all values exist in options
      service.appliedFilter.subscribe(filter => {
        expect(filter).toEqual(initialFilter);
      });
    });

    it('should handle multiselect fields correctly', () => {
      const initialFilter = {
        field1: 'value1',
        multiSelectField: ['option1', 'option2', 'option4']
      };
      service.submitFilter(initialFilter);

      const schema: SchemaTopFilterField[] = [
        { field: 'field1', type: 'string' },
        {
          field: 'multiSelectField',
          type: 'multiselect',
          data: [
            { id: 'option1', text: 'Option 1' },
            { id: 'option2', text: 'Option 2' },
            { id: 'option3', text: 'Option 3' }
            // option4 is missing from the options
          ]
        }
      ];

      service.updateFilter(schema);

      // multiSelectField should only contain values that exist in options
      service.appliedFilter.subscribe(filter => {
        expect(filter.field1).toEqual('value1');
        expect(filter.multiSelectField).toEqual([]);
      });
    });

    it('should handle select-table fields correctly', () => {
      const initialFilter = {
        field1: 'value1',
        tableField: { id: 'option2' }
      };
      service.submitFilter(initialFilter);

      const schema: SchemaTopFilterField[] = [
        { field: 'field1', type: 'string' },
        {
          field: 'tableField',
          type: 'select-table',
          data: [
            { id: 'option1', text: 'Option 1' },
            // option2 is missing from the options
            { id: 'option3', text: 'Option 3' }
          ]
        }
      ];

      service.updateFilter(schema);

      // tableField should be removed as its value doesn't exist in options
      service.appliedFilter.subscribe(filter => {
        expect(filter).toEqual({ field1: 'value1' });
        expect(filter.tableField).toBeUndefined();
      });
    });

    it('should not modify filter when schema is empty', () => {
      const initialFilter = { field1: 'value1', field2: 'value2' };
      service.submitFilter(initialFilter);

      service.updateFilter([]);

      // filter should remain unchanged
      service.appliedFilter.subscribe(filter => {
        expect(filter).toEqual(initialFilter);
      });
    });
  });
});

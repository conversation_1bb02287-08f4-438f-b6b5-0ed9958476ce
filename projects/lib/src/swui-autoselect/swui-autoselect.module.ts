import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { SwuiAutoselectComponent } from './swui-autoselect.component';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';


export const MOULES_AUTOSELECT = [
  MatAutocompleteModule,
  MatInputModule,
  MatButtonModule,
  ReactiveFormsModule,
];

@NgModule({
  declarations: [SwuiAutoselectComponent],
  exports: [SwuiAutoselectComponent],
  imports: [
    CommonModule,
    ...MOULES_AUTOSELECT,
  ]
})
export class SwuiAutoselectModule {
}

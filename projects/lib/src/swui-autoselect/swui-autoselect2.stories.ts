import { Meta } from '@storybook/angular/types-6-0';
import { SwuiAutoselectComponent } from './swui-autoselect.component';
import { OptionModel } from './option.model';
import { moduleMetadata } from '@storybook/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { SwuiAutoselectModule } from './swui-autoselect.module';
import { MatFormFieldModule } from '@angular/material/form-field';

const options: OptionModel[] = [
  {id: 'one', text: 'One'},
  {id: 'two', text: 'Two'},
  {id: 'three', text: 'Three'},
  {id: 'four', text: 'Four', disabled: true},
  {id: 'five', text: 'Five'}
];

const template = `
  <mat-form-field appearance="outline">
    <mat-label>Test</mat-label>
    <lib-swui-autoselect
      [value]="value"
      [disabled]="disabled"
      [required]="required"
      [options]="options">
    </lib-swui-autoselect>
  </mat-form-field>
`;

export default {
  title: 'Forms/Autoselect',
  component: SwuiAutoselectComponent,
  decorators: [
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        SwuiAutoselectModule,
        MatFormFieldModule,
      ],
    }),
  ],
} as Meta;

export const Primary = () => ({
  props: {
    options
  },
  template
});

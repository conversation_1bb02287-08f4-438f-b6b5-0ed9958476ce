import { ChangeDetectionStrategy, Component, ElementRef, HostBinding, Input, OnInit, Optional, Self, ViewChild } from '@angular/core';
import { UntypedFormControl, FormGroupDirective, NgControl } from '@angular/forms';
import { BehaviorSubject } from 'rxjs';
import { FocusMonitor } from '@angular/cdk/a11y';

import { OptionModel } from './option.model';
import { MatFormFieldControl } from '@angular/material/form-field';
import { MatAutocomplete } from '@angular/material/autocomplete';
import { MatInput } from '@angular/material/input';
import { SwuiMatFormFieldControl } from '../common/swui-mat-form-field-control';
import { ErrorStateMatcher } from '@angular/material/core';
import { takeUntil } from 'rxjs/operators';


const CONTROL_NAME = 'lib-swui-autoselect';
let nextUniqueId = 0;

@Component({
    selector: 'lib-swui-autoselect',
    templateUrl: './swui-autoselect.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [{ provide: MatFormFieldControl, useExisting: SwuiAutoselectComponent }],
    standalone: false
})
export class SwuiAutoselectComponent extends SwuiMatFormFieldControl<string | undefined> implements OnInit {
  @Input()
  get value(): string | undefined {
    return this._value;
  }

  set value( val: string | undefined ) {
    this._selectedId$.next(val || '');
    this.stateChanges.next(undefined);
  }

  @Input()
  get options(): OptionModel[] {
    return this._options;
  }

  set options( data: OptionModel[] ) {
    this._options = data;
    this.filteredOptions = data;
  }

  @Input() isNotInOptionsValue = false;

  get empty() {
    return this._isEmpty;
  }

  readonly controlType = CONTROL_NAME;

  readonly inputFormControl: UntypedFormControl = new UntypedFormControl();
  filteredOptions: OptionModel[] = [];

  @ViewChild(MatInput) input?: MatInput;
  @ViewChild('auto') matAutocomplete: MatAutocomplete | undefined;

  @HostBinding() readonly id = `${CONTROL_NAME}-${nextUniqueId++}`;

  @HostBinding('class.floating')
  get shouldLabelFloat() {
    return this.focused || !this.empty;
  }

  private _value: string | undefined;
  private _options: OptionModel[] = [];
  private readonly _selectedId$: BehaviorSubject<string> = new BehaviorSubject('');
  private _isEmpty = true;

  constructor( fm: FocusMonitor,
               elRef: ElementRef<HTMLElement>,
               @Optional() @Self() ngControl: NgControl,
               @Optional() parentFormGroup: FormGroupDirective,
               errorStateMatcher: ErrorStateMatcher ) {
    super(fm, elRef, ngControl, parentFormGroup, errorStateMatcher);
  }

  ngOnInit() {
    this.inputFormControl.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(( text: string ) => {
      this.filteredOptions = this._filter(text);
      let item = this.options.find(( option: OptionModel ) => option.text === text);
      if (this.isNotInOptionsValue && text && !item) {
        item = {
          id: text,
          text: text,
        };
      }
      this._value = item ? item.id : undefined;
      this._isEmpty = !this._value;
      this.onChange(this._value);
    });

    this._selectedId$.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(( id: string | undefined ) => {
      this.setInputValue(id);
    });
  }

  onContainerClick( event: Event ) {
    event.stopPropagation();
    if (this.input && (event.target as Element).tagName.toLowerCase() !== 'input' && !this.disabled) {
      this.input.focus();
    }
  }

  writeValue( id: string ) {
    if (!id) {
      return;
    }
    this._selectedId$.next(id);
  }

  protected onDisabledState( disabled: boolean ) {
    if (disabled) {
      this.inputFormControl.disable();
    } else {
      this.inputFormControl.enable();
    }
  }

  protected isErrorState(): boolean {
    if (this.input) {
      return this.input.errorState;
    }
    return false;
  }

  private _filter( value: string ): OptionModel[] {
    const filterValue = value ? value.toLowerCase() : '';
    return this.options.filter(( option: OptionModel ) => option.text.toLowerCase().includes(filterValue));
  }

  private setInputValue( id: string | undefined ) {
    let item = this.options.find(( option: OptionModel ) => option.id === id && !option.disabled);
    if (this.isNotInOptionsValue && id && !item) {
      item = {
        id: id,
        text: id,
      };
    }
    this.inputFormControl.setValue(item ? item.text : undefined);
  }
}

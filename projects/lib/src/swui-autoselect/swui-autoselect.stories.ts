import { moduleMetadata, storiesOf } from '@storybook/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { SwuiAutoselectComponent } from './swui-autoselect.component';
import { SwuiAutoselectModule } from './swui-autoselect.module';
import { MatFormFieldModule } from '@angular/material/form-field';
import { OptionModel } from './option.model';

const options: OptionModel[] = [
  {id: 'one', text: 'One'},
  {id: 'two', text: 'Two'},
  {id: 'three', text: 'Three'},
  {id: 'four', text: 'Four', disabled: true},
  {id: 'five', text: 'Five'}
];

const template = `
  <mat-form-field appearance="outline">
    <mat-label>Test</mat-label>
    <lib-swui-autoselect
      [value]="value"
      [disabled]="disabled"
      [required]="required"
      [options]="options">
    </lib-swui-autoselect>
  </mat-form-field>
`;

storiesOf('Forms/Autocomplete', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        SwuiAutoselectModule,
        MatFormFieldModule,
      ],
    })
  )
  .add('default', () => ({
    component: SwuiAutoselectComponent,
    props: {
      options,
    },
    template
  }))
  .add('disabled', () => ({
    component: SwuiAutoselectComponent,
    props: {
      options,
      disabled: true,
    },
    template
  }))
  .add('required', () => ({
    component: SwuiAutoselectComponent,
    props: {
      options,
      required: true,
    },
    template
  }));

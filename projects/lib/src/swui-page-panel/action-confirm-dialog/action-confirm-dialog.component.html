<h2 mat-dialog-title *ngIf="data?.title">
  {{ data?.title | translate }}
</h2>

<div mat-dialog-content>
  <p *ngIf="data.action.confirmText">
    {{ data.action.confirmText | translate }}
  </p>
</div>

<div mat-dialog-actions align="end">
  <button mat-button color="primary" class="mat-button-md" mat-dialog-close>
    {{ data?.closeButtonText ? (data?.closeButtonText | translate) : ('DIALOG.close' | translate) }}
  </button>

  <button mat-flat-button color="primary" class="mat-button-md" cdkFocusInitial (click)="doConfirm()">
    {{ data?.confirmButtonText ? (data?.confirmButtonText | translate) : ('DIALOG.confirmAction' | translate) }}
  </button>
</div>

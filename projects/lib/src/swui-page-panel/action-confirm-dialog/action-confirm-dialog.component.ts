import { Component, Inject, OnInit } from '@angular/core';
import { PanelAction } from '../swui-page-panel.component';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';

export interface ConfirmDialogResult {
  confirmed: boolean;
}

export interface ConfirmDialogData {
  title?: string;
  action: PanelAction;
  closeButtonText?: string;
  confirmButtonText: string;
}

@Component({
    selector: 'lib-swui-action-confirm-dialog',
    templateUrl: 'action-confirm-dialog.component.html',
    standalone: false
})
export class ActionConfirmDialogComponent implements OnInit {
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: ConfirmDialogData,
    public dialogRef: MatDialogRef<ActionConfirmDialogComponent>
  ) {
  }

  ngOnInit() {
  }

  doConfirm() {
    this.dialogRef.close({ confirmed: true });
  }
}

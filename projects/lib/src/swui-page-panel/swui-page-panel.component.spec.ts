import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { CommonModule, Location } from '@angular/common';
import { Router, RouterModule } from '@angular/router';

import { RouterTestingModule } from '@angular/router/testing';
import { of } from 'rxjs';
import { TranslateModule, TranslateStore } from '@ngx-translate/core';

import { PanelAction, SwuiPagePanelComponent } from './swui-page-panel.component';
import { ActionConfirmDialogComponent } from './action-confirm-dialog/action-confirm-dialog.component';
import { MatMenuModule } from '@angular/material/menu';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';

function createFakeEvent( type: string ) {
  const event = document.createEvent('MouseEvent');
  event.initEvent(type, true, true);
  return event;
}

describe('SwuiPagePanelComponent', () => {
  let component: SwuiPagePanelComponent;
  let fixture: ComponentFixture<SwuiPagePanelComponent>;
  let testPanelActions: PanelAction[] = [];

  let location: Location;
  let router: Router;
  let dialogSpy: jasmine.Spy;
  let dialogRefSpyObj = jasmine.createSpyObj({ afterClosed: of({ confirmed: true }), close: null });

  let empty: any;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        CommonModule,
        RouterModule,

        TranslateModule.forChild(),

        MatCardModule,
        MatButtonModule,
        MatIconModule,
        MatMenuModule,
        MatDialogModule,

        RouterTestingModule.withRoutes([]),
      ],
      declarations: [
        SwuiPagePanelComponent,
        ActionConfirmDialogComponent,
      ],
      providers: [
        {
          provide: Router, useValue: { navigate: jasmine.createSpy('navigate') }
        },
        {
          provide: Location, useValue: { back: jasmine.createSpy('back') }
        },
        {
          provide: TranslateStore,
        },
      ]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiPagePanelComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    location = TestBed.inject(Location);
    router = TestBed.inject(Router);
    dialogSpy = spyOn(TestBed.inject(MatDialog), 'open')
      .and
      .returnValue(dialogRefSpyObj);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set empty action', () => {
    component.actions = testPanelActions;
    expect(component.actions).toBeTruthy();
  });

  it('should not set empty action', () => {
    component.actions = empty;
    expect(component.actions).toBeTruthy();
  });

  it('action should return availableFn', () => {
    testPanelActions.push({
      title: 'test',
      availableFn: () => {
        return true;
      }
    });
    component.actions = testPanelActions;
    expect(component.actions).toEqual(testPanelActions);
  });

  it('should get action', () => {
    testPanelActions.push({
      title: 'test'
    });
    component.actions = testPanelActions;
    expect(component.actions).toEqual(testPanelActions);
  });

  it('should set title', () => {
    component.title = 'test';
    expect(component.title).toBe('test');
  });

  it('should set undefined title', () => {
    component.title = empty;
    expect(component.title).toBeUndefined();
  });

  it('should set back', () => {
    component.back = true;
    expect(component.back).toBe(true);
  });

  it('should set backUrl', () => {
    component.backUrl = 'test';
    expect(component.backUrl).toBe('test');
  });

  it('backUrl can be undefined', () => {
    component.backUrl = empty;
    expect(component.backUrl).toBeUndefined();
  });

  it('should set actionsMenuButtonTitle', () => {
    component.actionsMenuButtonTitle = 'test';
    expect(component.actionsMenuButtonTitle).toBe('test');
  });

  it('actionsMenuButtonColor can be undefined', () => {
    component.actionsMenuButtonColor = empty;
    expect(component.actionsMenuButtonColor).toBeUndefined();
  });

  it('should set actionsMenuButtonColor', () => {
    component.actionsMenuButtonColor = 'primary';
    expect(component.actionsMenuButtonColor).toBe('primary');
  });

  it('layout can be undefined', () => {
    component.layout = empty;
    expect(component.layout).toBeUndefined();
  });

  it('should set layout', () => {
    component.layout = 'separate';
    expect(component.layout).toBe('separate');
  });

  it('should click goBack', () => {
    spyOn(component, 'goBack');
    component.goBack(createFakeEvent('click'));
    expect(component.goBack).toHaveBeenCalled();
  });

  it('goBack with backUrl navigate to /test', () => {
    component.backUrl = '/test';
    component.goBack(createFakeEvent('click'));
    expect(router.navigate).toHaveBeenCalledWith(['/test']);
  });

  it('goBack without backUrl navigate', () => {
    component.goBack(createFakeEvent('click'));
    expect(location.back).toHaveBeenCalledTimes(1);
  });

  it('should click performAction', () => {
    const action: PanelAction = {
      title: 'test'
    };
    spyOn(component, 'performAction');
    component.performAction(createFakeEvent('click'), action);
    expect(component.performAction).toHaveBeenCalled();
  });

  it('isDisabled should return false', () => {
    const action: PanelAction = {
      title: 'test'
    };
    expect(component.isDisabled(action)).toBeFalsy();
  });

  it('isDisabled should return true', () => {
    const action: PanelAction = {
      title: 'test',
      disabledFn: () => {
        return true;
      }
    };
    expect(component.isDisabled(action)).toBeTruthy();
  });

  it('performAction should navigate to actionUrl', () => {
    const action: PanelAction = {
      title: 'test',
      actionUrl: '/test'
    };

    component.performAction(createFakeEvent('click'), action);
    expect(dialogSpy).toHaveBeenCalledTimes(0);
    expect(router.navigate).toHaveBeenCalledWith(['/test']);
  });

  it('performAction return actionFn', () => {
    const action: PanelAction = {
      title: 'test',
      actionFn: () => true
    };
    component.performAction(createFakeEvent('click'), action);
    expect(component.performAction).toBeTruthy();
    expect(dialogSpy).toHaveBeenCalledTimes(0);
  });

  it('should open performAction dialog', () => {
    const action: PanelAction = {
      title: 'test',
      confirm: true
    };

    component.performAction(createFakeEvent('click'), action);
    expect(dialogSpy).toHaveBeenCalledTimes(1);
  });

  it('should close performAction dialog', () => {
    const action: PanelAction = {
      title: 'test',
      confirm: true
    };
    spyOn<any>(component, 'callAction');
    component.performAction(createFakeEvent('click'), action);
    expect(dialogSpy).toHaveBeenCalledTimes(1);
    expect(dialogRefSpyObj.afterClosed).toHaveBeenCalled();
    expect(component['callAction']).toHaveBeenCalled();
  });
});

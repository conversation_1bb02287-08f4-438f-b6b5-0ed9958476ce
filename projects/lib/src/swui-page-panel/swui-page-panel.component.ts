import { Location } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { ThemePalette } from '@angular/material/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { filter, take } from 'rxjs/operators';
import {
  ActionConfirmDialogComponent, ConfirmDialogResult
} from './action-confirm-dialog/action-confirm-dialog.component';

export type PagePanelButtonLayout = 'menu' | 'separate';

export interface PanelAction {
  title: string;
  hover?: string;
  icon?: string;
  svgIcon?: string;
  fontIcon?: string;
  fontSet?: string;
  color?: ThemePalette;
  iconFontSize?: string;
  iconWidth?: string;
  iconHeight?: string;
  iconRightMargin?: string;
  actionUrl?: string;
  actionFn?: Function;
  disabledFn?: Function;
  availableFn?: Function;
  confirm?: boolean;
  confirmText?: string;
  getStyle?: () => Record<string, string>;
}

@Component({
    selector: 'lib-swui-page-panel',
    templateUrl: 'swui-page-panel.component.html',
    styleUrls: [
        './swui-page-panel.component.scss',
    ],
    standalone: false
})

export class SwuiPagePanelComponent implements OnInit {
  @Input()
  set actions( actions: PanelAction[] ) {
    if (actions) {
      this._actions = actions.filter(item => {
        let available = typeof item.availableFn === 'undefined';
        if (!available && item.availableFn) {
          available = item.availableFn();
        }
        return available;
      });
    }
  }

  get actions(): PanelAction[] {
    return this._actions;
  }

  @Input() title = '';
  @Input() back = false;
  @Input() backUrl: string | undefined;

  @Input() actionsMenuButtonTitle = '';
  @Input() actionsMenuButtonColor: ThemePalette = 'primary';
  @Input() layout: PagePanelButtonLayout = 'separate';

  private _actions: PanelAction[] = [];
  private confirmDialogRef!: MatDialogRef<ActionConfirmDialogComponent>;

  constructor(
    private router: Router,
    private dialog: MatDialog,
    private location: Location
  ) {
  }

  ngOnInit() {
  }

  goBack( $event: MouseEvent ) {
    $event.preventDefault();

    if (this.backUrl) {
      this.router.navigate([this.backUrl]);
    } else {
      this.location.back();
    }
  }

  performAction( $event: MouseEvent, action: PanelAction ) {
    $event.preventDefault();

    if (action.confirm) {
      this.confirmDialogRef = this.dialog.open(ActionConfirmDialogComponent, {
        width: '350px',
        data: { action }
      });
      this.confirmDialogRef.afterClosed().pipe(
        filter(( confirmResult: ConfirmDialogResult ) => confirmResult.confirmed),
        take(1)
      ).subscribe(() => this.callAction(action));
    } else {
      this.callAction(action);
    }
  }

  isDisabled( action: PanelAction ): boolean {
    return typeof action.disabledFn !== 'undefined' ? action.disabledFn() : false;
  }

  private callAction( action: PanelAction ) {
    if (action.actionUrl) {
      this.router.navigate([action.actionUrl]);
    } else if (typeof action.actionFn !== 'undefined') {
      action.actionFn();
    }
  }
}

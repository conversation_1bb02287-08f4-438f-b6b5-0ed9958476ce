import { moduleMetadata, storiesOf } from '@storybook/angular';
import { I18nModule } from '../i18n.module';
import { SwuiPagePanelModule } from './swui-page-panel.module';
import { RouterModule } from '@angular/router';
import { APP_BASE_HREF } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

const EN = require('./.storybook/locale.json');
const template = `
  <lib-swui-page-panel
    [title]="title"
    [actions]="actions"
    [backUrl]="backUrl"
    [layout]="layout">
  </lib-swui-page-panel>
`;


storiesOf('Page Panel', module)
  .addParameters({
    cssresources: [
      {
        id: 'mat-drawer-container',
        code: `<style>body { background-color: #eaedf1; }</style>`,
        picked: true,
      }
    ]
  })
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        RouterModule.forRoot([]),
        SwuiPagePanelModule,
        I18nModule.forRoot({ translations: { en: EN } }),
      ],
      providers: [
        { provide: APP_BASE_HREF, useValue: '/iframe.html' },
      ]
    })
  )
  .add('Single action', () => ({
    template,
    props: {
      title: 'MENU_SECTIONS.marketingPromotions',
      layout: 'separate',
      actions: [
        {
          title: 'PROMO.btnCreatePromo',
          icon: 'add',
          color: 'primary',
          actionFn: () => console.log('action called successfully')
        }
      ]
    }
  }))
  .add('Multiple actions separated', () => ({
    template,
    props: {
      title: 'MENU_SECTIONS.marketingPromotions',
      layout: 'separate',
      actions: [
        {
          title: 'PROMO.btnCreatePromo',
          icon: 'today',
          color: 'primary',
          actionFn: () => console.log('action called successfully')
        },
        {
          title: 'PROMO.btnCreateSimpleFreeBet',
          icon: 'filter',
          color: 'primary',
          actionFn: () => console.log('action called successfully')
        }
      ]
    }
  }))
  .add('Multiple actions in Menu', () => ({
    template,
    props: {
      title: 'MENU_SECTIONS.marketingPromotions',
      layout: 'menu',
      actions: [
        {
          title: 'PROMO.btnCreatePromo',
          icon: 'today',
          color: 'primary',
          actionFn: () => console.log('action called successfully')
        },
        {
          title: 'PROMO.btnCreateSimpleFreeBet',
          icon: 'filter',
          color: 'primary',
          actionFn: () => console.log('action called successfully')
        }
      ]
    }
  }))
  .add('No actions', () => ({
    template,
    props: {
      title: 'Create Promotion',
      backUrl: '/',
      layout: 'separate',
      actions: []
    }
  }))
  .add('Confirmation action', () => ({
    template,
    props: {
      title: 'Edit Promotion "Demo Promotion"',
      backUrl: '/',
      layout: 'separate',
      actions: [
        {
          title: 'Stop Active Promo',
          icon: 'stop',
          iconRightMargin: '5',
          confirm: true,
          confirmText: 'You are trying to stop active promotion. Please confirm your action.',
          color: 'warn',
          actionFn: () => console.log('Promotion has been stopped sucessfully')
        }
      ]
    }
  }))
  .add('Action icon params', () => ({
    template,
    props: {
      title: 'MENU_SECTIONS.marketingPromotions',
      layout: 'separate',
      actions: [
        {
          title: 'PROMO.btnCreatePromo',
          icon: 'filter',
          iconRightMargin: '26',
          iconFontSize: '26',
          iconWidth: '26',
          iconHeight: '26',
          color: 'primary',
          actionFn: () => console.log('action called successfully')
        }
      ]
    }
  }));


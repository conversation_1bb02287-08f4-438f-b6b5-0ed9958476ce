import { AfterViewInit, Component, ElementRef, HostBinding, HostListener, Input, OnDestroy, ViewChild } from '@angular/core';
import { Subscription } from 'rxjs';

import { SwuiSidebarService } from './swui-sidebar.service';
import { SwuiMenuItem } from '../swui-menu/swui-menu.interface';
import { DomSanitizer, SafeStyle } from '@angular/platform-browser';

@Component({
    selector: 'lib-swui-sidebar',
    templateUrl: './swui-sidebar.component.html',
    styleUrls: ['./swui-sidebar.component.scss'],
    standalone: false
})
export class SwuiSidebarComponent implements AfterViewInit, OnDestroy {
  @ViewChild('sidebar') sidebarRef?: ElementRef;

  @Input() menuItems?: SwuiMenuItem[];
  @Input() activeColor?: string;
  @Input() topPadding = '0px';

  isCollapsed = false;
  isHovered = false;

  private _sidebarStateSubscription: Subscription | undefined;

  constructor( private sidebarStateService: SwuiSidebarService,
               private sanitizer: DomSanitizer ) {
    this.collapseOnDimension();
    this._sidebarStateSubscription = this.sidebarStateService.isCollapsed.subscribe(( val: boolean ) => {
      this.isCollapsed = val;
    });
  }

  @HostBinding('attr.style')
  public get valueAsStyle(): SafeStyle {
    return this.sanitizer.bypassSecurityTrustStyle(`--top-padding: ${this.topPadding}`);
  }

  @HostListener('window:resize') onResize() {
    this.collapseOnDimension();
  }

  @HostListener('document:click', ['$event.target'])
  public onClick( targetElement: HTMLElement ) {
    if (this.sidebarRef) {
      const clickedInside = this.sidebarRef.nativeElement.contains(targetElement);
      if (!clickedInside && window.innerWidth <= 1024) {
        this.sidebarStateService.isCollapsed.next(true);
      }
    }
  }

  ngAfterViewInit() {
    this.bindMouseenterEvent();
    this.bindMouseLeaveEvent();
  }

  ngOnDestroy() {
    if (this._sidebarStateSubscription) {
      this._sidebarStateSubscription.unsubscribe();
    }
  }

  toggleSidebar( event: MouseEvent ) {
    event.preventDefault();
    this.sidebarStateService.isCollapsed.next(!this.isCollapsed);
  }

  private bindMouseenterEvent() {
    if (this.sidebarRef) {
      this.sidebarRef.nativeElement.addEventListener('mouseenter', () => {
        if (this.isCollapsed) {
          this.isHovered = true;
        }
      });
    }
  }

  private bindMouseLeaveEvent() {
    if (this.sidebarRef) {
      this.sidebarRef.nativeElement.addEventListener('mouseleave', () => {
        this.isHovered = false;
      });
    }
  }

  private collapseOnDimension() {
    if (window.innerWidth <= 1024) {
      this.sidebarStateService.isCollapsed.next(true);
    }
  }

}

<div #sidebar class="sw-sidebar" [ngClass]="{'sw-sidebar--collapsed': isCollapsed, 'sw-sidebar--hovered': isHovered}">
  <div class="sw-sidebar__body">
    <div class="sw-sidebar__scroll">
      <lib-swui-menu
        [activeColor]="activeColor"
        [items]="menuItems"
        [isSidebarCollapsed]="isCollapsed"
        [isSidebarHovered]="isHovered">
      </lib-swui-menu>
      <ng-content></ng-content>
    </div>
  </div>
  <div class="sw-sidebar__toggle" matRipple (click)="toggleSidebar($event)">
    <mat-icon class="sw-sidebar__toggle-icon">{{ isCollapsed ? 'chevron_right' : 'chevron_left' }}</mat-icon>
  </div>
</div>

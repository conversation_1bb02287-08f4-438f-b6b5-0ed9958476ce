$topPadding: var(--top-padding);

.sw-sidebar {
  position: fixed;
  top: $topPadding;
  left: 0;
  z-index: 1005;
  display: flex;
  flex-direction: column;
  width: 250px;
  height: calc(100vh - #{$topPadding});
  background-color: #444761;
  transition: width 0.3s ease;
  overflow: hidden;
  border-top: 1px solid #484B69;

  &--collapsed {
    width: 54px;

    .sw-sidebar {
      &__header {
        padding: 0 4px;
      }
    }

    @media (max-width: 767px) {
      width: 0;
    }
  }

  &--hovered {
    width: 250px;

    .sw-sidebar__header {
      padding: 0 32px;
    }
  }

  &__body {
    height: 100%;
    padding: 0 0 24px;
    box-sizing: border-box;
  }

  &__scroll {
    height: 100%;
    overflow: auto;
    scrollbar-color: #444761 #2a2c44;

    &::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
      background-color: #1f212f;
    }

    &::-webkit-scrollbar {
      width: 10px;
      background-color: #1f212f;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #333758;
      border: 1px solid #555555;
    }
  }

  &__logo {
    display: flex;
    align-items: center;
    height: 100%;

    img {
      display: block;
      max-height: 100%;
      max-width: 100%;
      width: auto;
      height: auto;
    }
  }

  &__toggle {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: flex-end;
    padding: 0 16px;
    color: #939DB1;
    background-color: #2A2C44;
    cursor: pointer;
    user-select: none;
    transition: all 0.3s ease;
  }

  &__toggle-icon {
    width: 20px;
    margin-right: 2px;
  }
}


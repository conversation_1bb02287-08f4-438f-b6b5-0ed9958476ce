import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SwuiSidebarComponent } from './swui-sidebar.component';
import { SwuiSidebarService } from './swui-sidebar.service';


describe('SwuiSidebarComponent', () => {
  let component: SwuiSidebarComponent;
  let fixture: ComponentFixture<SwuiSidebarComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [SwuiSidebarComponent],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [SwuiSidebarService],
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiSidebarComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

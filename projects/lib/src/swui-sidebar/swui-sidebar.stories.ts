import { moduleMetadata, storiesOf } from '@storybook/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { APP_BASE_HREF } from '@angular/common';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

import { SwuiMenuModule } from '../swui-menu/swui-menu.module';
import { SwuiSidebarComponent } from './swui-sidebar.component';
import { SwuiSidebarModule } from './swui-sidebar.module';

export const MENU = [
  {
    title: 'Business management',
    icon: 'insert_emoticon',
    url: '/parent1',
    children: [
      {
        title: 'Business structure',
        icon: '',
        url: '/parent1/child1',
      },
      {
        title: 'Entity bulk actions',
        icon: '',
        url: '/parent1/child2',
      },
      {
        title: 'Menu child 3',
        icon: '',
        url: '/parent1/child3',
      }
    ]
  },
  {
    title: 'No children',
    icon: 'account_balance',
    url: '/parent2',
  },
  {
    title: 'Huge amount of childs',
    icon: 'face',
    url: '/parent3',
    children: [
      {
        title: 'Menu child 4',
        icon: 'icon-home4',
        url: '/parent3/child1',
      },
      {
        title: 'Menu child 4',
        icon: 'icon-home4',
        url: '/parent3/child2',
      },
      {
        title: 'Menu child 4',
        icon: 'icon-home4',
        url: '/parent3/child3',
      },
      {
        title: 'Menu child 4',
        icon: 'icon-home4',
        url: '/parent3/child4',
      },
      {
        title: 'Menu child 4',
        icon: 'icon-home4',
        url: '/parent3/child5',
      },
      {
        title: 'Menu child 4',
        icon: 'icon-home4',
        url: '/parent3/child6',
      },
      {
        title: 'Menu child 4',
        icon: 'icon-home4',
        url: '/parent3/child7',
      },
    ]
  },
  {
    title: 'One child',
    icon: 'extension',
    url: '/parent4',
    children: [
      {
        title: 'Business structure',
        icon: '',
        url: '/parent4/child1',
      },
    ]
  },
  {
    title: 'Atatata',
    icon: 'https',
    url: '/parent5',
    children: [
      {
        title: 'Business structure',
        icon: '',
        url: '/parent5/child1',
      },
      {
        title: 'Entity bulk actions',
        icon: '',
        url: '/parent5/child2',
      },
      {
        title: 'Menu child 3',
        icon: '',
        url: '/child3',
      }
    ]
  },
  {
    title: 'Settings',
    icon: 'settings',
    url: '/parent6',
    children: [
      {
        title: 'Business structure',
        icon: '',
        url: '/parent6/child1',
      },
      {
        title: 'Entity bulk actions',
        icon: '',
        url: '/parent6/child2',
      },
      {
        title: 'Menu child 3',
        icon: '',
        url: '/parent6/child3',
      }
    ]
  },
];

const props = {
  menuItems: MENU
};

storiesOf('Sidebar menu/sidebar', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        SwuiMenuModule,
        SwuiSidebarModule,
        TranslateModule.forRoot(),
        RouterModule.forRoot([
          {
            path: 'iframe.html',
            redirectTo: '',
            pathMatch: 'full'
          },
          {
            path: '',
            children: [
              {
                path: 'parent1',
                component: SwuiSidebarComponent,
                children: [
                  {
                    path: 'child1',
                    component: SwuiSidebarComponent,
                  },
                  {
                    path: 'child2',
                    component: SwuiSidebarComponent,
                  },
                  {
                    path: 'child3',
                    component: SwuiSidebarComponent,
                  }
                ]
              },
              {
                path: 'parent2',
                component: SwuiSidebarComponent,
              },
              {
                path: 'parent3',
                component: SwuiSidebarComponent,
                children: [
                  {
                    path: 'child1',
                    component: SwuiSidebarComponent,
                  },
                  {
                    path: 'child2',
                    component: SwuiSidebarComponent,
                  },
                  {
                    path: 'child3',
                    component: SwuiSidebarComponent,
                  },
                  {
                    path: 'child4',
                    component: SwuiSidebarComponent,
                  },
                  {
                    path: 'child5',
                    component: SwuiSidebarComponent,
                  },
                  {
                    path: 'child6',
                    component: SwuiSidebarComponent,
                  },
                  {
                    path: 'child7',
                    component: SwuiSidebarComponent,
                  },
                ]
              },
              {
                path: 'parent4',
                component: SwuiSidebarComponent,
                children: [
                  {
                    path: 'child1',
                    component: SwuiSidebarComponent,
                  },
                ]
              },
              {
                path: 'parent5',
                component: SwuiSidebarComponent,
                children: [
                  {
                    path: 'child1',
                    component: SwuiSidebarComponent,
                  },
                  {
                    path: 'child2',
                    component: SwuiSidebarComponent,
                  },
                  {
                    path: 'child3',
                    component: SwuiSidebarComponent,
                  }
                ]
              },
              {
                path: 'parent6',
                component: SwuiSidebarComponent,
                children: [
                  {
                    path: 'child1',
                    component: SwuiSidebarComponent,
                  },
                  {
                    path: 'child2',
                    component: SwuiSidebarComponent,
                  },
                  {
                    path: 'child3',
                    component: SwuiSidebarComponent,
                  }
                ]
              },
            ]
          }
        ])
      ],
      providers: [
        { provide: APP_BASE_HREF, useValue : '/' },
      ]
    })
  )
  .add('default', () => ({
    component: SwuiSidebarComponent,
    props
  }))
  .add('custom color', () => ({
    component: SwuiSidebarComponent,
    props: {
      ...props,
      activeColor: '#6499db'
    }
  }))
  .add('loading', () => ({
    component: SwuiSidebarComponent,
  }));

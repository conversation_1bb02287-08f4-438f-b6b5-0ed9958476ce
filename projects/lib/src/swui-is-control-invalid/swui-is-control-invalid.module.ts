import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SwuiIsControlInvalidDirective } from './swui-is-control-invalid.directive';
import { SwuiIsControlInvalidPipe } from './swui-is-control-invalid.pipe';

@NgModule({
  imports: [
    CommonModule,
  ],
  declarations: [
    SwuiIsControlInvalidDirective,
    SwuiIsControlInvalidPipe
  ],
  exports: [
    SwuiIsControlInvalidDirective,
    SwuiIsControlInvalidPipe
  ]
})
export class SwuiIsControlInvalidModule {
}

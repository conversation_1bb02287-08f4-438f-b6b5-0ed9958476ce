import { Pipe, PipeTransform } from '@angular/core';
import { AbstractControl } from '@angular/forms';
import { SwuiIsControlInvalidService } from './swui-is-control-invalid.service';

@Pipe({
    name: 'swIsControlInvalid',
    standalone: false
})
export class SwuiIsControlInvalidPipe implements PipeTransform {

  constructor( private readonly service: SwuiIsControlInvalidService ) {
  }

  transform( control?: AbstractControl ): boolean {
    return this.service.isControlInvalid(control);
  }
}

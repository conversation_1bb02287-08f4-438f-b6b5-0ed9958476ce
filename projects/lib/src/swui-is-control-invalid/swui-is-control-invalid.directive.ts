import { Directive, DoCheck, Input, TemplateRef, ViewContainerRef } from '@angular/core';
import { AbstractControl } from '@angular/forms';
import { SwuiIsControlInvalidService } from './swui-is-control-invalid.service';

@Directive({
    // eslint-disable-next-line @angular-eslint/directive-selector
    selector: '[swIsControlInvalid]',
    standalone: false
})
export class SwuiIsControlInvalidDirective implements DoCheck {
  @Input('swIsControlInvalid') control: AbstractControl | undefined;

  private hasView = false;

  constructor( private readonly service: SwuiIsControlInvalidService,
               private readonly templateRef: TemplateRef<any>,
               private readonly viewContainer: ViewContainerRef ) {
  }

  ngDoCheck(): void {
    if (this.control) {
      const invalid = this.service.isControlInvalid(this.control);
      if (invalid && !this.hasView) {
        this.viewContainer.createEmbeddedView(this.templateRef);
        this.hasView = true;
      } else if (!invalid && this.hasView) {
        this.viewContainer.clear();
        this.hasView = false;
      }
    }
  }
}

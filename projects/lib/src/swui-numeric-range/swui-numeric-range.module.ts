import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SwuiNumericRangeComponent } from './swui-numeric-range.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ReactiveFormsModule } from '@angular/forms';
import { SwuiControlMessagesModule } from '../swui-control-messages/swui-control-messages.module';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';

@NgModule({
  imports: [
    CommonModule,
    BrowserAnimationsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatButtonModule,
    SwuiControlMessagesModule
  ],
  declarations: [SwuiNumericRangeComponent],
  exports: [SwuiNumericRangeComponent]
})
export class SwuiNumericRangeModule {
}

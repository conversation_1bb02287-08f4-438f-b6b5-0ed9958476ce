import { moduleMetadata, storiesOf } from '@storybook/angular';
import { SwuiNumericRangeModule } from './swui-numeric-range.module';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';

storiesOf('Forms/Numeric Range', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        SwuiNumericRangeModule,
        MatFormFieldModule,
        MatInputModule,
        MatIconModule,
        BrowserAnimationsModule,
        ReactiveFormsModule
      ],
    })
  )
  .add('Empty', () => ({
    template: `<form [formGroup]="myGroup" >
      <lib-swui-numeric-range formGroupName="range" [options]="options"></lib-swui-numeric-range>
    </form>`,
    props: {
      options: {placeholder: ['FROM', 'TO'], enableCloseButton: true},
      myGroup: new FormGroup({
        range: new FormGroup({
          from: new FormControl(''),
          to: new FormControl(''),
        })
      })
    }
  }))
  .add('with set value', () => ({
    template: `<form [formGroup]="myGroup" >
      <lib-swui-numeric-range formGroupName="range" [options]="options"></lib-swui-numeric-range>
    </form>`,
    props: {
      options: {placeholder: ['FROM', 'TO'], enableCloseButton: true},
      myGroup: new FormGroup({
        range: new FormGroup({
          from: new FormControl(111),
          to: new FormControl(555),
        })
      })
    }
  }))
  .add('with set value and disable close button and validation', () => ({
    template: `<form [formGroup]="myGroup" >
      <lib-swui-numeric-range formGroupName="range" [options]="options"></lib-swui-numeric-range>
    </form>`,
    props: {
      options: {placeholder: ['FROM', 'TO'], enableCloseButton: [true, false]},
      myGroup: new FormGroup({
        range: new FormGroup({
          from: new FormControl(111, Validators.required),
          to: new FormControl(555),
        })
      })
    }
  }));

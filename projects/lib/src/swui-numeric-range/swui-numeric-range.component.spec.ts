import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { SwuiNumericRangeComponent } from './swui-numeric-range.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ControlContainer, FormGroupDirective, ReactiveFormsModule } from '@angular/forms';
import { SwuiControlMessagesModule } from '../swui-control-messages/swui-control-messages.module';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';

describe('SwuiNumericRangeComponent', () => {
  let component: SwuiNumericRangeComponent;
  let fixture: ComponentFixture<SwuiNumericRangeComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [SwuiNumericRangeComponent],
      imports: [
        BrowserAnimationsModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatIconModule,
        MatButtonModule,
        SwuiControlMessagesModule
      ],
      providers: [
        {
          provide: ControlContainer,
          useExisting: FormGroupDirective, multi: true
        }
      ]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiNumericRangeComponent);
    component = fixture.componentInstance;
    component.ngOnInit();
  });
});

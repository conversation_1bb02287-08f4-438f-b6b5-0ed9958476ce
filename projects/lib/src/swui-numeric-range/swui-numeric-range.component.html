<mat-form-field>
  <input matInput type="number" [placeholder]="fromPlaceholder" [formControl]="fromControl">
  <button mat-button *ngIf="fromIsCloseEnable && fromControl.value" matSuffix mat-icon-button (click)="fromControl.reset('')">
    <mat-icon>close</mat-icon>
  </button>
  <lib-swui-control-messages [control]="fromControl" [messages]="fromTransformFn"></lib-swui-control-messages>
</mat-form-field>

<mat-form-field>
  <input matInput type="number" [placeholder]="toPlaceholder" [formControl]="toControl">
  <button mat-button *ngIf="toIsCloseEnable && toControl.value" matSuffix mat-icon-button (click)="toControl.reset('')">
    <mat-icon>close</mat-icon>
  </button>
  <lib-swui-control-messages [control]="toControl" [messages]="toTransformFn"></lib-swui-control-messages>
</mat-form-field>

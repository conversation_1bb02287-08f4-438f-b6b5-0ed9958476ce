<form [formGroup]="form" class="chooser" [ngClass]="{'chooser--time-enabled': timePicker}">
  <lib-swui-mat-calendar
    class="chooser__calendar"
    [formControl]="dateControl"
    [isFromRange]="isFromRange"
    [isToRange]="isToRange"
    [fromDate]="fromDate"
    [toDate]="toDate"
    [minDate]="minDate"
    [maxDate]="maxDate"
    [time]="timeControl.value"
    [timeZone]="timeZone">
  </lib-swui-mat-calendar>
  <lib-swui-timepicker
    *ngIf="timePicker"
    class="chooser__time"
    [minTime]="minTime"
    [maxTime]="maxTime"
    [formControl]="timeControl"
    [timeDisableLevel]="timeDisableLevel">
  </lib-swui-timepicker>
</form>

import { Component, forwardRef, HostBinding, HostListener, Input, OnDestroy, OnInit } from '@angular/core';
import { ControlValueAccessor, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, NG_VALUE_ACCESSOR } from '@angular/forms';
import * as moment_ from 'moment';
import { BehaviorSubject, combineLatest, Subject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { SwuiTimepickerInterface, SwuiTimepickerTimeDisableLevel } from '../swui-timepicker/swui-timepicker.interface';

const moment = moment_;

interface DateTimeChooserForm {
  date: string;
  time: SwuiTimepickerInterface | null;
}

function transformValue( value: string, timeZone: string, ): DateTimeChooserForm {
  const date = timeZone ? moment.tz(value, timeZone) : moment.utc(value);
  if (date.isValid()) {
    return {
      date: date.toISOString(),
      time: {
        hour: date.hours(),
        minute: date.minutes(),
        second: date.seconds(),
      }
    };
  }

  return {
    date: '',
    time: null
  };
}

@Component({
    selector: 'lib-swui-date-time-chooser',
    templateUrl: './swui-date-time-chooser.component.html',
    styleUrls: ['./swui-date-time-chooser.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => SwuiDateTimeChooserComponent),
            multi: true
        },
    ],
    standalone: false
})

export class SwuiDateTimeChooserComponent implements ControlValueAccessor, OnInit, OnDestroy {
  @Input() set minDate(date: string) {
    this._minDate = date;
    this.setMinTime();
  }

  get minDate(): string {
    return this._minDate;
  }

  @Input() set maxDate(date: string) {
    this._maxDate = date;
    this.setMaxTime();
  }

  get maxDate(): string {
    return this._maxDate;
  }
  @Input() isFromRange = false;
  @Input() isToRange = false;
  @Input() fromDate = '';
  @Input() toDate = '';
  @Input() timeDisableLevel?: SwuiTimepickerTimeDisableLevel;
  @Input() chooseStart?: boolean;

  @Input()
  set timeZone( val: string ) {
    this._timeZone$.next(val);
  }

  get timeZone(): string {
    return this._timeZone$.value;
  }

  @Input()
  set timePicker( val: boolean ) {
    this._timePicker = !!val;
    if (!!val) {
      this.timeControl.enable();
    } else {
      this.timeControl.disable();
    }
  }

  get timePicker(): boolean {
    return this._timePicker;
  }

  @Input()
  set value( val: string ) {
    this._value$.next(val && moment.utc(val).isValid() ? val : '');
  }

  get value(): string {
    return this._value$.value;
  }

  form: UntypedFormGroup;
  isDisabled = false;

  @HostBinding('attr.tabindex')
  public tabindex = 0;

  onChange: ( _: any ) => void = (() => {
  });

  minTime = '';
  maxTime = '';
  private _minDate = '';
  private _maxDate = '';
  private _timePicker = false;
  private _timeZone$ = new BehaviorSubject<string>('');
  private _value$ = new BehaviorSubject<string>('');
  private _destroyed$ = new Subject();

  constructor( private fb: UntypedFormBuilder ) {
    this.form = this.initForm();

    this.dateControl.valueChanges
      .pipe(
        takeUntil(this._destroyed$),
        filter(() => this.isToRange && !this.timeControl.value)
      )
      .subscribe(value => {
        const timeZone = this._timeZone$.value;
        const date = timeZone ? moment.tz(value, timeZone) : moment.utc(value);
        const maxDate = this.maxDate && (timeZone ? moment.tz(this.maxDate, timeZone) : moment.utc(this.maxDate));

        if (maxDate && date.diff(maxDate, 'days') < 1) {
          this.timeControl.setValue({
            hour: this.chooseStart || this.timeDisableLevel?.hour ? 0 : maxDate.hours(),
            minute: this.chooseStart || this.timeDisableLevel?.minute ? 0 : maxDate.minutes(),
            second: this.chooseStart || this.timeDisableLevel?.second ? 0 : maxDate.seconds(),
          });

          return;
        }

        const time = this.chooseStart
          ? {
            hour: 0,
            minute: 0,
            second: 0,
          }
          : {
            hour: 23,
            minute: 59,
            second: 59,
          };

        this.timeControl.setValue(time);
      });
  }

  @HostListener('blur') onblur() {
    this.onTouched();
  }

  onTouched: any = () => {
  };

  ngOnInit(): void {
    combineLatest([this._timeZone$, this._value$])
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe(( [timezone, value] ) => {
        this.form.patchValue(transformValue(value, timezone));
      });

    this.form.valueChanges
      .pipe(
        takeUntil(this._destroyed$)
      )
      .subscribe(( val: DateTimeChooserForm ) => {
        const { date, time } = val;
        const processedDate = this._timeZone$.value ? moment.tz(date, this._timeZone$.value) : moment.utc(date);
        if (time) {
          processedDate.set(time);
        }
        this.setMinTime();
        this.setMaxTime();
        this.onChange(processedDate.toISOString());
      });
  }

  ngOnDestroy(): void {
    this._destroyed$.next(undefined);
    this._destroyed$.complete();
  }

  writeValue( val: string ): void {
    this._value$.next(val && moment.utc(val).isValid() ? val : '');
  }

  registerOnChange( fn: ( _: any ) => void ): void {
    this.onChange = fn;
  }

  registerOnTouched( fn: any ): void {
    this.onTouched = fn;
  }

  setDisabledState( disabled: boolean ) {
    this.isDisabled = !!disabled;
    if (!!disabled) {
      this.form.disable();
    } else {
      this.form.enable();
    }
  }

  get dateControl(): UntypedFormControl {
    return this.form.get('date') as UntypedFormControl;
  }

  get timeControl(): UntypedFormControl {
    return this.form.get('time') as UntypedFormControl;
  }

  private initForm(): UntypedFormGroup {
    return this.fb.group({
      date: [],
      time: []
    });
  }

  private setMinTime() {
    if (!this.minDate) {
      return;
    }

    const minDate = this.timeZone ? moment.tz(this.minDate, this.timeZone) : moment.utc(this.minDate);
    const date = this.timeZone ? moment.tz(this.dateControl.value, this.timeZone) : moment.utc(this.dateControl.value);

    if (date.diff(minDate, 'days') === 0 && date.date() === minDate.date()) {
      this.minTime = `${minDate.hours()}:${minDate.minutes()}:${minDate.seconds()}`;
    } else {
      this.minTime = '';
    }
  }

  private setMaxTime() {
    if (!this.maxDate) {
      return;
    }

    const maxDate = this.timeZone ? moment.tz(this.maxDate, this.timeZone) : moment.utc(this.maxDate);
    const date = this.timeZone ? moment.tz(this.dateControl.value, this.timeZone) : moment.utc(this.dateControl.value);

    if (date.diff(maxDate, 'days') === 0 && date.date() === maxDate.date()) {
      this.maxTime = `${maxDate.hours()}:${maxDate.minutes()}:${maxDate.seconds()}`;
    } else {
      this.maxTime = '';
    }
  }
}

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ReactiveFormsModule } from '@angular/forms';
import { SwuiDateTimeChooserComponent } from './swui-date-time-chooser.component';
import { SwuiMatCalendarModule } from '../swui-mat-calendar/swui-mat-calendar.module';
import { SwuiTimepickerModule } from '../swui-timepicker/swui-timepicker.module';


export const DATE_TIME_CHOOSER_MODULES = [
  ReactiveFormsModule,
  SwuiMatCalendarModule,
  SwuiTimepickerModule,
];

@NgModule({
  declarations: [SwuiDateTimeChooserComponent],
  imports: [
    CommonModule,
    ...DATE_TIME_CHOOSER_MODULES,
  ],
  exports: [
    SwuiDateTimeChooserComponent,
  ]
})

export class SwuiDateTimeChooserModule {
}

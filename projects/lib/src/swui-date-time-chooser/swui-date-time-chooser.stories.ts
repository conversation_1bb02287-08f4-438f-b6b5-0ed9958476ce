import { FormsModule } from '@angular/forms';
import { moduleMetadata, storiesOf } from '@storybook/angular';
import { MatCardModule } from '@angular/material/card';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { DATE_TIME_CHOOSER_MODULES, SwuiDateTimeChooserModule } from './swui-date-time-chooser.module';
import { SwuiDateTimeChooserComponent } from './swui-date-time-chooser.component';


const template = `
  <mat-card style="margin: 32px">
    <lib-swui-date-time-chooser
      [disabled]="disabled"
      [timeZone]="timeZone"
      [maxDate]="maxDate"
      [minDate]="minDate"
      [timePicker]="timePicker"
      [timeDisableLevel]="timeDisableLevel"
      [(ngModel)]="value">
    </lib-swui-date-time-chooser>
  </mat-card>
`;

storiesOf('Date/DateTimeChooser', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        SwuiDateTimeChooserModule,
        ...DATE_TIME_CHOOSER_MODULES,
        MatCardModule,
        FormsModule
      ],
    })
  )
  .add('default', () => ({
    component: SwuiDateTimeChooserComponent,
    template,
    props: {
    },
  }))
  .add('timeZone +13', () => ({
    component: SwuiDateTimeChooserComponent,
    template,
    props: {
      timeZone: 'Pacific/Tongatapu',
      value: '2020-07-03T00:00:00.000Z'
    },
  }))
  .add('timepicker +13', () => ({
    component: SwuiDateTimeChooserComponent,
    template,
    props: {
      timePicker: true,
      timeZone: 'Pacific/Tongatapu',
      value: '2020-07-03T00:00:00.000Z'
    },
  }))
  .add('timeDisableLevel seconds', () => ({
    component: SwuiDateTimeChooserComponent,
    template,
    props: {
      timePicker: true,
      timeDisableLevel: { second: false },
      value: '2020-07-03T00:00:10.000Z'
    },
  }))
  .add('disabled', () => ({
    component: SwuiDateTimeChooserComponent,
    template,
    props: {
      timePicker: true,
      disabled: true,
      value: '2020-07-03T00:00:00.000Z'
    },
  }));


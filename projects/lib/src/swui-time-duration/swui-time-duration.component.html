<form [formGroup]="form" class="sw-time"
      [ngClass]="{
      'seconds-disabled': secondsDisabled,
      'days-disabled': daysDisabled
      }">
  <ng-container  *ngIf="!daysDisabled">
    <input
      #days
      type="text"
      matInput
      formControlName="days"
      placeholder="00"
      class="sw-time__input sw-time__input--days">
    <div class="sw-time__separ">:</div>
  </ng-container>
  <input
    #hours
    type="text"
    matInput
    formControlName="hours"
    placeholder="00"
    class="sw-time__input sw-time__input--hours">
  <div class="sw-time__separ">:</div>
  <input
    #minutes
    type="text"
    matInput
    formControlName="minutes"
    placeholder="00"
    class="sw-time__input">
  <div class="sw-time__separ" *ngIf="!secondsDisabled">:</div>
  <input
    #seconds
    type="text"
    *ngIf="!secondsDisabled"
    formControlName="seconds"
    placeholder="00"
    class="sw-time__input">
</form>

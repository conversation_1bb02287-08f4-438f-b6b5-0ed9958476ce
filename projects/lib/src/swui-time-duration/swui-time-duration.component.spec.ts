import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { SwuiTimeDurationComponent } from './swui-time-duration.component';
import { CommonModule } from '@angular/common';
import { UntypedFormControl, ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';
import { DebugElement } from '@angular/core';
import { MatInputModule } from '@angular/material/input';

function createFakeEvent( type: string ) {
  const event = document.createEvent('Event');
  event.initEvent(type, true, true);
  return event;
}

function dispatchFakeEvent( node: Node | Window, type: string ) {
  node.dispatchEvent(createFakeEvent(type));
}

describe('SwuiTimeDurationComponent', () => {
  let component: SwuiTimeDurationComponent;
  let fixture: ComponentFixture<SwuiTimeDurationComponent>;
  let testTime: number;
  let host: DebugElement;
  let daysControl: UntypedFormControl;
  let hoursControl: UntypedFormControl;
  let minutesControl: UntypedFormControl;
  let secondsControl: UntypedFormControl;
  let formTestValue: Object;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [
        CommonModule,
        BrowserAnimationsModule,
        ReactiveFormsModule,
        MatInputModule,
      ],
      declarations: [SwuiTimeDurationComponent]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SwuiTimeDurationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    host = fixture.debugElement;
    testTime = 1.296e+8;
    formTestValue = { days: '0', hours: '0', minutes: '1', seconds: '10' };
    daysControl = component.form.get('days') as UntypedFormControl;
    hoursControl = component.form.get('hours') as UntypedFormControl;
    minutesControl = component.form.get('minutes') as UntypedFormControl;
    secondsControl = component.form.get('seconds') as UntypedFormControl;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set value', () => {
    component.value = testTime;
    expect(component.value).toBe(coerceNumberProperty(testTime, 0));
  });

  it('should set required', () => {
    component.required = true;
    expect(component.required).toBe(coerceBooleanProperty(true));
  });

  it('should set disabled', () => {
    component.disabled = true;
    expect(component.disabled).toBe(coerceBooleanProperty(true));
  });

  it('should set enabled', () => {
    component.disabled = false;
    expect(component.disabled).toBe(coerceBooleanProperty(false));
  });

  it('should disable inner form on disabled', () => {
    component.disabled = true;
    expect(component.form.disabled).toBe(true);
  });

  it('should enable inner form on disabled', () => {
    component.disabled = false;
    expect(component.form.disabled).toBe(false);
  });

  it('should disable seconds', () => {
    component.secondsDisabled = true;
    expect(secondsControl.disabled).toBe(true);
  });

  it('should set placeholder', () => {
    component.placeholder = 'test';
    expect(component.placeholder).toBe('test');
  });

  it('should get empty false if controls are null', () => {
    component.value = null;
    expect(component.empty).toBe(false);
  });

  it('should get empty false if controls are equals 0', () => {
    component.value = 0;
    expect(component.empty).toBe(false);
  });

  it('should get empty false if controls are not empty', () => {
    component.value = testTime;
    expect(component.empty).toBe(false);
  });

  it('should get error state false', () => {
    expect(component.errorState).toBeFalsy();
  });

  it('should set state changes', () => {
    const nextSpy = spyOn(component.stateChanges, 'next');
    component.required = true;
    expect(nextSpy).toHaveBeenCalled();
  });

  it('should controlType to be defined', () => {
    expect(component.controlType).toBe('lib-swui-time-duration');
  });

  it('should set host id', () => {
    expect(host.nativeElement.getAttribute('id')).toBeDefined();
  });

  it('should shouldLabelFloat to be true when not empty', () => {
    component.value = testTime;
    expect(component.shouldLabelFloat).toBe(true);
  });

  it('should shouldLabelFloat to be true when host focused', () => {
    dispatchFakeEvent(host.nativeElement, 'focus');
    expect(component.shouldLabelFloat).toBe(true);
  });

  it('should shouldLabelFloat return true', () => {
    expect(component.shouldLabelFloat).toBe(true);
  });

  it('should set host class floating when it is not empty', () => {
    component.value = testTime;
    fixture.detectChanges();
    expect(host.nativeElement.classList.contains('floating')).toBe(true);
  });

  it('should set host class floating when host focused', () => {
    dispatchFakeEvent(host.nativeElement, 'focus');
    fixture.detectChanges();
    expect(host.nativeElement.classList.contains('floating')).toBe(true);
  });

  it('should set aria-describedby on host', () => {
    expect(host.nativeElement.getAttribute('aria-describedby')).toBe('');
  });

  it('should call onTouched on focus', () => {
    spyOn(component, 'onTouched');
    dispatchFakeEvent(host.nativeElement, 'focus');
    dispatchFakeEvent(host.nativeElement, 'blur');
    expect(component.onTouched).toHaveBeenCalled();
  });

  it('should init form', () => {
    expect(component.form).toBeDefined();
  });

  it('should call onChange onInit when form value changed', () => {
    spyOn(component, 'onChange');
    component.form.setValue(formTestValue);
    expect(component.onChange).toHaveBeenCalled();
  });

  it('should set new value when form value changed', () => {
    component.form.setValue(formTestValue);
    expect(component.value).toBe(70000);
  });

  it('should complete state change on destroy', () => {
    const completeSpy = spyOn(component.stateChanges, 'complete');
    component.ngOnDestroy();
    expect(completeSpy).toHaveBeenCalled();
  });

  it('should setDescribedByIds', () => {
    const testIds = ['test1', 'test2'];
    component.setDescribedByIds(testIds);
    expect(component.describedBy).toBe(testIds.join(' '));
  });

  it('patchform on writevalue', () => {
    component.writeValue(testTime);
    expect(component.value).toBe(testTime);
  });

  it('should set onChange in registerOnChange', () => {
    let test = false;
    const fn = () => {
      test = true;
    };
    component.registerOnChange(fn);
    component.writeValue(testTime);
    expect(test).toBe(true);
  });

  it('should set onChange in registerOnTouched', () => {
    let test = false;
    const fn = () => {
      test = true;
    };
    component.registerOnTouched(fn);
    dispatchFakeEvent(host.nativeElement, 'focus');
    dispatchFakeEvent(host.nativeElement, 'blur');
    expect(test).toBe(true);
  });

  it('should disable form', () => {
    component.setDisabledState(true);
    expect(component.form.disabled).toBe(true);
  });

  it('should enable form', () => {
    component.setDisabledState(false);
    expect(component.form.disabled).toBe(false);
  });

  it('should not cut last symbol if days value.length > 2 && control !== hours && firstSymbol !== 0', () => {
    daysControl.setValue('123');
    fixture.detectChanges();
    component.processInputValue();
    expect(daysControl.value).toBe('123');
  });

  it('should set 00 if minutes euqals to 60', () => {
    minutesControl.setValue('60');
    fixture.detectChanges();
    component.processInputValue();
    expect(minutesControl.value).toBe('00');
  });

  it('should convert 66 minutes to 01 hour and 06 minutes', () => {
    minutesControl.setValue('66');
    fixture.detectChanges();
    component.processInputValue();
    expect(hoursControl.value).toBe('01');
    expect(minutesControl.value).toBe('06');
  });

  it('should cut first symbol 0 if first symbol = 0  && value.length > 2', () => {
    minutesControl.setValue('012');
    fixture.detectChanges();
    component.processInputValue();
    expect(minutesControl.value).toBe('12');
  });

  it('should take a full hour and leave a minutes', () => {
    minutesControl.setValue('066');
    fixture.detectChanges();
    component.processInputValue();
    expect(minutesControl.value).toBe('06');
  });

  it('should count the hours to days and hours', () => {
    hoursControl.setValue('066');
    fixture.detectChanges();
    component.processInputValue();
    expect(daysControl.value).toBe('02');
    expect(hoursControl.value).toBe('18');
  });

  it('should set max hours value if value > maxHoursValue && el === hours', () => {
    hoursControl.setValue('24');
    fixture.detectChanges();
    component.processInputValue();
    expect(hoursControl.value).toBe('00');
  });

  it('should set empty string if value is not digits', () => {
    minutesControl.setValue('string value');
    fixture.detectChanges();
    component.processInputValue();
    expect(minutesControl.value).toBe('00');
  });

  it('should set 0 value instead of negative', () => {
    minutesControl.setValue('-20');
    fixture.detectChanges();
    component.processInputValue();
    expect(minutesControl.value).toBe('00');
  });

  it('should not set valueAccessor if form control', () => {
    (fixture.componentInstance as any).ngControl = new UntypedFormControl(testTime);
    expect(component.ngControl.valueAccessor).toBeUndefined();
  });

});

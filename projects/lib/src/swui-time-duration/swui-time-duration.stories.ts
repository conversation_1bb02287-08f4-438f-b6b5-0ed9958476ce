import { moduleMetadata, storiesOf } from '@storybook/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MatFormFieldModule } from '@angular/material/form-field';

import { SwuiTimeDurationModule } from './swui-time-duration.module';
import { SwuiTimeDurationComponent } from './swui-time-duration.component';


storiesOf('Date/TimeDuration', module)
  .addDecorator(
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        SwuiTimeDurationModule,
        MatFormFieldModule,
      ],
    })
  )
  .add('default', () => ({
    component: SwuiTimeDurationComponent,
    template: '<lib-swui-time-duration [value]="1.296e+8"></lib-swui-time-duration>',
    props: {
      dimension: 'm',
    },
  }))
  .add('ngModel', () => ({
    component: SwuiTimeDurationComponent,
    template: '<lib-swui-time-duration [ngModel]="121000"></lib-swui-time-duration>',
    props: {
      dimension: 'm',
    },
  }))
  .add('mat-form-field', () => ({
    template: `
      <mat-form-field appearance="outline">
        <mat-label>Time</mat-label>
        <lib-swui-time-duration></lib-swui-time-duration>
      </mat-form-field>
    `,
    component: SwuiTimeDurationComponent,
  }))
  .add('days disabled', () => ({
    template: `
      <mat-form-field appearance="outline">
        <mat-label>Time</mat-label>
        <lib-swui-time-duration [daysDisabled]="true"></lib-swui-time-duration>
      </mat-form-field>
    `,
    component: SwuiTimeDurationComponent,
  }))
  .add('seconds disabled', () => ({
    template: `
      <mat-form-field appearance="outline">
        <mat-label>Time</mat-label>
        <lib-swui-time-duration [secondsDisabled]="true"></lib-swui-time-duration>
      </mat-form-field>
    `,
    component: SwuiTimeDurationComponent,
  }))
  .add('days and seconds disabled', () => ({
    template: `
      <mat-form-field appearance="outline">
        <mat-label>Time</mat-label>
        <lib-swui-time-duration [secondsDisabled]="true" [daysDisabled]="true"></lib-swui-time-duration>
      </mat-form-field>
    `,
    component: SwuiTimeDurationComponent,
  }))
  .add('value', () => ({
    template: `
      <mat-form-field appearance="outline">
        <mat-label>Time</mat-label>
        <lib-swui-time-duration [value]="90061000"></lib-swui-time-duration>
      </mat-form-field>
    `,
    component: SwuiTimeDurationComponent,
  }))
  .add('value && seconds disabled', () => ({
    template: `
      <mat-form-field appearance="outline">
        <mat-label>Time</mat-label>
        <lib-swui-time-duration [value]="90061000" [secondsDisabled]="true"></lib-swui-time-duration>
      </mat-form-field>
    `,
    component: SwuiTimeDurationComponent,
  }))
  .add('disabled', () => ({
    template: `
      <mat-form-field appearance="outline">
        <mat-label>Time</mat-label>
        <lib-swui-time-duration disabled [value]="123123" [secondsDisabled]="true"></lib-swui-time-duration>
      </mat-form-field>
    `,
    component: SwuiTimeDurationComponent,
  }))
  .add('required', () => ({
    template: `
      <mat-form-field appearance="outline">
        <mat-label>Time</mat-label>
        <lib-swui-time-duration required [value]="123123" [secondsDisabled]="true"></lib-swui-time-duration>
      </mat-form-field>
    `,
    component: SwuiTimeDurationComponent,
  }));

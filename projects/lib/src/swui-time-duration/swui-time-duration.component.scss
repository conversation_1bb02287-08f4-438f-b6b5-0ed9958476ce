.sw-time {
  position: relative;
  display: flex;
  align-items: center;
  &__input {
    border: none;
    background: none;
    padding: 0;
    outline: none;
    font: inherit;
    text-align: center;
    width: calc((100% - 15px) / 4);
    &::placeholder {
      color: rgba(0, 0, 0, .6);
    }

    &:-ms-input-placeholder {
      color: rgba(0, 0, 0, .6);
    }

    &::-ms-input-placeholder {
      color: rgba(0, 0, 0, .6);
    }
  }
  &__separ {
    opacity: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 5px;
  }
  &.seconds-disabled {
    .sw-time {
      &__input {
        width: calc((100% - 10px) / 3);
      }
    }
  }
  &.days-disabled {
    .sw-time {
      &__input {
        width: calc((100% - 10px) / 3);
      }
    }
    &.seconds-disabled {
      .sw-time {
        &__input {
          width: calc((100% - 10px) / 2);
        }
      }
    }
  }
}
:host.floating {
  div {
    opacity: 1;
    transition: opacity 0.1s ease-in-out;
  }
}

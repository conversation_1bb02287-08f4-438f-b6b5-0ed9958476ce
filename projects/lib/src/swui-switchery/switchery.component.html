<div class="checkbox checkbox-right checkbox-switchery" [ngClass]="{'disabled':isDisabled}"
     *ngIf="!inline; else inlineRendering"
     (click)="toggleChecked($event)">
  <label class="switchery-label">
    <input type="checkbox" class="switchery" [checked]="checked" style="display: none">
    <span class="switchery switchery-default" [ngClass]="checkedClass()"><small></small></span>
    <span *ngIf="actualPrefix()" class="mr-10">{{ actualPrefix() }}</span>
  </label>
</div>

<ng-template #inlineRendering>
  <input type="checkbox" class="switchery" [checked]="checked" style="display: none">
  <span class="switchery switchery-default" style="left: 15px" [ngClass]="checkedClass()"><small></small></span>
  <span *ngIf="actualPrefix()" class="mr-10">{{ actualPrefix() }}</span>
</ng-template>

:host {
  display: inline-block;
}

span.switchery {
  box-shadow: rgb(223, 223, 223) 0 0 0 0 inset;
  border-color: rgb(223, 223, 223);
  background-color: rgb(255, 255, 255);
  transition: border 0.4s, box-shadow 0.4s, background-color 1.2s;

  & > small {
    left: 0;
    background-color: rgb(255, 255, 255);
    transition: background-color 0.4s, left 0.2s;
  }

  &.checked {
    background-color: rgb(100, 189, 99);
    border-color: rgb(100, 189, 99);
    box-shadow: rgb(100, 189, 99) 0 0 0 12px inset;

    &.disabled {
      opacity: 0.5;
    }

    & > small {
      left: 22px
    }
  }
}

.checkbox {
  &.checkbox-switchery {
    height: 26px;
    margin: 0;

    &.checkbox-right {
      label {
        min-height: 26px;
        padding-right: 46px;
      }
    }
  }
}

{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "../../out-tsc/lib", "declaration": true, "declarationMap": true, "inlineSources": true, "types": [], "lib": ["dom", "es2018"]}, "angularCompilerOptions": {"skipTemplateCodegen": true, "strictMetadataEmit": true, "enableResourceInlining": true, "annotateForClosureCompiler": true, "fullTemplateTypeCheck": true, "strictInjectionParameters": true}, "exclude": ["src/test.ts", "**/*.spec.ts", "**/*.stories.*"]}
{"extends": "../../.eslintrc.json", "ignorePatterns": ["!**/*", "**/*.stories.ts", "**/.storybook/**/*.ts"], "overrides": [{"files": ["*.ts"], "parserOptions": {"project": ["./tsconfig.lib.json", "./tsconfig.spec.json"], "createDefaultProgram": true}, "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "lib<PERSON><PERSON><PERSON>", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "lib-swui", "style": "kebab-case"}]}}, {"files": ["*.html"], "rules": {}}]}